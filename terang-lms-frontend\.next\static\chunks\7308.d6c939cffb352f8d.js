try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="5785f9c3-d1b4-4790-9b2c-eebf25aeff12",e._sentryDebugIdIdentifier="sentry-dbid-5785f9c3-d1b4-4790-9b2c-eebf25aeff12")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7308],{87308:(e,t,n)=>{function r(e,t,n){return t(n),n(e,t)}n.r(t),n.d(t,{elm:()=>y});var i=/[a-z]/,f=/[A-Z]/,o=/[a-zA-Z0-9_]/,a=/[0-9]/,u=/[0-9A-Fa-f]/,s=/[-&*+.\\/<>=?^|:]/,l=/[(),[\]{}]/,c=/[ \v\f]/;function d(){return function(e,t){if(e.eatWhile(c))return null;var n=e.next();if(l.test(n))return"{"===n&&e.eat("-")?r(e,t,function e(t){return 0==t?d():function(n,r){for(;!n.eol();){var i=n.next();if("{"==i&&n.eat("-"))++t;else if("-"==i&&n.eat("}")&&0==--t)return r(d()),"comment"}return r(e(t)),"comment"}}(1)):"["===n&&e.match("glsl|")?r(e,t,h):"builtin";if("'"===n)return r(e,t,k);if('"'===n)return e.eat('"')?e.eat('"')?r(e,t,b):"string":r(e,t,p);if(f.test(n))return e.eatWhile(o),"type";if(i.test(n)){var g=1===e.pos;return e.eatWhile(o),g?"def":"variable"}if(a.test(n)){if("0"===n){if(e.eat(/[xX]/))return e.eatWhile(u),"number"}else e.eatWhile(a);return e.eat(".")&&e.eatWhile(a),e.eat(/[eE]/)&&(e.eat(/[-+]/),e.eatWhile(a)),"number"}return s.test(n)?"-"===n&&e.eat("-")?(e.skipToEnd(),"comment"):(e.eatWhile(s),"keyword"):"_"===n?"keyword":"error"}}function b(e,t){for(;!e.eol();)if('"'===e.next()&&e.eat('"')&&e.eat('"')){t(d());break}return"string"}function p(e,t){for(;e.skipTo('\\"');)e.next(),e.next();return e.skipTo('"')?(e.next(),t(d()),"string"):(e.skipToEnd(),t(d()),"error")}function k(e,t){for(;e.skipTo("\\'");)e.next(),e.next();return e.skipTo("'")?(e.next(),t(d()),"string"):(e.skipToEnd(),t(d()),"error")}function h(e,t){for(;!e.eol();)if("|"===e.next()&&e.eat("]")){t(d());break}return"string"}var g={case:1,of:1,as:1,if:1,then:1,else:1,let:1,in:1,type:1,alias:1,module:1,where:1,import:1,exposing:1,port:1};let y={name:"elm",startState:function(){return{f:d()}},copyState:function(e){return{f:e.f}},token:function(e,t){var n=t.f(e,function(e){t.f=e}),r=e.current();return g.hasOwnProperty(r)?"keyword":n},languageData:{commentTokens:{line:"--"}}}}}]);