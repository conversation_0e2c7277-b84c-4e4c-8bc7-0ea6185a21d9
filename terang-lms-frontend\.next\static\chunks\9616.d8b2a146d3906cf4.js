try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c0fd897f-a7b7-473f-b53d-02f8aad14c5e",e._sentryDebugIdIdentifier="sentry-dbid-c0fd897f-a7b7-473f-b53d-02f8aad14c5e")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9616],{49616:(e,t,r)=>{r.r(t),r.d(t,{tiddlyWiki:()=>x});var n={},i={allTags:!0,closeAll:!0,list:!0,newJournal:!0,newTiddler:!0,permaview:!0,saveChanges:!0,search:!0,slider:!0,tabs:!0,tag:!0,tagging:!0,tags:!0,tiddler:!0,timeline:!0,today:!0,version:!0,option:!0,with:!0,filter:!0},a=/[\w_\-]/i,f=/^\-\-\-\-+$/,u=/^\/\*\*\*$/,o=/^\*\*\*\/$/,l=/^<<<$/,c=/^\/\/\{\{\{$/,s=/^\/\/\}\}\}$/,d=/^<!--\{\{\{-->$/,k=/^<!--\}\}\}-->$/,m=/^\{\{\{$/,h=/^\}\}\}$/,b=/.*?\}\}\}/;function p(e,t,r){return t.tokenize=r,r(e,t)}function g(e,t){var r=e.sol(),i=e.peek();if(t.block=!1,r&&/[<\/\*{}\-]/.test(i)){if(e.match(m))return t.block=!0,p(e,t,_);if(e.match(l))return"quote";if(e.match(u)||e.match(o)||e.match(c)||e.match(s)||e.match(d)||e.match(k))return"comment";if(e.match(f))return"contentSeparator"}if(e.next(),r&&/[\/\*!#;:>|]/.test(i)){if("!"==i)return e.skipToEnd(),"header";if("*"==i)return e.eatWhile("*"),"comment";if("#"==i)return e.eatWhile("#"),"comment";if(";"==i)return e.eatWhile(";"),"comment";if(":"==i)return e.eatWhile(":"),"comment";if(">"==i)return e.eatWhile(">"),"quote";if("|"==i)return"header"}if("{"==i&&e.match("{{"))return p(e,t,_);if(/[hf]/i.test(i)&&/[ti]/i.test(e.peek())&&e.match(/\b(ttps?|tp|ile):\/\/[\-A-Z0-9+&@#\/%?=~_|$!:,.;]*[A-Z0-9+&@#\/%=~_|$]/i))return"link";if('"'==i)return"string";if("~"==i||/[\[\]]/.test(i)&&e.match(i))return"brace";if("@"==i)return e.eatWhile(a),"link";if(/\d/.test(i))return e.eatWhile(/\d/),"number";if("/"==i){if(e.eat("%"))return p(e,t,w);else if(e.eat("/"))return p(e,t,$)}if("_"==i&&e.eat("_"))return p(e,t,v);if("-"==i&&e.eat("-")){if(" "!=e.peek())return p(e,t,z);if(" "==e.peek())return"brace"}return"'"==i&&e.eat("'")?p(e,t,y):"<"==i&&e.eat("<")?p(e,t,W):(e.eatWhile(/[\w\$_]/),n.propertyIsEnumerable(e.current())?"keyword":null)}function w(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=g;break}n="%"==r}return"comment"}function y(e,t){for(var r,n=!1;r=e.next();){if("'"==r&&n){t.tokenize=g;break}n="'"==r}return"strong"}function _(e,t){var r=t.block;return r&&e.current()||(!r&&e.match(b)||r&&e.sol()&&e.match(h)?t.tokenize=g:e.next()),"comment"}function $(e,t){for(var r,n=!1;r=e.next();){if("/"==r&&n){t.tokenize=g;break}n="/"==r}return"emphasis"}function v(e,t){for(var r,n=!1;r=e.next();){if("_"==r&&n){t.tokenize=g;break}n="_"==r}return"link"}function z(e,t){for(var r,n=!1;r=e.next();){if("-"==r&&n){t.tokenize=g;break}n="-"==r}return"deleted"}function W(e,t){if("<<"==e.current())return"meta";var r=e.next();return r?">"==r&&">"==e.peek()?(e.next(),t.tokenize=g,"meta"):(e.eatWhile(/[\w\$_]/),i.propertyIsEnumerable(e.current())?"keyword":null):(t.tokenize=g,null)}let x={name:"tiddlywiki",startState:function(){return{tokenize:g}},token:function(e,t){return e.eatSpace()?null:t.tokenize(e,t)}}}}]);