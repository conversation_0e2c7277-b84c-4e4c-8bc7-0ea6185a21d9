{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|static|.*\\..*|_static|_vercel).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "nlo92T4FmNAGG3aAH77SL", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "0c391521cb1bf7f635ecfa2bec8c2c61", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "329b5a6f8209a046c1cab214c4342b336df3e6f7f591ad3e038020c13bf7f677", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "21fdc98ce3b29b66fdbb6576db2fdf087cd44494b5e8c465b1fdbb0c25d0bc89"}}}, "functions": {}, "sortedMiddleware": ["/"]}