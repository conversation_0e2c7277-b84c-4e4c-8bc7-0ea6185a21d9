try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="79c79274-77a0-4640-bfd0-8497008ba08a",e._sentryDebugIdIdentifier="sentry-dbid-79c79274-77a0-4640-bfd0-8497008ba08a")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1412],{11412:(e,t,n)=>{function r(e){for(var t={},n=0,r=e.length;n<r;++n)t[e[n]]=!0;return t}n.r(t),n.d(t,{ruby:()=>h});var a,i=["alias","and","BEGIN","begin","break","case","class","def","defined?","do","else","elsif","END","end","ensure","false","for","if","in","module","next","not","or","redo","rescue","retry","return","self","super","then","true","undef","unless","until","when","while","yield","nil","raise","throw","catch","fail","loop","callcc","caller","lambda","proc","public","protected","private","require","load","require_relative","extend","autoload","__END__","__FILE__","__LINE__","__dir__"],o=r(i),u=r(["def","class","case","for","while","until","module","catch","loop","proc","begin"]),l=r(["end","until"]),s={"[":"]","{":"}","(":")"},f={"]":"[","}":"{",")":"("};function d(e,t,n){return n.tokenize.push(e),e(t,n)}function c(e,t){if(e.sol()&&e.match("=begin")&&e.eol())return t.tokenize.push(k),"comment";if(e.eatSpace())return null;var n,r,i,o=e.next();if("`"==o||"'"==o||'"'==o)return d(p(o,"string",'"'==o||"`"==o),e,t);if("/"==o)if(function(e){for(var t,n=e.pos,r=0,a=!1,i=!1;null!=(t=e.next());)if(i)i=!1;else{if("[{(".indexOf(t)>-1)r++;else if("]})".indexOf(t)>-1){if(--r<0)break}else if("/"==t&&0==r){a=!0;break}i="\\"==t}return e.backUp(e.pos-n),a}(e))return d(p(o,"string.special",!0),e,t);else return"operator";if("%"==o){var u="string",l=!0;e.eat("s")?u="atom":e.eat(/[WQ]/)?u="string":e.eat(/[r]/)?u="string.special":e.eat(/[wxq]/)&&(u="string",l=!1);var f=e.eat(/[^\w\s=]/);return f?(s.propertyIsEnumerable(f)&&(f=s[f]),d(p(f,u,l,!0),e,t)):"operator"}if("#"==o)return e.skipToEnd(),"comment";if("<"==o&&(i=e.match(/^<([-~])[\`\"\']?([a-zA-Z_?]\w*)[\`\"\']?(?:;|$)/))){return d((n=i[2],r=i[1],function(e,t){return r&&e.eatSpace(),e.match(n)?t.tokenize.pop():e.skipToEnd(),"string"}),e,t)}else if("0"==o)return e.eat("x")?e.eatWhile(/[\da-fA-F]/):e.eat("b")?e.eatWhile(/[01]/):e.eatWhile(/[0-7]/),"number";else if(/\d/.test(o))return e.match(/^[\d_]*(?:\.[\d_]+)?(?:[eE][+\-]?[\d_]+)?/),"number";else if("?"==o){for(;e.match(/^\\[CM]-/););return e.eat("\\")?e.eatWhile(/\w/):e.next(),"string"}else{if(":"==o)return e.eat("'")?d(p("'","atom",!1),e,t):e.eat('"')?d(p('"',"atom",!0),e,t):e.eat(/[\<\>]/)?(e.eat(/[\<\>]/),"atom"):e.eat(/[\+\-\*\/\&\|\:\!]/)?"atom":e.eat(/[a-zA-Z$@_\xa1-\uffff]/)?(e.eatWhile(/[\w$\xa1-\uffff]/),e.eat(/[\?\!\=]/),"atom"):"operator";if("@"==o&&e.match(/^@?[a-zA-Z_\xa1-\uffff]/))return e.eat("@"),e.eatWhile(/[\w\xa1-\uffff]/),"propertyName";if("$"==o)return e.eat(/[a-zA-Z_]/)?e.eatWhile(/[\w]/):e.eat(/\d/)?e.eat(/\d/):e.next(),"variableName.special";if(/[a-zA-Z_\xa1-\uffff]/.test(o))return(e.eatWhile(/[\w\xa1-\uffff]/),e.eat(/[\?\!]/),e.eat(":"))?"atom":"variable";if("|"==o&&(t.varList||"{"==t.lastTok||"do"==t.lastTok))return a="|",null;if(/[\(\)\[\]{}\\;]/.test(o))return a=o,null;if("-"==o&&e.eat(">"))return"operator";if(!/[=+\-\/*:\.^%<>~|]/.test(o))return null;var c=e.eatWhile(/[=+\-\/*:\.^%<>~|]/);return"."!=o||c||(a="."),"operator"}}function p(e,t,n,r){return function(a,i){var o,u=!1;for("read-quoted-paused"===i.context.type&&(i.context=i.context.prev,a.eat("}"));null!=(o=a.next());){if(o==e&&(r||!u)){i.tokenize.pop();break}if(n&&"#"==o&&!u){if(a.eat("{")){"}"==e&&(i.context={prev:i.context,type:"read-quoted-paused"}),i.tokenize.push(function e(t){return t||(t=1),function(n,r){if("}"==n.peek())if(1==t)return r.tokenize.pop(),r.tokenize[r.tokenize.length-1](n,r);else r.tokenize[r.tokenize.length-1]=e(t-1);else"{"==n.peek()&&(r.tokenize[r.tokenize.length-1]=e(t+1));return c(n,r)}}());break}else if(/[@\$]/.test(a.peek())){i.tokenize.push(function(){var e=!1;return function(t,n){return e?(n.tokenize.pop(),n.tokenize[n.tokenize.length-1](t,n)):(e=!0,c(t,n))}}());break}}u=!u&&"\\"==o}return t}}function k(e,t){return e.sol()&&e.match("=end")&&e.eol()&&t.tokenize.pop(),e.skipToEnd(),"comment"}let h={name:"ruby",startState:function(e){return{tokenize:[c],indented:0,context:{type:"top",indented:-e},continuedLine:!1,lastTok:null,varList:!1}},token:function(e,t){a=null,e.sol()&&(t.indented=e.indentation());var n,r=t.tokenize[t.tokenize.length-1](e,t),i=a;if("variable"==r){var s=e.current();"keyword"==(r="."==t.lastTok?"property":o.propertyIsEnumerable(e.current())?"keyword":/^[A-Z]/.test(s)?"tag":"def"==t.lastTok||"class"==t.lastTok||t.varList?"def":"variable")&&(i=s,u.propertyIsEnumerable(s)?n="indent":l.propertyIsEnumerable(s)?n="dedent":("if"==s||"unless"==s)&&e.column()==e.indentation()?n="indent":"do"==s&&t.context.indented<t.indented&&(n="indent"))}return(a||r&&"comment"!=r)&&(t.lastTok=i),"|"==a&&(t.varList=!t.varList),"indent"==n||/[\(\[\{]/.test(a)?t.context={prev:t.context,type:a||r,indented:t.indented}:("dedent"==n||/[\)\]\}]/.test(a))&&t.context.prev&&(t.context=t.context.prev),e.eol()&&(t.continuedLine="\\"==a||"operator"==r),r},indent:function(e,t,n){if(e.tokenize[e.tokenize.length-1]!=c)return null;var r=t&&t.charAt(0),a=e.context,i=a.type==f[r]||"keyword"==a.type&&/^(?:end|until|else|elsif|when|rescue)\b/.test(t);return a.indented+(i?0:n.unit)+(e.continuedLine?n.unit:0)},languageData:{indentOnInput:/^\s*(?:end|rescue|elsif|else|\})$/,commentTokens:{line:"#"},autocomplete:i}}}}]);