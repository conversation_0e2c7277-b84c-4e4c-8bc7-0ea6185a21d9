try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4e8cba29-fd31-4bee-b8fe-f108adde8a60",e._sentryDebugIdIdentifier="sentry-dbid-4e8cba29-fd31-4bee-b8fe-f108adde8a60")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1252],{41252:(e,t,n)=>{n.r(t),n.d(t,{vb:()=>C});var r="error";function a(e){return RegExp("^(("+e.join(")|(")+"))\\b","i")}var i=RegExp("^[\\+\\-\\*/%&\\\\|\\^~<>!]"),o=RegExp("^[\\(\\)\\[\\]\\{\\}@,:`=;\\.]"),c=RegExp("^((==)|(<>)|(<=)|(>=)|(<>)|(<<)|(>>)|(//)|(\\*\\*))"),d=RegExp("^((\\+=)|(\\-=)|(\\*=)|(%=)|(/=)|(&=)|(\\|=)|(\\^=))"),s=RegExp("^((//=)|(>>=)|(<<=)|(\\*\\*=))"),u=RegExp("^[_A-Za-z][_A-Za-z0-9]*"),l=["class","module","sub","enum","select","while","if","function","get","set","property","try","structure","synclock","using","with"],f=["else","elseif","case","catch","finally"],h=["next","loop"],m=["and","andalso","or","orelse","xor","in","not","is","isnot","like"],b=a(m),p=["#const","#else","#elseif","#end","#if","#region","addhandler","addressof","alias","as","byref","byval","cbool","cbyte","cchar","cdate","cdbl","cdec","cint","clng","cobj","compare","const","continue","csbyte","cshort","csng","cstr","cuint","culng","cushort","declare","default","delegate","dim","directcast","each","erase","error","event","exit","explicit","false","for","friend","gettype","goto","handles","implements","imports","infer","inherits","interface","isfalse","istrue","lib","me","mod","mustinherit","mustoverride","my","mybase","myclass","namespace","narrowing","new","nothing","notinheritable","notoverridable","of","off","on","operator","option","optional","out","overloads","overridable","overrides","paramarray","partial","private","protected","public","raiseevent","readonly","redim","removehandler","resume","return","shadows","shared","static","step","stop","strict","then","throw","to","true","trycast","typeof","until","until","when","widening","withevents","writeonly"],g=["object","boolean","char","string","byte","sbyte","short","ushort","int16","uint16","integer","uinteger","int32","uint32","long","ulong","int64","uint64","decimal","single","double","float","date","datetime","intptr","uintptr"],y=a(p),k=a(g),v=a(l),w=a(f),I=a(h),x=a(["end"]),E=a(["do"]);function _(e,t){t.currentIndent++}function z(e,t){t.currentIndent--}function L(e,t){if(e.eatSpace())return null;if("'"===e.peek())return e.skipToEnd(),"comment";if(e.match(/^((&H)|(&O))?[0-9\.a-f]/i,!1)){var n,a,l,f=!1;if(e.match(/^\d*\.\d+F?/i)||e.match(/^\d+\.\d*F?/)?f=!0:e.match(/^\.\d+F?/)&&(f=!0),f)return e.eat(/J/i),"number";var h=!1;if(e.match(/^&H[0-9a-f]+/i)||e.match(/^&O[0-7]+/i)?h=!0:e.match(/^[1-9]\d*F?/)?(e.eat(/J/i),h=!0):e.match(/^0(?![\dx])/i)&&(h=!0),h)return e.eat(/L/i),"number"}return e.match('"')?(a=1==(n=e.current()).length,l="string",t.tokenize=function(e,t){for(;!e.eol();){if(e.eatWhile(/[^'"]/),e.match(n))return t.tokenize=L,l;e.eat(/['"]/)}return a&&(t.tokenize=L),l},t.tokenize(e,t)):e.match(s)||e.match(d)?null:e.match(c)||e.match(i)||e.match(b)?"operator":e.match(o)?null:e.match(E)?(_(e,t),t.doInCurrentLine=!0,"keyword"):e.match(v)?(t.doInCurrentLine?t.doInCurrentLine=!1:_(e,t),"keyword"):e.match(w)?"keyword":e.match(x)?(z(e,t),z(e,t),"keyword"):e.match(I)?(z(e,t),"keyword"):e.match(k)||e.match(y)?"keyword":e.match(u)?"variable":(e.next(),r)}let C={name:"vb",startState:function(){return{tokenize:L,lastToken:null,currentIndent:0,nextLineIndent:0,doInCurrentLine:!1}},token:function(e,t){e.sol()&&(t.currentIndent+=t.nextLineIndent,t.nextLineIndent=0,t.doInCurrentLine=0);var n=function(e,t){var n=t.tokenize(e,t),a=e.current();if("."===a)return"variable"===(n=t.tokenize(e,t))?"variable":r;var i="[({".indexOf(a);return(-1!==i&&_(e,t),-1!==(i="])}".indexOf(a))&&z(e,t))?r:n}(e,t);return t.lastToken={style:n,content:e.current()},n},indent:function(e,t,n){var r=t.replace(/^\s+|\s+$/g,"");return r.match(I)||r.match(x)||r.match(w)?n.unit*(e.currentIndent-1):e.currentIndent<0?0:e.currentIndent*n.unit},languageData:{closeBrackets:{brackets:["(","[","{",'"']},commentTokens:{line:"'"},autocomplete:l.concat(f).concat(h).concat(m).concat(p).concat(g)}}}}]);