try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="9567a3de-316c-4232-8bf6-a8e9e3a061be",e._sentryDebugIdIdentifier="sentry-dbid-9567a3de-316c-4232-8bf6-a8e9e3a061be")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6826],{56826:(e,t,n)=>{function r(e,t){return RegExp((t?"":"^")+"(?:"+e.join("|")+")"+(t?"$":"\\b"))}function a(e,t,n){return n.tokenize.push(e),e(t,n)}n.r(t),n.d(t,{crystal:()=>E});var u=/^(?:[-+/%|&^]|\*\*?|[<>]{2})/,i=/^(?:[=!]~|===|<=>|[<>=!]=?|[|&]{2}|~)/,o=/^(?:\[\][?=]?)/,c=/^(?:\.(?:\.{2})?|->|[?:])/,s=/^[a-z_\u009F-\uFFFF][a-zA-Z0-9_\u009F-\uFFFF]*/,f=/^[A-Z_\u009F-\uFFFF][a-zA-Z0-9_\u009F-\uFFFF]*/,l=r(["abstract","alias","as","asm","begin","break","case","class","def","do","else","elsif","end","ensure","enum","extend","for","fun","if","include","instance_sizeof","lib","macro","module","next","of","out","pointerof","private","protected","rescue","return","require","select","sizeof","struct","super","then","type","typeof","uninitialized","union","unless","until","when","while","with","yield","__DIR__","__END_LINE__","__FILE__","__LINE__"]),m=r(["true","false","nil","self"]),h=r(["def","fun","macro","class","module","struct","lib","enum","union","do","for"]),p=r(["if","unless","case","while","until","begin","then"]),d=["end","else","elsif","rescue","ensure"],k=r(d),_=["\\)","\\}","\\]"],b=RegExp("^(?:"+_.join("|")+")$"),F={def:w,fun:w,macro:function(e,t){var n;if(e.eatSpace())return null;if(n=e.match(s)){if("def"==n)return"keyword";e.eat(/[?!]/)}return t.tokenize.pop(),"def"},class:x,module:x,struct:x,lib:x,enum:x,union:x},z={"[":"]","{":"}","(":")","<":">"};function g(e,t){if(e.eatSpace())return null;if("\\"!=t.lastToken&&e.match("{%",!1))return a(I("%","%"),e,t);if("\\"!=t.lastToken&&e.match("{{",!1))return a(I("{","}"),e,t);if("#"==e.peek())return e.skipToEnd(),"comment";if(e.match(s)){if(e.eat(/[?!]/),d=e.current(),e.eat(":"))return"atom";if("."==t.lastToken)return"property";if(l.test(d))return h.test(d)?"fun"==d&&t.blocks.indexOf("lib")>=0||"def"==d&&"abstract"==t.lastToken||(t.blocks.push(d),t.currentIndent+=1):("operator"==t.lastStyle||!t.lastStyle)&&p.test(d)?(t.blocks.push(d),t.currentIndent+=1):"end"==d&&(t.blocks.pop(),t.currentIndent-=1),F.hasOwnProperty(d)&&t.tokenize.push(F[d]),"keyword";else if(m.test(d))return"atom";return"variable"}if(e.eat("@"))return"["==e.peek()?a(y("[","]","meta"),e,t):(e.eat("@"),e.match(s)||e.match(f),"propertyName");if(e.match(f))return"tag";if(e.eat(":"))return e.eat('"')?a(v('"',"atom",!1),e,t):e.match(s)||e.match(f)||e.match(u)||e.match(i)||e.match(o)?"atom":(e.eat(":"),"operator");if(e.eat('"'))return a(v('"',"string",!0),e,t);if("%"==e.peek()){var n,r,d,k,_="string",b=!0;if(e.match("%r"))_="string.special",k=e.next();else if(e.match("%w"))b=!1,k=e.next();else if(e.match("%q"))b=!1,k=e.next();else if(k=e.match(/^%([^\w\s=])/))k=k[1];else if(e.match(/^%[a-zA-Z_\u009F-\uFFFF][\w\u009F-\uFFFF]*/))return"meta";else if(e.eat("%"))return"operator";return z.hasOwnProperty(k)&&(k=z[k]),a(v(k,_,b),e,t)}return(d=e.match(/^<<-('?)([A-Z]\w*)\1/))?a((n=d[2],r=!d[1],function(e,t){if(e.sol()&&(e.eatSpace(),e.match(n)))return t.tokenize.pop(),"string";for(var a=!1;e.peek();)if(a)e.next(),a=!1;else{if(e.match("{%",!1))return t.tokenize.push(I("%","%")),"string";if(e.match("{{",!1))return t.tokenize.push(I("{","}")),"string";if(r&&e.match("#{",!1))return t.tokenize.push(y("#{","}","meta")),"string";a="\\"==e.next()&&r}return"string"}),e,t):e.eat("'")?(e.match(/^(?:[^']|\\(?:[befnrtv0'"]|[0-7]{3}|u(?:[0-9a-fA-F]{4}|\{[0-9a-fA-F]{1,6}\})))/),e.eat("'"),"atom"):e.eat("0")?(e.eat("x")?e.match(/^[0-9a-fA-F_]+/):e.eat("o")?e.match(/^[0-7_]+/):e.eat("b")&&e.match(/^[01_]+/),"number"):e.eat(/^\d/)?(e.match(/^[\d_]*(?:\.[\d_]+)?(?:[eE][+-]?\d+)?/),"number"):e.match(u)?(e.eat("="),"operator"):e.match(i)||e.match(c)?"operator":(d=e.match(/[({[]/,!1))?a(y(d=d[0],z[d],null),e,t):e.eat("\\")?(e.next(),"meta"):(e.next(),null)}function y(e,t,n,r){return function(a,u){if(!r&&a.match(e))return u.tokenize[u.tokenize.length-1]=y(e,t,n,!0),u.currentIndent+=1,n;var i=g(a,u);return a.current()===t&&(u.tokenize.pop(),u.currentIndent-=1,i=n),i}}function I(e,t,n){return function(r,a){return!n&&r.match("{"+e)?(a.currentIndent+=1,a.tokenize[a.tokenize.length-1]=I(e,t,!0),"meta"):r.match(t+"}")?(a.currentIndent-=1,a.tokenize.pop(),"meta"):g(r,a)}}function w(e,t){return e.eatSpace()?null:(e.match(s)?e.eat(/[!?]/):e.match(u)||e.match(i)||e.match(o),t.tokenize.pop(),"def")}function x(e,t){return e.eatSpace()?null:(e.match(f),t.tokenize.pop(),"def")}function v(e,t,n){return function(r,a){for(var u=!1;r.peek();)if(u)r.next(),u=!1;else{if(r.match("{%",!1))return a.tokenize.push(I("%","%")),t;if(r.match("{{",!1))return a.tokenize.push(I("{","}")),t;if(n&&r.match("#{",!1))return a.tokenize.push(y("#{","}","meta")),t;var i=r.next();if(i==e)return a.tokenize.pop(),t;u=n&&"\\"==i}return t}}let E={name:"crystal",startState:function(){return{tokenize:[g],currentIndent:0,lastToken:null,lastStyle:null,blocks:[]}},token:function(e,t){var n=t.tokenize[t.tokenize.length-1](e,t),r=e.current();return n&&"comment"!=n&&(t.lastToken=r,t.lastStyle=n),n},indent:function(e,t,n){return(t=t.replace(/^\s*(?:\{%)?\s*|\s*(?:%\})?\s*$/g,""),k.test(t)||b.test(t))?n.unit*(e.currentIndent-1):n.unit*e.currentIndent},languageData:{indentOnInput:r(_.concat(d),!0),commentTokens:{line:"#"}}}}}]);