try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="657a5cae-e4d9-4576-8d49-6a2824e207ac",e._sentryDebugIdIdentifier="sentry-dbid-657a5cae-e4d9-4576-8d49-6a2824e207ac")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9954],{183:(e,t,r)=>{Promise.resolve().then(r.bind(r,99813)),Promise.resolve().then(r.bind(r,9005))},2030:(e,t,r)=>{"use strict";r.d(t,{Cn:()=>a,iI:()=>s});let s=e=>[{title:"Available Courses",url:"/courses",icon:"searchList",isActive:e.startsWith("/courses"),shortcut:["a","c"],items:[]},{title:"My Courses",url:"/my-courses",icon:"graduationCap",isActive:e.startsWith("/my-courses"),shortcut:["m","c"],items:[]}],a=s("")},9005:(e,t,r)=>{"use strict";r.d(t,{EnrollmentProvider:()=>i,q:()=>l});var s=r(95155),a=r(12115),n=r(55542);let o=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(o);if(!e)throw Error("useEnrollment must be used within an EnrollmentProvider");return e},i=e=>{let{children:t}=e,[r,l]=(0,a.useState)(!1),[i,d]=(0,a.useState)(n.n4),[c,u]=(0,a.useState)([]),m="lms-enrollment-data",h="lms-multiple-enrollment-data";(0,a.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(h);if(e){let t=JSON.parse(e);Date.now()<t.expirationTime?(u(t.enrolledCourses),l(t.enrolledCourses.length>0),t.enrolledCourses.length>0&&d(t.enrolledCourses[0])):localStorage.removeItem(h);return}let t=localStorage.getItem(m);if(t){let e=JSON.parse(t);if(Date.now()<e.expirationTime){l(e.isEnrolled),d(e.courseData),u([e.courseData]);let t={enrolledCourses:[e.courseData],enrollmentTimestamp:e.enrollmentTimestamp,expirationTime:e.expirationTime};localStorage.setItem(h,JSON.stringify(t)),localStorage.removeItem(m)}else localStorage.removeItem(m)}}catch(e){console.error("Failed to load enrollment data:",e),localStorage.removeItem(m),localStorage.removeItem(h)}})()},[]);let x=e=>{let t=Date.now();try{u(r=>{let s,a={enrolledCourses:s=r.some(t=>t.id===e.id)?r.map(t=>t.id===e.id?e:t):[...r,e],enrollmentTimestamp:t,expirationTime:t+6e5};return localStorage.setItem(h,JSON.stringify(a)),s}),setTimeout(()=>{localStorage.removeItem(h),l(!1),u([]),d(n.n4)},6e5)}catch(e){console.error("Failed to persist enrollment data:",e)}};return(0,s.jsx)(o.Provider,{value:{isEnrolled:r,courseData:i,enrollInCourse:()=>{l(!0);let e={...n.n4,status:"in-progress"};d(e),x(e)},enrollInCourseWithPurchase:e=>{l(!0);let t={...e,status:"in-progress",totalProgress:0};d(t),x(t)},updateCourseProgress:e=>{i.id===e.id&&d(e),u(t=>t.map(t=>t.id===e.id?e:t)),r&&x(e)},enrolledCourses:c,isEnrolledInCourse:e=>c.some(t=>t.id===e),getCourseById:e=>c.find(t=>t.id===e)},"data-sentry-element":"EnrollmentContext.Provider","data-sentry-component":"EnrollmentProvider","data-sentry-source-file":"enrollment-context.tsx",children:t})}},20063:(e,t,r)=>{"use strict";var s=r(47260);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},99813:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(95155),a=r(2030),n=r(51497),o=r(20063),l=r(12115);let i=l.forwardRef((e,t)=>{var r;let{action:a,active:n,currentRootActionId:o}=e,i=l.useMemo(()=>{if(!o)return a.ancestors;let e=a.ancestors.findIndex(e=>e.id===o);return a.ancestors.slice(e+1)},[a.ancestors,o]);return(0,s.jsxs)("div",{ref:t,className:"relative z-10 flex cursor-pointer items-center justify-between px-4 py-3",children:[n&&(0,s.jsx)("div",{id:"kbar-result-item",className:"border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4"}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[a.icon&&a.icon,(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{children:[i.length>0&&i.map(e=>(0,s.jsxs)(l.Fragment,{children:[(0,s.jsx)("span",{className:"text-muted-foreground mr-2",children:e.name}),(0,s.jsx)("span",{className:"mr-2",children:"›"})]},e.id)),(0,s.jsx)("span",{children:a.name})]}),a.subtitle&&(0,s.jsx)("span",{className:"text-muted-foreground text-sm",children:a.subtitle})]})]}),(null==(r=a.shortcut)?void 0:r.length)?(0,s.jsx)("div",{className:"relative z-10 grid grid-flow-col gap-1",children:a.shortcut.map((e,t)=>(0,s.jsx)("kbd",{className:"bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium",children:e},e+t))}):null]})});function d(){let{results:e,rootActionId:t}=(0,n.useMatches)();return(0,s.jsx)(n.KBarResults,{items:e,onRender:e=>{let{item:r,active:a}=e;return"string"==typeof r?(0,s.jsx)("div",{className:"text-primary-foreground px-4 py-2 text-sm uppercase opacity-50",children:r}):(0,s.jsx)(i,{action:r,active:a,currentRootActionId:null!=t?t:""})},"data-sentry-element":"KBarResults","data-sentry-component":"RenderResults","data-sentry-source-file":"render-result.tsx"})}i.displayName="KBarResultItem";var c=r(5379);let u=()=>{let{theme:e,setTheme:t}=(0,c.D)();(0,n.useRegisterActions)([{id:"toggleTheme",name:"Toggle Theme",shortcut:["t","t"],section:"Theme",perform:()=>{t("light"===e?"dark":"light")}},{id:"setLightTheme",name:"Set Light Theme",section:"Theme",perform:()=>t("light")},{id:"setDarkTheme",name:"Set Dark Theme",section:"Theme",perform:()=>t("dark")}],[e])};function m(e){let{children:t}=e,r=(0,o.useRouter)(),i=(0,l.useMemo)(()=>{let e=e=>{r.push(e)};return a.Cn.flatMap(t=>{var r,s;let a="#"!==t.url?{id:"".concat(t.title.toLowerCase(),"Action"),name:t.title,shortcut:t.shortcut,keywords:t.title.toLowerCase(),section:"Navigation",subtitle:"Go to ".concat(t.title),perform:()=>e(t.url)}:null,n=null!=(s=null==(r=t.items)?void 0:r.map(r=>({id:"".concat(r.title.toLowerCase(),"Action"),name:r.title,shortcut:r.shortcut,keywords:r.title.toLowerCase(),section:t.title,subtitle:"Go to ".concat(r.title),perform:()=>e(r.url)})))?s:[];return a?[a,...n]:n})},[r]);return(0,s.jsx)(n.KBarProvider,{actions:i,"data-sentry-element":"KBarProvider","data-sentry-component":"KBar","data-sentry-source-file":"index.tsx",children:(0,s.jsx)(h,{"data-sentry-element":"KBarComponent","data-sentry-source-file":"index.tsx",children:t})})}let h=e=>{let{children:t}=e;return u(),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.KBarPortal,{"data-sentry-element":"KBarPortal","data-sentry-source-file":"index.tsx",children:(0,s.jsx)(n.KBarPositioner,{className:"bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm","data-sentry-element":"KBarPositioner","data-sentry-source-file":"index.tsx",children:(0,s.jsxs)(n.KBarAnimator,{className:"bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg","data-sentry-element":"KBarAnimator","data-sentry-source-file":"index.tsx",children:[(0,s.jsx)("div",{className:"bg-card border-border sticky top-0 z-10 border-b",children:(0,s.jsx)(n.KBarSearch,{className:"bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden","data-sentry-element":"KBarSearch","data-sentry-source-file":"index.tsx"})}),(0,s.jsx)("div",{className:"max-h-[400px]",children:(0,s.jsx)(d,{"data-sentry-element":"RenderResults","data-sentry-source-file":"index.tsx"})})]})})}),t]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[1675,5542,4850,8441,3840,7358],()=>t(183)),_N_E=e.O()}]);