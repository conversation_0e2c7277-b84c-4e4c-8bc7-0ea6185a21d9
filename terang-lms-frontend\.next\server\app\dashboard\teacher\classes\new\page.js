try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8a2b84c7-ef39-45ca-8551-0cdc2fa0449d",e._sentryDebugIdIdentifier="sentry-dbid-8a2b84c7-ef39-45ca-8551-0cdc2fa0449d")}catch(e){}(()=>{var e={};e.id=350,e.ids=[350],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4978:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6516:(e,t,r)=>{Promise.resolve().then(r.bind(r,83756))},8086:e=>{"use strict";e.exports=require("module")},8238:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var s=r(91754);r(93491);var a=r(82233);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},14621:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(91754);function a({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}r(93491),r(76328)},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21626:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(91754);r(93491);var a=r(66207),n=r(82233);function o({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},52377:(e,t,r)=>{Promise.resolve().then(r.bind(r,14621))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58428:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var s=r(91754);r(93491);var a=r(82233);function n({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},60290:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>v,generateImageMetadata:()=>y,generateMetadata:()=>f,generateViewport:()=>g,metadata:()=>p});var a=r(63033),n=r(18188),o=r(5434),i=r(45188),d=r(67999),l=r(4590),c=r(23064),u=r(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(o.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(i.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...a},h="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;s=new Proxy(m,{apply:(e,t,r)=>{let s,a,n;try{let e=h?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}});let f=void 0,y=void 0,g=void 0,v=s},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64755:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=r(63033),n=r(1472),o=r(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s="function"==typeof i?new Proxy(i,{apply:(e,t,r)=>{let s,a,n;try{let e=l?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher",componentType:"Layout",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}}):i;let c=void 0,u=void 0,p=void 0,m=s},66207:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var s=r(93491),a=r(90604),n=r(91754),o=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},70132:(e,t,r)=>{Promise.resolve().then(r.bind(r,84882))},73024:e=>{"use strict";e.exports=require("node:fs")},73562:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78073:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(95500),a=r(56947),n=r(26052),o=r(13636),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let d={children:["",{children:["dashboard",{children:["teacher",{children:["classes",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84882)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,64755)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\new\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/teacher/classes/new/page",pathname:"/dashboard/teacher/classes/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81753:(e,t,r)=>{Promise.resolve().then(r.bind(r,64755))},83756:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(91754),a=r(93491),n=r(21372),o=r(9260),i=r(56682),d=r(59672),l=r(21626),c=r(58428),u=r(4978),p=r(31619),m=r(8238),x=r(73562),h=r(16041),f=r.n(h),y=r(76328),g=r(81012),v=r(15854);function b(){let e=(0,n.useRouter)(),[t,r]=(0,a.useState)(!1),[h,b]=(0,a.useState)({name:"",description:""}),[j,w]=(0,a.useState)(null),[A,N]=(0,a.useState)(null),[C,S]=(0,a.useState)(!1),P=async e=>{try{S(!0);let t=new FormData;t.append("file",e);let r=await fetch("/api/upload",{method:"POST",body:t});if(!r.ok)throw Error("Failed to upload image");return(await r.json()).url}catch(e){return console.error("Error uploading image:",e),g.oR.error("Failed to upload image"),null}finally{S(!1)}},q=async t=>{t.preventDefault(),r(!0);try{let t=y.qs.getUser();if(!t)return void g.oR.error("Please log in to create classes");if(!t.institutionId)return void g.oR.error("You must be assigned to an institution to create classes");if(!h.name.trim())return void g.oR.error("Class name is required");let r=null;if(j&&!(r=await P(j)))return;let s=await fetch("/api/classes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:h.name.trim(),description:h.description.trim(),teacherId:t.id,institutionId:t.institutionId,coverPicture:r})}),a=await s.json();a.success?(g.oR.success("Class created successfully!"),e.push("/dashboard/teacher/classes")):g.oR.error(a.error||"Failed to create class")}catch(e){console.error("Error creating class:",e),g.oR.error("Failed to create class")}finally{r(!1)}},_=(e,t)=>{b(r=>({...r,[e]:t}))};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewClassPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(f(),{href:"/dashboard/teacher/classes","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create New Class"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create a new class to organize your students"})]})]}),(0,s.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Class Details"}),(0,s.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter the basic information for the new class"})]}),(0,s.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Class Name"}),(0,s.jsx)(d.p,{id:"name",value:h.name,onChange:e=>_("name",e.target.value),placeholder:"e.g., Mathematics Grade 10A",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Choose a descriptive name that includes subject and grade level"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"description","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Description"}),(0,s.jsx)(c.T,{id:"description",value:h.description,onChange:e=>_("description",e.target.value),placeholder:"Brief description of the class and its objectives",rows:4,"data-sentry-element":"Textarea","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Provide a brief description of what this class covers"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"coverImage","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Cover Image"}),(0,s.jsx)("div",{className:"space-y-4",children:A?(0,s.jsxs)("div",{className:"relative w-full max-w-md",children:[(0,s.jsx)(v.default,{src:A,alt:"Cover preview",width:400,height:200,className:"rounded-lg object-cover w-full h-48"}),(0,s.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{w(null),N(null)},children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]}):(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[(0,s.jsx)(m.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)(l.J,{htmlFor:"coverImage",className:"cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Choose Image"}),(0,s.jsx)(d.p,{id:"coverImage",type:"file",accept:"image/*",onChange:e=>{let t=e.target.files?.[0];if(t){if(!t.type.startsWith("image/"))return void g.oR.error("Please select a valid image file");if(t.size>5242880)return void g.oR.error("Image size must be less than 5MB");w(t);let e=new FileReader;e.onload=e=>{N(e.target?.result)},e.readAsDataURL(t)}},className:"hidden"})]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 5MB"})]})}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Upload a cover image for your class (optional)"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)(f(),{href:"/dashboard/teacher/classes","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(i.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,s.jsxs)(i.$,{type:"submit",disabled:t||C,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),C?"Uploading Image...":t?"Creating...":"Create Class"]})]})]})})]}),(0,s.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Next Steps"}),(0,s.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"After creating your class, you can:"})]}),(0,s.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,s.jsx)("span",{children:"Add students to your class"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,s.jsx)("span",{children:"Create or assign courses to the class"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,s.jsx)("span",{children:"Generate course codes for student enrollment"})]}),(0,s.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"h-2 w-2 rounded-full bg-blue-500"}),(0,s.jsx)("span",{children:"Track student progress and performance"})]})]})})]})]})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},84882:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=r(63033),n=r(1472),o=r(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\new\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s="function"==typeof i?new Proxy(i,{apply:(e,t,r)=>{let s,a,n;try{let e=l?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher/classes/new",componentType:"Page",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}}):i;let c=void 0,u=void 0,p=void 0,m=s},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634],()=>r(78073));module.exports=s})();
//# sourceMappingURL=page.js.map