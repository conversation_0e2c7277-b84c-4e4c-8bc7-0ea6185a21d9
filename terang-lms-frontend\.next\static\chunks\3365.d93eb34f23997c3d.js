try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="98d2b8b0-831d-4124-826c-4e796aeabbf9",e._sentryDebugIdIdentifier="sentry-dbid-98d2b8b0-831d-4124-826c-4e796aeabbf9")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3365],{83365:(e,t,n)=>{n.r(t),n.d(t,{commonLisp:()=>f});var r,o=/^(block|let*|return-from|catch|load-time-value|setq|eval-when|locally|symbol-macrolet|flet|macrolet|tagbody|function|multiple-value-call|the|go|multiple-value-prog1|throw|if|progn|unwind-protect|labels|progv|let|quote)$/,l=/^with|^def|^do|^prog|case$|^cond$|bind$|when$|unless$/,i=/^(?:[+\-]?(?:\d+|\d*\.\d+)(?:[efd][+\-]?\d+)?|[+\-]?\d+(?:\/[+\-]?\d+)?|#b[+\-]?[01]+|#o[+\-]?[0-7]+|#x[+\-]?[\da-f]+)/,a=/[^\s'`,@()\[\]";]/;function s(e){for(var t;t=e.next();)if("\\"==t)e.next();else if(!a.test(t)){e.backUp(1);break}return e.current()}function c(e,t){if(e.eatSpace())return r="ws",null;if(e.match(i))return"number";var n=e.next();if("\\"==n&&(n=e.next()),'"'==n)return(t.tokenize=u)(e,t);if("("==n)return r="open","bracket";if(")"==n)return r="close","bracket";if(";"==n)return e.skipToEnd(),r="ws","comment";if(/['`,@]/.test(n))return null;else if("|"==n)if(e.skipTo("|"))return e.next(),"variableName";else return e.skipToEnd(),"error";else if("#"==n){var n=e.next();if("("==n)return r="open","bracket";if(/[+\-=\.']/.test(n))return null;if(/\d/.test(n)&&e.match(/^\d*#/))return null;else if("|"==n)return(t.tokenize=d)(e,t);else if(":"==n)return s(e),"meta";else if("\\"==n)return e.next(),s(e),"string.special";else return"error"}else{var a=s(e);return"."==a?null:(r="symbol","nil"==a||"t"==a||":"==a.charAt(0))?"atom":"open"==t.lastType&&(o.test(a)||l.test(a))?"keyword":"&"==a.charAt(0)?"variableName.special":"variableName"}}function u(e,t){for(var n,r=!1;n=e.next();){if('"'==n&&!r){t.tokenize=c;break}r=!r&&"\\"==n}return"string"}function d(e,t){for(var n,o;n=e.next();){if("#"==n&&"|"==o){t.tokenize=c;break}o=n}return r="ws","comment"}let f={name:"commonlisp",startState:function(){return{ctx:{prev:null,start:0,indentTo:0},lastType:null,tokenize:c}},token:function(e,t){e.sol()&&"number"!=typeof t.ctx.indentTo&&(t.ctx.indentTo=t.ctx.start+1),r=null;var n=t.tokenize(e,t);return"ws"!=r&&(null==t.ctx.indentTo?"symbol"==r&&l.test(e.current())?t.ctx.indentTo=t.ctx.start+e.indentUnit:t.ctx.indentTo="next":"next"==t.ctx.indentTo&&(t.ctx.indentTo=e.column()),t.lastType=r),"open"==r?t.ctx={prev:t.ctx,start:e.column(),indentTo:null}:"close"==r&&(t.ctx=t.ctx.prev||t.ctx),n},indent:function(e){var t=e.ctx.indentTo;return"number"==typeof t?t:e.ctx.start+1},languageData:{commentTokens:{line:";;",block:{open:"#|",close:"|#"}},closeBrackets:{brackets:["(","[","{",'"']}}}}}]);