try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="150f8e01-739c-44b1-8859-8bf183d2b4f0",e._sentryDebugIdIdentifier="sentry-dbid-150f8e01-739c-44b1-8859-8bf183d2b4f0")}catch(e){}(()=>{var e={};e.id=4274,e.ids=[4274],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3514:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(91754),r=s(93491),n=s(9260),l=s(56682),o=s(59672),i=s(80601),d=s(69122),c=s(40636),u=s(92681),m=s(41939),p=s(26711);let x=(0,s(55732).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var f=s(96196),h=s(88373),y=s(22928),g=s(57e3),b=s(99462),v=s(93626),j=s(16041),w=s.n(j),S=s(40254);function A(){let[e,t]=(0,r.useState)(""),[s,j]=(0,r.useState)("all"),[A,N]=(0,r.useState)("all"),[T,C]=(0,r.useState)([]),[_,P]=(0,r.useState)([]),[q,k]=(0,r.useState)(!0),[E,D]=(0,r.useState)(null),{toast:I}=(0,S.d)();(0,r.useRef)(!0),(0,r.useCallback)(async()=>{try{let e=await fetch("/api/institutions"),t=await e.json();t.success&&P(t.data.institutions)}catch(e){console.error("Error fetching institutions:",e)}},[]);let R=(0,r.useCallback)(async(e="",t="all",s="all")=>{try{k(!0);let a=new URLSearchParams;e&&a.append("search",e),"all"!==t&&a.append("role",t),"all"!==s&&a.append("institution_id",s);let r=await fetch(`/api/users?${a.toString()}`),n=await r.json();n.success?C(n.data.users):I({title:"Error",description:n.error||"Failed to fetch users",variant:"destructive"})}catch(e){console.error("Error fetching users:",e),I({title:"Error",description:"Failed to fetch users",variant:"destructive"})}finally{k(!1)}},[]),U=async t=>{if(confirm("Are you sure you want to delete this user? This action cannot be undone."))try{D(t);let a=await fetch(`/api/users/${t}`,{method:"DELETE"}),r=await a.json();r.success?(I({title:"Success",description:"User deleted successfully"}),R(e,s,A)):I({title:"Error",description:r.error||"Failed to delete user",variant:"destructive"})}catch(e){console.error("Error deleting user:",e),I({title:"Error",description:"Failed to delete user",variant:"destructive"})}finally{D(null)}},G=e=>{let t="super_admin"===e?"default":"teacher"===e?"secondary":"outline";return(0,a.jsx)(i.E,{variant:t,"data-sentry-element":"Badge","data-sentry-component":"getRoleBadge","data-sentry-source-file":"page.tsx",children:e.replace("_"," ")})};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"UsersPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Users"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage all users on the platform"})]}),(0,a.jsx)(w(),{href:"/dashboard/admin/users/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Add User"]})})]}),(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"All Users"}),(0,a.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"View and manage all registered users"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(p.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(o.p,{placeholder:"Search users by name or email...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(d.l6,{value:s,onValueChange:j,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(d.bq,{className:"w-[150px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(x,{className:"mr-2 h-4 w-4","data-sentry-element":"Filter","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(d.yv,{placeholder:"Role","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(d.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(d.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Roles"}),(0,a.jsx)(d.eb,{value:"student","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Student"}),(0,a.jsx)(d.eb,{value:"teacher","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Teacher"}),(0,a.jsx)(d.eb,{value:"super_admin","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Super Admin"})]})]}),(0,a.jsxs)(d.l6,{value:A,onValueChange:N,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(d.bq,{className:"w-[200px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Building2","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(d.yv,{placeholder:"Institution","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(d.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(d.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Institutions"}),(0,a.jsx)(d.eb,{value:"null","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"No Institution"}),_.map(e=>(0,a.jsx)(d.eb,{value:e.id.toString(),children:e.name},e.id))]})]})]}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(c.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(c.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(c.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"User"}),(0,a.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Role"}),(0,a.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,a.jsx)(c.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Created"}),(0,a.jsx)(c.TableHead,{className:"w-[70px]","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Actions"})]})}),(0,a.jsx)(c.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:q?(0,a.jsx)(c.TableRow,{children:(0,a.jsxs)(c.TableCell,{colSpan:5,className:"py-8 text-center",children:[(0,a.jsx)(h.A,{className:"mx-auto h-6 w-6 animate-spin"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:"Loading users..."})]})}):0===T.length?(0,a.jsx)(c.TableRow,{children:(0,a.jsxs)(c.TableCell,{colSpan:5,className:"py-8 text-center",children:[(0,a.jsx)(y.A,{className:"text-muted-foreground mx-auto mb-4 h-12 w-12"}),(0,a.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"No users found"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:e||"all"!==s||"all"!==A?"Try adjusting your search or filters.":"Get started by adding a new user."}),!e&&"all"===s&&"all"===A&&(0,a.jsx)(w(),{href:"/dashboard/admin/users/new",children:(0,a.jsxs)(l.$,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Add User"]})})]})}):T.map(e=>(0,a.jsxs)(c.TableRow,{children:[(0,a.jsx)(c.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"text-muted-foreground h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:e.email})]})]})}),(0,a.jsx)(c.TableCell,{children:G(e.role)}),(0,a.jsx)(c.TableCell,{children:e.institution_name?(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(f.A,{className:"text-muted-foreground h-3 w-3"}),(0,a.jsx)("span",{className:"text-sm",children:e.institution_name})]}):(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:"No institution"})}),(0,a.jsx)(c.TableCell,{children:(0,a.jsx)("span",{className:"text-sm",children:new Date(e.created_at).toLocaleDateString()})}),(0,a.jsx)(c.TableCell,{children:(0,a.jsxs)(u.rI,{children:[(0,a.jsx)(u.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",className:"h-8 w-8 p-0",disabled:E===e.id,children:E===e.id?(0,a.jsx)(h.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(g.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(u.SQ,{align:"end",children:[(0,a.jsx)(u._2,{asChild:!0,children:(0,a.jsxs)(w(),{href:`/dashboard/admin/users/${e.id}`,children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,a.jsxs)(u._2,{className:"text-red-600",onClick:()=>U(e.id),disabled:E===e.id||"super_admin"===e.role,children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})})]})]})]})}},6902:(e,t,s)=>{Promise.resolve().then(s.bind(s,28782))},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>c});var a=s(91754);s(93491);var r=s(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,s)=>{Promise.resolve().then(s.bind(s,7346)),Promise.resolve().then(s.bind(s,21444)),Promise.resolve().then(s.bind(s,3033)),Promise.resolve().then(s.bind(s,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26711:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},28354:e=>{"use strict";e.exports=require("util")},28782:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var r=s(63033),n=s(1472),l=s(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx","default");let i={...r},d="workUnitAsyncStorage"in i?i.workUnitAsyncStorage:"requestAsyncStorage"in i?i.requestAsyncStorage:void 0;a="function"==typeof o?new Proxy(o,{apply:(e,t,s)=>{let a,r,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return l.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}}):o;let c=void 0,u=void 0,m=void 0,p=a},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30974:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var r=s(63033),n=s(1472),l=s(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\page.tsx","default");let i={...r},d="workUnitAsyncStorage"in i?i.workUnitAsyncStorage:"requestAsyncStorage"in i?i.requestAsyncStorage:void 0;a="function"==typeof o?new Proxy(o,{apply:(e,t,s)=>{let a,r,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return l.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin/users",componentType:"Page",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}}):o;let c=void 0,u=void 0,m=void 0,p=a},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},38727:(e,t,s)=>{Promise.resolve().then(s.bind(s,3514))},40254:(e,t,s)=>{"use strict";s.d(t,{d:()=>r});var a=s(81012);function r(){return{toast:({title:e,description:t,variant:s="default"})=>{"destructive"===s?a.oR.error(e,{description:t}):a.oR.success(e,{description:t})}}}},40636:(e,t,s)=>{"use strict";s.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>l,TableRow:()=>i});var a=s(91754);s(93491);var r=s(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...t})})}function l({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...t,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function o({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...t,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},41692:e=>{"use strict";e.exports=require("node:tls")},42953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var a=s(95500),r=s(56947),n=s(26052),l=s(13636),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let i={children:["",{children:["dashboard",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,30974)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,28782)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/users/page",pathname:"/dashboard/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},44708:e=>{"use strict";e.exports=require("node:https")},46814:(e,t,s)=>{Promise.resolve().then(s.bind(s,49540))},48161:e=>{"use strict";e.exports=require("node:os")},49540:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(91754);function r({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}s(93491),s(76328)},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>b,generateImageMetadata:()=>y,generateMetadata:()=>h,generateViewport:()=>g,metadata:()=>m});var r=s(63033),n=s(18188),l=s(5434),o=s(45188),i=s(67999),d=s(4590),c=s(23064),u=s(7688);let m={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function p({children:e}){let t=await (0,c.UL)(),s=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(l.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(d.SidebarProvider,{defaultOpen:s,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(i.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...r},f="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;a=new Proxy(p,{apply:(e,t,s)=>{let a,r,n;try{let e=f?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}});let h=void 0,y=void 0,g=void 0,b=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69122:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var a=s(91754);s(93491);var r=s(97543),n=s(33093),l=s(87435),o=s(20388),i=s(82233);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u({className:e,size:t="default",children:s,...l}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[s,(0,a.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)(x,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,a.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:t}),(0,a.jsx)(f,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function p({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(l.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,a.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(o.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function f({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,s)=>{Promise.resolve().then(s.bind(s,5434)),Promise.resolve().then(s.bind(s,45188)),Promise.resolve().then(s.bind(s,67999)),Promise.resolve().then(s.bind(s,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78895:(e,t,s)=>{Promise.resolve().then(s.bind(s,30974))},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80601:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(91754);s(93491);var r=s(16435),n=s(25758),l=s(82233);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,asChild:s=!1,...n}){let i=s?r.DX:"span";return(0,a.jsx)(i,{"data-slot":"badge",className:(0,l.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},99462:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8134,8634],()=>s(42953));module.exports=a})();
//# sourceMappingURL=page.js.map