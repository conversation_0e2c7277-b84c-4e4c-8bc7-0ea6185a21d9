try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c62d62b2-9b6a-4708-a392-f312e1a66414",e._sentryDebugIdIdentifier="sentry-dbid-c62d62b2-9b6a-4708-a392-f312e1a66414")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6269],{46269:(e,t,n)=>{function r(e){return RegExp("^(("+e.join(")|(")+"))\\b")}n.r(t),n.d(t,{oz:()=>g});var a=/[\^@!\|<>#~\.\*\-\+\\/,=]/,o=/(<-)|(:=)|(=<)|(>=)|(<=)|(<:)|(>:)|(=:)|(\\=)|(\\=:)|(!!)|(==)|(::)/,i=/(:::)|(\.\.\.)|(=<:)|(>=:)/,u=["in","then","else","of","elseof","elsecase","elseif","catch","finally","with","require","prepare","import","export","define","do"],c=["end"],s=r(["true","false","nil","unit"]),f=r(["andthen","at","attr","declare","feat","from","lex","mod","div","mode","orelse","parser","prod","prop","scanner","self","syn","token"]),d=r(["local","proc","fun","case","class","if","cond","or","dis","choice","not","thread","try","raise","lock","for","suchthat","meth","functor"]),l=r(u),h=r(c);function m(e,t){if(e.eatSpace())return null;if(e.match(/[{}]/))return"bracket";if(e.match("[]"))return"keyword";if(e.match(i)||e.match(o))return"operator";if(e.match(s))return"atom";var n,r=e.match(d);if(r)return t.doInCurrentLine?t.doInCurrentLine=!1:t.currentIndent++,"proc"==r[0]||"fun"==r[0]?t.tokenize=b:"class"==r[0]?t.tokenize=k:"meth"==r[0]&&(t.tokenize=p),"keyword";if(e.match(l)||e.match(f))return"keyword";if(e.match(h))return t.currentIndent--,"keyword";var u=e.next();if('"'==u||"'"==u){return n=u,t.tokenize=function(e,t){for(var r,a=!1,o=!1;null!=(r=e.next());){if(r==n&&!a){o=!0;break}a=!a&&"\\"==r}return(o||!a)&&(t.tokenize=m),"string"},t.tokenize(e,t)}if(/[~\d]/.test(u)){if("~"==u){if(!/^[0-9]/.test(e.peek()))return null;else if("0"==e.next()&&e.match(/^[xX][0-9a-fA-F]+/)||e.match(/^[0-9]*(\.[0-9]+)?([eE][~+]?[0-9]+)?/))return"number"}return"0"==u&&e.match(/^[xX][0-9a-fA-F]+/)||e.match(/^[0-9]*(\.[0-9]+)?([eE][~+]?[0-9]+)?/)?"number":null}return"%"==u?(e.skipToEnd(),"comment"):"/"==u&&e.eat("*")?(t.tokenize=z,z(e,t)):a.test(u)?"operator":(e.eatWhile(/\w/),"variable")}function k(e,t){return e.eatSpace()?null:(e.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)/),t.tokenize=m,"type")}function p(e,t){return e.eatSpace()?null:(e.match(/([a-zA-Z][A-Za-z0-9_]*)|(`.+`)/),t.tokenize=m,"def")}function b(e,t){return e.eatSpace()?null:!t.hasPassedFirstStage&&e.eat("{")?(t.hasPassedFirstStage=!0,"bracket"):t.hasPassedFirstStage?(e.match(/([A-Z][A-Za-z0-9_]*)|(`.+`)|\$/),t.hasPassedFirstStage=!1,t.tokenize=m,"def"):(t.tokenize=m,null)}function z(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=m;break}r="*"==n}return"comment"}let g={name:"oz",startState:function(){return{tokenize:m,currentIndent:0,doInCurrentLine:!1,hasPassedFirstStage:!1}},token:function(e,t){return e.sol()&&(t.doInCurrentLine=0),t.tokenize(e,t)},indent:function(e,t,n){var r=t.replace(/^\s+|\s+$/g,"");return r.match(h)||r.match(l)||r.match(/(\[])/)?n.unit*(e.currentIndent-1):e.currentIndent<0?0:e.currentIndent*n.unit},languageData:{indentOnInut:RegExp("[\\[\\]]|("+u.concat(c).join("|")+")$"),commentTokens:{line:"%",block:{open:"/*",close:"*/"}}}}}}]);