try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="baa80510-de78-4655-9280-ad58610345ff",e._sentryDebugIdIdentifier="sentry-dbid-baa80510-de78-4655-9280-ad58610345ff")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5264],{5264:(e,t,n)=>{n.r(t),n.d(t,{solr:()=>u});var r=/[^\s\|\!\+\-\*\?\~\^\&\:\(\)\[\]\{\}\"\\]/,o=/[\|\!\+\-\*\?\~\^\&]/,a=/^(OR|AND|NOT|TO)$/;function i(e,t){var n,u=e.next();return'"'==u?t.tokenize=function(e,t){for(var n,r=!1;null!=(n=e.next())&&(n!=u||r);)r=!r&&"\\"==n;return r||(t.tokenize=i),"string"}:o.test(u)?t.tokenize=function(e,t){return"|"==u?e.eat(/\|/):"&"==u&&e.eat(/\&/),t.tokenize=i,"operator"}:r.test(u)&&(n=u,t.tokenize=function(e,t){for(var o,u=n;(n=e.peek())&&null!=n.match(r);)u+=e.next();return(t.tokenize=i,a.test(u))?"operator":parseFloat(o=u).toString()===o?"number":":"==e.peek()?"propertyName":"string"}),t.tokenize!=i?t.tokenize(e,t):null}let u={name:"solr",startState:function(){return{tokenize:i}},token:function(e,t){return e.eatSpace()?null:t.tokenize(e,t)}}}}]);