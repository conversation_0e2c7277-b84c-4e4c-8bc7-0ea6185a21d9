try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="73ead41c-1f2d-4b81-8217-721673a4d181",e._sentryDebugIdIdentifier="sentry-dbid-73ead41c-1f2d-4b81-8217-721673a4d181")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65],{4662:(e,t,s)=>{s.d(t,{Fc:()=>l,TN:()=>c,XL:()=>d});var a=s(95155);s(12115);var n=s(83101),r=s(64269);let i=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(i({variant:s}),t),...n,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,r.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...s,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},12800:(e,t,s)=>{s.d(t,{Xi:()=>c,av:()=>o,j7:()=>d,tU:()=>l});var a=s(95155),n=s(12115),r=s(25667),i=s(64269);let l=r.bL,d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...n})});d.displayName=r.B8.displayName;let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",s),...n})});c.displayName=r.l9.displayName;let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(r.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})});o.displayName=r.UC.displayName},14883:(e,t,s)=>{s.d(t,{$v:()=>h,EO:()=>u,Lt:()=>l,Rx:()=>y,Zr:()=>f,ck:()=>x,r7:()=>p,tv:()=>d,wd:()=>m});var a=s(95155);s(12115);var n=s(35646),r=s(64269),i=s(20764);function l(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"alert-dialog",...t,"data-sentry-element":"AlertDialogPrimitive.Root","data-sentry-component":"AlertDialog","data-sentry-source-file":"alert-dialog.tsx"})}function d(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"alert-dialog-trigger",...t,"data-sentry-element":"AlertDialogPrimitive.Trigger","data-sentry-component":"AlertDialogTrigger","data-sentry-source-file":"alert-dialog.tsx"})}function c(e){let{...t}=e;return(0,a.jsx)(n.ZL,{"data-slot":"alert-dialog-portal",...t,"data-sentry-element":"AlertDialogPrimitive.Portal","data-sentry-component":"AlertDialogPortal","data-sentry-source-file":"alert-dialog.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)(n.hJ,{"data-slot":"alert-dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s,"data-sentry-element":"AlertDialogPrimitive.Overlay","data-sentry-component":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"})}function u(e){let{className:t,...s}=e;return(0,a.jsxs)(c,{"data-sentry-element":"AlertDialogPortal","data-sentry-component":"AlertDialogContent","data-sentry-source-file":"alert-dialog.tsx",children:[(0,a.jsx)(o,{"data-sentry-element":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"}),(0,a.jsx)(n.UC,{"data-slot":"alert-dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...s,"data-sentry-element":"AlertDialogPrimitive.Content","data-sentry-source-file":"alert-dialog.tsx"})]})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s,"data-sentry-component":"AlertDialogHeader","data-sentry-source-file":"alert-dialog.tsx"})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s,"data-sentry-component":"AlertDialogFooter","data-sentry-source-file":"alert-dialog.tsx"})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(n.hE,{"data-slot":"alert-dialog-title",className:(0,r.cn)("text-lg font-semibold",t),...s,"data-sentry-element":"AlertDialogPrimitive.Title","data-sentry-component":"AlertDialogTitle","data-sentry-source-file":"alert-dialog.tsx"})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(n.VY,{"data-slot":"alert-dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-element":"AlertDialogPrimitive.Description","data-sentry-component":"AlertDialogDescription","data-sentry-source-file":"alert-dialog.tsx"})}function y(e){let{className:t,...s}=e;return(0,a.jsx)(n.rc,{className:(0,r.cn)((0,i.r)(),t),...s,"data-sentry-element":"AlertDialogPrimitive.Action","data-sentry-component":"AlertDialogAction","data-sentry-source-file":"alert-dialog.tsx"})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(n.ZD,{className:(0,r.cn)((0,i.r)({variant:"outline"}),t),...s,"data-sentry-element":"AlertDialogPrimitive.Cancel","data-sentry-component":"AlertDialogCancel","data-sentry-source-file":"alert-dialog.tsx"})}},26737:(e,t,s)=>{s.d(t,{k:()=>l});var a=s(95155),n=s(12115),r=s(9484),i=s(64269);let l=n.forwardRef((e,t)=>{let{className:s,value:n,...l}=e;return(0,a.jsx)(r.bL,{ref:t,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...l,children:(0,a.jsx)(r.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});l.displayName=r.bL.displayName},38004:(e,t,s)=>{s.d(t,{Cf:()=>u,Es:()=>x,L3:()=>p,c7:()=>m,lG:()=>l,rr:()=>h,zM:()=>d});var a=s(95155);s(12115);var n=s(89511),r=s(65229),i=s(64269);function l(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function d(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function c(e){let{...t}=e;return(0,a.jsx)(n.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u(e){let{className:t,children:s,...l}=e;return(0,a.jsxs)(c,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(o,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...l,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[s,(0,a.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(r.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...s,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},42526:(e,t,s)=>{s.d(t,{J:()=>i});var a=s(95155);s(12115);var n=s(10489),r=s(64269);function i(e){let{className:t,...s}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},46201:(e,t,s)=>{s.d(t,{Separator:()=>l});var a=s(95155),n=s(12115),r=s(57268),i=s(64269);let l=n.forwardRef((e,t)=>{let{className:s,orientation:n="horizontal",decorative:l=!0,...d}=e;return(0,a.jsx)(r.b,{ref:t,decorative:l,orientation:n,className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",s),...d})});l.displayName=r.b.displayName},47254:(e,t,s)=>{s.d(t,{T:()=>r});var a=s(95155);s(12115);var n=s(64269);function r(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},47886:(e,t,s)=>{s.d(t,{WG:()=>n,cl:()=>i,qs:()=>a});let a={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},n=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},r=()=>{let e=a.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let t=r();return t?t.role!==e?(window.location.href=n(t),null):t:null}},66094:(e,t,s)=>{s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>r,aR:()=>i,wL:()=>o});var a=s(95155);s(12115);var n=s(64269);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...s,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...s,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},79676:(e,t,s)=>{s.d(t,{q:()=>eB});var a=s(95155),n=s(12115),r=s(20764),i=s(66094),l=s(26737),d=s(46201),c=s(5917),o=s(90368),u=s(27937),m=s(64269),x=s(31936),p=s(42526),h=s(47254),y=s(25532),f=s(88021),j=s(44466),g=s(6387),v=s(97655),b=s(71163),N=s(72226),k=s(43152),w=s(65229),C=s(21786),D=s(23327),z=s(16485),A=s(18720);function S(e){let{data:t,onUpdate:s}=e,[i,l]=(0,n.useState)(!1),[d,c]=(0,n.useState)(!!(t.startDate||t.endDate)),o=(0,n.useRef)(null);return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"BasicInfoStep","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"courseName","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Nama Course *"}),(0,a.jsx)(x.p,{id:"courseName",placeholder:"Masukkan nama course",value:t.name,onChange:e=>s({name:e.target.value}),"data-sentry-element":"Input","data-sentry-source-file":"basic-info-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"instructor","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Nama Instruktur *"}),(0,a.jsx)(x.p,{id:"instructor",placeholder:"Masukkan nama instruktur",value:t.instructor,onChange:e=>s({instructor:e.target.value}),"data-sentry-element":"Input","data-sentry-source-file":"basic-info-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"courseCode","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Kode Course *"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"courseCode",placeholder:"Kode unik untuk course",value:t.courseCode,onChange:e=>s({courseCode:e.target.value.toUpperCase()}),className:"flex-1","data-sentry-element":"Input","data-sentry-source-file":"basic-info-step.tsx"}),(0,a.jsxs)(r.$,{type:"button",variant:"outline",onClick:()=>{l(!0),setTimeout(()=>{s({courseCode:Math.random().toString(36).substring(2,8).toUpperCase()}),l(!1),A.oR.success("Kode course berhasil dibuat")},1e3)},disabled:i,"data-sentry-element":"Button","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(k.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Shuffle","data-sentry-source-file":"basic-info-step.tsx"}),i?"Membuat...":"Generate"]})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Kode ini akan digunakan siswa untuk mendaftar ke course"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"description","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Deskripsi Course *"}),(0,a.jsx)(h.T,{id:"description",placeholder:"Jelaskan tentang course ini...",value:t.description,onChange:e=>s({description:e.target.value}),rows:4,"data-sentry-element":"Textarea","data-sentry-source-file":"basic-info-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{"data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Cover Image"}),t.coverImagePreview?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:t.coverImagePreview,alt:"Course cover",className:"w-full h-auto object-cover rounded-md aspect-video"}),(0,a.jsx)(r.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:()=>{t.coverImagePreview&&URL.revokeObjectURL(t.coverImagePreview),s({coverImage:void 0,coverImagePreview:void 0})},children:(0,a.jsx)(w.A,{className:"w-4 h-4"})})]}):(0,a.jsxs)("div",{className:"border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors aspect-video flex flex-col items-center justify-center",onClick:()=>{var e;return null==(e=o.current)?void 0:e.click()},children:[(0,a.jsx)(C.A,{className:"w-8 h-8 mx-auto mb-2 text-muted-foreground"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Klik untuk upload cover image"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"PNG, JPG hingga 5MB"})]}),(0,a.jsx)("input",{ref:o,type:"file",accept:"image/*",onChange:e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;if(!a.type.startsWith("image/"))return void A.oR.error("File harus berupa gambar");if(a.size>5242880)return void A.oR.error("Ukuran file maksimal 5MB");let n=URL.createObjectURL(a);s({coverImage:a,coverImagePreview:n}),A.oR.success("Gambar berhasil diunggah")},className:"hidden"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.J,{"data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Tipe Course *"}),(0,a.jsxs)(g.AM,{"data-sentry-element":"Popover","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(g.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"h-auto p-1","data-sentry-element":"Button","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(D.A,{className:"h-4 w-4 text-muted-foreground","data-sentry-element":"Info","data-sentry-source-file":"basic-info-step.tsx"})})}),(0,a.jsx)(g.hl,{className:"w-96",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Informasi Tipe Course"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(f.E,{variant:"secondary","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Self-paced"})}),(0,a.jsxs)("ul",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsx)("li",{children:"• Siswa belajar dengan kecepatan sendiri"}),(0,a.jsx)("li",{children:"• Tidak ada deadline ketat"}),(0,a.jsx)("li",{children:"• Akses selamanya setelah enrollment"}),(0,a.jsx)("li",{children:"• Cocok untuk pembelajaran mandiri"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(f.E,{variant:"default","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Verified"})}),(0,a.jsxs)("ul",{className:"text-xs text-muted-foreground space-y-1",children:[(0,a.jsx)("li",{children:"• Course dengan jadwal dan deadline"}),(0,a.jsx)("li",{children:"• Sertifikat resmi setelah selesai"}),(0,a.jsx)("li",{children:"• Monitoring progress lebih ketat"}),(0,a.jsx)("li",{children:"• Cocok untuk pembelajaran formal"})]})]})]})]})})]})]}),(0,a.jsxs)(y.l6,{value:t.type,onValueChange:e=>s({type:e}),"data-sentry-element":"Select","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(y.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(y.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"basic-info-step.tsx"})}),(0,a.jsxs)(y.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(y.eb,{value:"self_paced","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.E,{variant:"secondary","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Self-paced"}),(0,a.jsx)("span",{children:"Siswa belajar dengan kecepatan sendiri"})]})}),(0,a.jsx)(y.eb,{value:"verified","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.E,{variant:"default","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Verified"}),(0,a.jsx)("span",{children:"Course dengan jadwal dan deadline"})]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{"data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Tipe Pendaftaran *"}),(0,a.jsxs)(y.l6,{value:t.enrollmentType,onValueChange:e=>{let a={enrollmentType:e};"purchase"!==e&&"both"!==e||t.currency||(a.currency="IDR"),s(a)},"data-sentry-element":"Select","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(y.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsx)(y.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"basic-info-step.tsx"})}),(0,a.jsxs)(y.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"basic-info-step.tsx",children:[(0,a.jsx)(y.eb,{value:"code","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Kode"}),(0,a.jsx)("span",{children:"Siswa mendaftar dengan kode"})]})}),(0,a.jsx)(y.eb,{value:"invitation","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Undangan"}),(0,a.jsx)("span",{children:"Hanya dengan undangan"})]})}),(0,a.jsx)(y.eb,{value:"both","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Keduanya"}),(0,a.jsx)("span",{children:"Kode atau undangan"})]})}),(0,a.jsx)(y.eb,{value:"purchase","data-sentry-element":"SelectItem","data-sentry-source-file":"basic-info-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.E,{variant:"default","data-sentry-element":"Badge","data-sentry-source-file":"basic-info-step.tsx",children:"Berbayar"}),(0,a.jsx)("span",{children:"Siswa harus membeli"})]})})]})]})]}),("purchase"===t.enrollmentType||"both"===t.enrollmentType)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,a.jsx)(p.J,{htmlFor:"price",children:"Harga *"}),(0,a.jsx)(x.p,{id:"price",type:"number",placeholder:"0",value:t.price||"",onChange:e=>s({price:parseFloat(e.target.value)||0}),min:"0",step:"1000"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{children:"Mata Uang *"}),(0,a.jsxs)(y.l6,{value:t.currency||"IDR",onValueChange:e=>s({currency:e}),children:[(0,a.jsx)(y.bq,{children:(0,a.jsx)(y.yv,{})}),(0,a.jsxs)(y.gC,{children:[(0,a.jsx)(y.eb,{value:"IDR",children:"IDR (Rupiah)"}),(0,a.jsx)(y.eb,{value:"USD",children:"USD (Dollar)"}),(0,a.jsx)(y.eb,{value:"EUR",children:"EUR (Euro)"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.S,{id:"enableDateRange",checked:d,onCheckedChange:e=>{c(e),e||s({startDate:null,endDate:null})},"data-sentry-element":"Checkbox","data-sentry-source-file":"basic-info-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"enableDateRange","data-sentry-element":"Label","data-sentry-source-file":"basic-info-step.tsx",children:"Atur Tanggal Mulai & Selesai"})]}),d&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{children:"Tanggal Mulai"}),(0,a.jsxs)(g.AM,{children:[(0,a.jsx)(g.Wv,{asChild:!0,children:(0,a.jsxs)(r.$,{variant:"outline",className:(0,m.cn)("w-full justify-start text-left font-normal",!t.startDate&&"text-muted-foreground"),children:[(0,a.jsx)(z.A,{className:"mr-2 h-4 w-4"}),t.startDate?(0,b.GP)(t.startDate,"PPP",{locale:N.id}):"Pilih tanggal mulai"]})}),(0,a.jsx)(g.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(j.V,{mode:"single",selected:t.startDate||void 0,onSelect:e=>s({startDate:e}),disabled:e=>e<new Date,initialFocus:!0})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{children:"Tanggal Selesai"}),(0,a.jsxs)(g.AM,{children:[(0,a.jsx)(g.Wv,{asChild:!0,children:(0,a.jsxs)(r.$,{variant:"outline",className:(0,m.cn)("w-full justify-start text-left font-normal",!t.endDate&&"text-muted-foreground"),children:[(0,a.jsx)(z.A,{className:"mr-2 h-4 w-4"}),t.endDate?(0,b.GP)(t.endDate,"PPP",{locale:N.id}):"Pilih tanggal selesai"]})}),(0,a.jsx)(g.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(j.V,{mode:"single",selected:t.endDate||void 0,onSelect:e=>s({endDate:e}),disabled:e=>!!(e<new Date||t.startDate&&e<=t.startDate),initialFocus:!0})})]})]})]})]})]})}var T=s(27599);function P(e){let{className:t,...s}=e;return(0,a.jsx)(T.bL,{"data-slot":"switch",className:(0,m.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,"data-sentry-element":"SwitchPrimitive.Root","data-sentry-component":"Switch","data-sentry-source-file":"switch.tsx",children:(0,a.jsx)(T.zi,{"data-slot":"switch-thumb",className:(0,m.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0"),"data-sentry-element":"SwitchPrimitive.Thumb","data-sentry-source-file":"switch.tsx"})})}var E=s(38004),B=s(14883),q=s(6191),I=s(47937),L=s(49476),R=s(80021),F=s(11010),M=s(71360),$=s(24033),Q=s(89715);function U(e){var t,s,l,d;let{data:c,onUpdate:o}=e,[m,y]=(0,n.useState)(new Set),[j,g]=(0,n.useState)(null),[v,b]=(0,n.useState)({moduleId:"",chapter:null}),[N,k]=(0,n.useState)(!1),[w,C]=(0,n.useState)(!1),D=e=>{let t=new Set(m);t.has(e)?t.delete(e):t.add(e),y(t)},z=()=>{g({id:"module-".concat(Date.now()),name:"",description:"",orderIndex:c.modules.length,chapters:[],hasModuleQuiz:!1}),k(!0)},S=e=>{g({...e}),k(!0)},T=e=>{o({modules:c.modules.filter(t=>t.id!==e).map((e,t)=>({...e,orderIndex:t}))}),A.oR.success("Modul berhasil dihapus")},U=e=>{let t=c.modules.find(t=>t.id===e);t&&(b({moduleId:e,chapter:{id:"chapter-".concat(Date.now()),name:"",content:[],orderIndex:t.chapters.length,hasChapterQuiz:!1}}),C(!0))},J=(e,t)=>{b({moduleId:e,chapter:{...t}}),C(!0)},Z=(e,t)=>{o({modules:c.modules.map(s=>{if(s.id===e){let e=s.chapters.filter(e=>e.id!==t).map((e,t)=>({...e,orderIndex:t}));return{...s,chapters:e}}return s})}),A.oR.success("Chapter berhasil dihapus")},O=(e,t)=>{let s=c.modules.findIndex(t=>t.id===e);if(-1===s)return;let a="up"===t?s-1:s+1;if(a<0||a>=c.modules.length)return;let n=[...c.modules];[n[s],n[a]]=[n[a],n[s]],n.forEach((e,t)=>{e.orderIndex=t}),o({modules:n})};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ModuleStructureStep","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Struktur Modul Course"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Buat modul dan chapter untuk mengorganisir konten course"})]}),(0,a.jsxs)(r.$,{onClick:z,"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Plus","data-sentry-source-file":"module-structure-step.tsx"}),"Tambah Modul"]})]}),0===c.modules.length?(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-12",children:[(0,a.jsx)(I.A,{className:"w-12 h-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Belum ada modul"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:"Mulai dengan membuat modul pertama untuk course Anda"}),(0,a.jsxs)(r.$,{onClick:z,children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2"}),"Buat Modul Pertama"]})]})}):(0,a.jsx)("div",{className:"space-y-4",children:c.modules.map((e,t)=>{let s=m.has(e.id);return(0,a.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(i.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 text-muted-foreground cursor-move"}),(0,a.jsxs)(f.E,{variant:"outline",children:["Modul ",t+1]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{className:"text-base",children:e.name||"Modul Tanpa Nama"}),e.description&&(0,a.jsx)(i.BT,{className:"mt-1",children:e.description})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.hasModuleQuiz&&(0,a.jsxs)(f.E,{variant:"secondary",children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),"Quiz Modul"]}),(0,a.jsxs)(f.E,{variant:"outline",children:[e.chapters.length," Chapter"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>O(e.id,"up"),disabled:0===t,children:"↑"}),(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>O(e.id,"down"),disabled:t===c.modules.length-1,children:"↓"}),(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>S(e),children:(0,a.jsx)(F.A,{className:"w-4 h-4"})}),(0,a.jsxs)(B.Lt,{children:[(0,a.jsx)(B.tv,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(B.EO,{children:[(0,a.jsxs)(B.wd,{children:[(0,a.jsx)(B.r7,{children:"Hapus Modul"}),(0,a.jsxs)(B.$v,{children:["Apakah Anda yakin ingin menghapus modul “",e.name,"”? Semua chapter di dalam modul ini juga akan terhapus."]})]}),(0,a.jsxs)(B.ck,{children:[(0,a.jsx)(B.Zr,{children:"Batal"}),(0,a.jsx)(B.Rx,{onClick:()=>T(e.id),children:"Hapus"})]})]})]}),(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>D(e.id),children:s?(0,a.jsx)($.A,{className:"w-4 h-4"}):(0,a.jsx)(u.A,{className:"w-4 h-4"})})]})]})]})}),s&&(0,a.jsx)(i.Wu,{className:"pt-0",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Chapters"}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>U(e.id),children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2"}),"Tambah Chapter"]})]}),0===e.chapters.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(Q.A,{className:"w-8 h-8 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"Belum ada chapter"})]}):(0,a.jsx)("div",{className:"space-y-2",children:e.chapters.map((t,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(L.A,{className:"w-4 h-4 text-muted-foreground cursor-move"}),(0,a.jsx)(f.E,{variant:"outline",className:"text-xs",children:s+1}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:t.name||"Chapter Tanpa Nama"}),t.hasChapterQuiz&&(0,a.jsxs)(f.E,{variant:"secondary",className:"text-xs mt-1",children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),"Quiz"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>J(e.id,t),children:(0,a.jsx)(F.A,{className:"w-4 h-4"})}),(0,a.jsxs)(B.Lt,{children:[(0,a.jsx)(B.tv,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(B.EO,{children:[(0,a.jsxs)(B.wd,{children:[(0,a.jsx)(B.r7,{children:"Hapus Chapter"}),(0,a.jsxs)(B.$v,{children:["Apakah Anda yakin ingin menghapus chapter “",t.name,"”?"]})]}),(0,a.jsxs)(B.ck,{children:[(0,a.jsx)(B.Zr,{children:"Batal"}),(0,a.jsx)(B.Rx,{onClick:()=>Z(e.id,t.id),children:"Hapus"})]})]})]})]})]},t.id))})]})})]},e.id)})}),(0,a.jsx)(E.lG,{open:N,onOpenChange:k,"data-sentry-element":"Dialog","data-sentry-source-file":"module-structure-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"module-structure-step.tsx",children:(null==j?void 0:j.name)?"Edit Modul":"Tambah Modul Baru"}),(0,a.jsx)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"module-structure-step.tsx",children:"Isi informasi dasar untuk modul ini"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"moduleName","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Nama Modul *"}),(0,a.jsx)(x.p,{id:"moduleName",placeholder:"Masukkan nama modul",value:(null==j?void 0:j.name)||"",onChange:e=>g(t=>t?{...t,name:e.target.value}:null),"data-sentry-element":"Input","data-sentry-source-file":"module-structure-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"moduleDescription","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Deskripsi"}),(0,a.jsx)(h.T,{id:"moduleDescription",placeholder:"Jelaskan tentang modul ini...",value:(null==j?void 0:j.description)||"",onChange:e=>g(t=>t?{...t,description:e.target.value}:null),rows:3,"data-sentry-element":"Textarea","data-sentry-source-file":"module-structure-step.tsx"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(P,{id:"hasModuleQuiz",checked:(null==j?void 0:j.hasModuleQuiz)||!1,onCheckedChange:e=>g(t=>t?{...t,hasModuleQuiz:e}:null),"data-sentry-element":"Switch","data-sentry-source-file":"module-structure-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"hasModuleQuiz","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Tambahkan quiz di akhir modul"})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(r.$,{variant:"outline",onClick:()=>k(!1),"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:"Batal"}),(0,a.jsxs)(r.$,{onClick:()=>{if(!j||!j.name.trim())return void A.oR.error("Nama modul harus diisi");let e=[...c.modules],t=e.findIndex(e=>e.id===j.id);t>=0?(e[t]=j,A.oR.success("Modul berhasil diperbarui")):(e.push(j),A.oR.success("Modul berhasil ditambahkan")),o({modules:e}),k(!1),g(null)},"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:[(null==j?void 0:j.name)?"Perbarui":"Tambah"," Modul"]})]})]})}),(0,a.jsx)(E.lG,{open:w,onOpenChange:C,"data-sentry-element":"Dialog","data-sentry-source-file":"module-structure-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"module-structure-step.tsx",children:(null==(t=v.chapter)?void 0:t.name)?"Edit Chapter":"Tambah Chapter Baru"}),(0,a.jsx)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"module-structure-step.tsx",children:"Isi informasi dasar untuk chapter ini"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"chapterName","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Nama Chapter *"}),(0,a.jsx)(x.p,{id:"chapterName",placeholder:"Masukkan nama chapter",value:(null==(s=v.chapter)?void 0:s.name)||"",onChange:e=>b(t=>({...t,chapter:t.chapter?{...t.chapter,name:e.target.value}:null})),"data-sentry-element":"Input","data-sentry-source-file":"module-structure-step.tsx"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(P,{id:"hasChapterQuiz",checked:(null==(l=v.chapter)?void 0:l.hasChapterQuiz)||!1,onCheckedChange:e=>b(t=>({...t,chapter:t.chapter?{...t.chapter,hasChapterQuiz:e}:null})),"data-sentry-element":"Switch","data-sentry-source-file":"module-structure-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"hasChapterQuiz","data-sentry-element":"Label","data-sentry-source-file":"module-structure-step.tsx",children:"Tambahkan quiz untuk chapter ini"})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"module-structure-step.tsx",children:[(0,a.jsx)(r.$,{variant:"outline",onClick:()=>C(!1),"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:"Batal"}),(0,a.jsxs)(r.$,{onClick:()=>{if(!v.chapter||!v.chapter.name.trim())return void A.oR.error("Nama chapter harus diisi");o({modules:c.modules.map(e=>{if(e.id===v.moduleId){let t=[...e.chapters],s=t.findIndex(e=>e.id===v.chapter.id);return s>=0?t[s]=v.chapter:t.push(v.chapter),{...e,chapters:t}}return e})}),C(!1),b({moduleId:"",chapter:null}),A.oR.success("Chapter berhasil disimpan")},"data-sentry-element":"Button","data-sentry-source-file":"module-structure-step.tsx",children:[(null==(d=v.chapter)?void 0:d.name)?"Perbarui":"Tambah"," Chapter"]})]})]})}),c.modules.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-lg",children:"Ringkasan Struktur"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:c.modules.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Modul"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:c.modules.reduce((e,t)=>e+t.chapters.length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Chapter"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:c.modules.filter(e=>e.hasModuleQuiz).length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Quiz Modul"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:c.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.hasChapterQuiz).length,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Quiz Chapter"})]})]})})]})]})}var J=s(50859),Z=s(48800),O=s(42196),_=s(23896),H=s(60890),K=s(15239);let W=(0,s(67909).default)(()=>Promise.all([s.e(6079),s.e(7520),s.e(2999),s.e(9516),s.e(7364)]).then(s.bind(s,47364)),{loadableGenerated:{webpack:()=>[47364]},ssr:!1,loading:()=>(0,a.jsx)("div",{className:"min-h-[200px] flex items-center justify-center",children:"Loading editor..."})});function G(e){let{initialContent:t,onContentChange:s,allowImages:l=!0,placeholder:d,contentRefs:c}=e,[o,u]=(0,n.useState)(t),[m,y]=(0,n.useState)(!1),[f,j]=(0,n.useState)("image"),[g,v]=(0,n.useState)("");n.useEffect(()=>{u(t)},[t]);let b=e=>{let t=[...o,{id:"block-".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,9)),type:e,value:""}];u(t),s(t)},N=e=>{j(e),y(!0),v("")},k=(e,t)=>{let a=o.map(s=>s.id===e?{...s,value:t}:s);u(a),s(a)},w=e=>{let t=o.filter(t=>t.id!==e);u(t),s(t)},D=(0,n.useCallback)(async(e,t,s)=>{if(!e||0===e.length)return void A.oR.error("No file selected for upload.");let a=e[0];A.oR.info("Uploading ".concat(a.name,"..."));try{let e=await fetch("/api/upload?filename=".concat(a.name),{method:"POST",body:a});if(!e.ok)throw Error("Upload failed: ".concat(e.statusText));let n=await e.json();k(t,n.url),A.oR.success("".concat(s.charAt(0).toUpperCase()+s.slice(1)," uploaded successfully!"))}catch(e){console.error("Error uploading ".concat(s,":"),e),A.oR.error("Failed to upload ".concat(s,": ").concat(e.message))}},[k]);return(0,a.jsxs)("div",{className:"space-y-4","data-sentry-component":"DynamicContentEditor","data-sentry-source-file":"dynamic-content-editor.tsx",children:[o.map((e,t)=>(0,a.jsxs)(i.Zp,{className:"relative p-4 mb-4 scroll-mt-4",ref:s=>{c&&(c.current[e.id||"block-".concat(t)]=s)},id:e.id||"block-".concat(t),children:["text"===e.type?(0,a.jsx)("div",{className:"mdx-editor-wrapper",children:(0,a.jsx)(W,{markdown:e.value||"",onChange:t=>k(e.id,t),placeholder:d||"Enter your content here...",className:"min-h-[200px]"})}):"image"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("div",{className:"relative w-full h-48 border rounded-md overflow-hidden",children:(0,a.jsx)(K.default,{src:e.value,alt:"Uploaded content",layout:"fill",objectFit:"contain",className:"rounded-md"})}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan gambar:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="image/*",t.onchange=t=>{let s=t.target.files;s&&D(Array.from(s),e.id,"image")},t.click()},children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL gambar:");t&&k(e.id,t)},children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):"video"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("video",{controls:!0,src:e.value,className:"w-full h-auto max-h-96 rounded-md"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan video:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="video/*",t.onchange=t=>{let s=t.target.files;s&&D(Array.from(s),e.id,"video")},t.click()},children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL video:");t&&k(e.id,t)},children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):"pdf"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("iframe",{src:e.value,className:"w-full h-96 rounded-md"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan PDF:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="application/pdf",t.onchange=t=>{let s=t.target.files;s&&D(Array.from(s),e.id,"pdf")},t.click()},children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL PDF:");t&&k(e.id,t)},children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):"zoom-recording"===e.type?(0,a.jsx)("div",{className:"space-y-2",children:e.value?(0,a.jsx)("iframe",{src:e.value,className:"w-full h-96 rounded-md"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground mb-2",children:"Pilih cara menambahkan Zoom Recording:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=document.createElement("input");t.type="file",t.accept="video/*",t.onchange=t=>{let s=t.target.files;s&&D(Array.from(s),e.id,"zoom-recording")},t.click()},children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Upload File"]}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let t=prompt("Masukkan URL Zoom Recording:");t&&k(e.id,t)},children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2"}),"Dari Link"]})]})]})}):(0,a.jsx)(h.T,{placeholder:"Enter ".concat(e.type," URL"),value:e.value,onChange:t=>k(e.id,t.target.value),rows:3}),(0,a.jsx)(r.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 text-muted-foreground hover:text-destructive",onClick:()=>w(e.id),children:(0,a.jsx)(M.A,{className:"h-4 w-4"})})]},e.id)),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 pt-2",children:[(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>b("text"),size:"sm","data-sentry-element":"Button","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsx)(Z.A,{className:"h-4 w-4 mr-2","data-sentry-element":"TextIcon","data-sentry-source-file":"dynamic-content-editor.tsx"})," Add Text Block"]}),l&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>N("image"),size:"sm",children:[(0,a.jsx)(O.A,{className:"h-4 w-4 mr-2"})," Add Image Block"]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>N("video"),size:"sm",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"})," Add Video Block"]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>N("pdf"),size:"sm",children:[(0,a.jsx)(Q.A,{className:"h-4 w-4 mr-2"})," Add PDF Block"]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>N("zoom-recording"),size:"sm",children:[(0,a.jsx)(H.A,{className:"h-4 w-4 mr-2"})," Add Zoom Recording Block"]})]})]}),(0,a.jsx)(E.lG,{open:m,onOpenChange:y,"data-sentry-element":"Dialog","data-sentry-source-file":"dynamic-content-editor.tsx",children:(0,a.jsxs)(E.Cf,{"data-sentry-element":"DialogContent","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsxs)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"dynamic-content-editor.tsx",children:["Tambah ",f.charAt(0).toUpperCase()+f.slice(1)," Block"]}),(0,a.jsxs)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"dynamic-content-editor.tsx",children:["Pilih cara menambahkan ",f,": upload file atau masukkan link."]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(p.J,{htmlFor:"link-url","data-sentry-element":"Label","data-sentry-source-file":"dynamic-content-editor.tsx",children:["URL ",f.charAt(0).toUpperCase()+f.slice(1)]}),(0,a.jsx)(x.p,{id:"link-url",placeholder:"Masukkan URL ".concat(f,"..."),value:g,onChange:e=>v(e.target.value),"data-sentry-element":"Input","data-sentry-source-file":"dynamic-content-editor.tsx"})]})}),(0,a.jsxs)(E.Es,{className:"flex gap-2","data-sentry-element":"DialogFooter","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{b(f),y(!1)},"data-sentry-element":"Button","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2","data-sentry-element":"Upload","data-sentry-source-file":"dynamic-content-editor.tsx"}),"Upload File"]}),(0,a.jsxs)(r.$,{onClick:()=>{if(g.trim()){let e=[...o,{id:"block-".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,9)),type:f,value:g.trim()}];u(e),s(e),y(!1),v(""),A.oR.success("Block berhasil ditambahkan dari link!")}else A.oR.error("Silakan masukkan URL yang valid")},disabled:!g.trim(),"data-sentry-element":"Button","data-sentry-source-file":"dynamic-content-editor.tsx",children:[(0,a.jsx)(J.A,{className:"h-4 w-4 mr-2","data-sentry-element":"Link","data-sentry-source-file":"dynamic-content-editor.tsx"}),"Gunakan Link"]})]})]})})]})}var V=s(40583),X=s(92001),Y=s(42529),ee=s(26983),et=s(76561),es=s(57828),ea=s(46046),en=s(7053),er=s(67047);function ei(e){var t,s,l,d,c,o,u,j,g,b;let{data:N,onUpdate:k}=e,[w,C]=(0,n.useState)((null==(t=N.modules[0])?void 0:t.id)||""),[D,z]=(0,n.useState)(""),[S,T]=(0,n.useState)({type:"chapter",quiz:null}),[P,L]=(0,n.useState)(!1),[$,U]=(0,n.useState)(null),[J,Z]=(0,n.useState)(!1),[_,K]=(0,n.useState)(!1),W=(0,n.useRef)({}),ei=N.modules.find(e=>e.id===w),el=null==ei?void 0:ei.chapters.find(e=>e.id===D),ed=e=>{let t=W.current[e];t&&t.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})},ec=e=>{switch(e){case"text":return(0,a.jsx)(V.A,{className:"w-3 h-3"});case"image":return(0,a.jsx)(O.A,{className:"w-3 h-3"});case"video":case"zoom-recording":return(0,a.jsx)(H.A,{className:"w-3 h-3"});case"pdf":return(0,a.jsx)(Q.A,{className:"w-3 h-3"});default:return(0,a.jsx)(X.A,{className:"w-3 h-3"})}},eo=e=>{if("text"===e.type){var t;return(null==(t=e.value)?void 0:t.slice(0,30))+(e.value&&e.value.length>30?"...":"")||"Empty text"}return e.type.charAt(0).toUpperCase()+e.type.slice(1)},eu=e=>{let t={id:"quiz-".concat(Date.now()),name:"chapter"===e?"Quiz ".concat(null==el?void 0:el.name):"module"===e?"Quiz ".concat(null==ei?void 0:ei.name):"Final Exam - ".concat(N.name),description:"",questions:[],minimumScore:70,timeLimit:"final"===e?120:void 0};T({type:e,quiz:t}),L(!0)},em=(e,t)=>{T({type:e,quiz:{...t}}),L(!0)},ex=e=>{U({...e}),Z(!0)},ep=e=>{if(!S.quiz)return;let t=S.quiz.questions.filter(t=>t.id!==e).map((e,t)=>({...e,orderIndex:t}));T(e=>({...e,quiz:e.quiz?{...e.quiz,questions:t}:null})),A.oR.success("Pertanyaan berhasil dihapus")},eh=(()=>{let e=N.modules.reduce((e,t)=>e+t.chapters.length,0),t=N.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.content&&e.content.length>0).length,0);return{total:e,completed:t,percentage:e>0?Math.round(t/e*100):0}})();return 0===N.modules.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(I.A,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Belum ada modul"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Kembali ke langkah sebelumnya untuk membuat struktur modul terlebih dahulu"})]}):(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ContentCreationStep","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Pembuatan Konten"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Tambahkan konten dan quiz untuk setiap chapter"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-medium",children:[eh.completed," / ",eh.total," Chapter"]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[eh.percentage,"% selesai"]})]}),(0,a.jsx)("div",{className:(0,m.cn)("w-12 h-12 rounded-full flex items-center justify-center",100===eh.percentage?"bg-green-100 text-green-600":"bg-muted text-muted-foreground"),children:100===eh.percentage?(0,a.jsx)(Y.A,{className:"w-6 h-6"}):(0,a.jsx)(ee.A,{className:"w-6 h-6"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsx)(i.ZB,{className:"text-base","data-sentry-element":"CardTitle","data-sentry-source-file":"content-creation-step.tsx",children:"Navigasi Konten"})}),(0,a.jsxs)(i.Wu,{className:"space-y-4 max-h-[70vh] overflow-y-auto","data-sentry-element":"CardContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("div",{className:"p-3 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm text-primary",children:"Final Exam"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Ujian akhir untuk seluruh course"})]}),N.finalExam&&(0,a.jsx)(Y.A,{className:"w-4 h-4 text-green-600"})]}),(0,a.jsxs)(r.$,{variant:N.finalExam?"outline":"default",size:"sm",className:"w-full",onClick:()=>{N.finalExam?em("final",N.finalExam):eu("final")},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(R.A,{className:"w-4 h-4 mr-2","data-sentry-element":"HelpCircle","data-sentry-source-file":"content-creation-step.tsx"}),N.finalExam?"Edit Final Exam":"Buat Final Exam"]})]})}),N.modules.map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:(0,m.cn)("p-2 rounded-lg cursor-pointer transition-colors",w===e.id?"bg-primary text-primary-foreground":"bg-muted hover:bg-muted/80"),onClick:()=>{C(e.id),z("")},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:e.name}),(0,a.jsxs)("div",{className:"text-xs opacity-75",children:[e.chapters.length," chapters"]})]}),e.moduleQuiz&&(0,a.jsxs)(f.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),"Quiz"]})]})}),w===e.id&&(0,a.jsxs)("div",{className:"ml-4 space-y-2",children:[(0,a.jsx)("div",{className:"p-2 rounded bg-secondary/50",children:(0,a.jsxs)(r.$,{variant:e.moduleQuiz?"outline":"secondary",size:"sm",className:"w-full text-xs",onClick:()=>{e.moduleQuiz?em("module",e.moduleQuiz):eu("module")},children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),e.moduleQuiz?"Edit Module Quiz":"Buat Module Quiz"]})}),e.chapters.map(e=>{let t=e.content&&e.content.length>0;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:(0,m.cn)("p-2 rounded text-xs cursor-pointer transition-colors flex items-center justify-between",D===e.id?"bg-primary/20 text-primary":"hover:bg-muted/50"),onClick:()=>z(e.id),children:[(0,a.jsx)("span",{children:e.name}),t&&(0,a.jsx)(Y.A,{className:"w-3 h-3 text-green-600"})]}),D===e.id&&t&&e.content&&(0,a.jsxs)("div",{className:"ml-4 space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-muted-foreground mb-1",children:[(0,a.jsx)(et.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Content Blocks"})]}),e.content.map((e,t)=>(0,a.jsxs)("button",{onClick:()=>ed(e.id||"block-".concat(t)),className:"w-full text-left p-1.5 rounded text-xs hover:bg-primary/10 transition-colors flex items-center space-x-2",children:[ec(e.type),(0,a.jsxs)("span",{className:"truncate flex-1",children:[t+1,". ",eo(e)]})]},e.id||t))]})]},e.id)})]})]},e.id))]})]})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:D?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:null==el?void 0:el.name}),(null==el?void 0:el.hasChapterQuiz)&&(0,a.jsxs)(f.E,{variant:"secondary",children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),"Chapter Quiz"]}),(null==ei?void 0:ei.moduleQuiz)&&(0,a.jsxs)(f.E,{variant:"outline",children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),"Module Quiz"]})]}),(0,a.jsxs)(i.BT,{children:["Modul: ",null==ei?void 0:ei.name,(null==ei?void 0:ei.moduleQuiz)&&(0,a.jsx)("span",{className:"ml-2 text-xs text-primary",children:"• Module ini memiliki quiz"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>K(!_),children:[(0,a.jsx)(es.A,{className:"w-4 h-4 mr-2"}),_?"Edit":"Preview"]}),(null==ei?void 0:ei.moduleQuiz)&&(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>em("module",ei.moduleQuiz),children:[(0,a.jsx)(R.A,{className:"w-4 h-4 mr-2"}),"Edit Module Quiz"]}),(null==el?void 0:el.hasChapterQuiz)&&(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{el.chapterQuiz?em("chapter",el.chapterQuiz):eu("chapter")},children:[(0,a.jsx)(R.A,{className:"w-4 h-4 mr-2"}),el.chapterQuiz?"Edit Chapter Quiz":"Buat Chapter Quiz"]})]})]})})}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{className:"text-base",children:"Konten Chapter"}),(0,a.jsx)(i.BT,{children:_?"Preview konten seperti yang akan dilihat siswa":"Gunakan Markdown untuk memformat konten"})]}),(0,a.jsx)(i.Wu,{children:_?(0,a.jsx)("div",{className:"max-h-[60vh] overflow-y-auto prose max-w-none pr-4",children:(null==el?void 0:el.content)&&el.content.length>0?(0,a.jsx)(a.Fragment,{children:el.content.map((e,t)=>(0,a.jsxs)("div",{ref:s=>{W.current[e.id||"block-".concat(t)]=s},className:"mb-6 scroll-mt-4",id:e.id||"block-".concat(t),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2 py-1 px-2 bg-muted/30 rounded text-xs text-muted-foreground",children:[ec(e.type),(0,a.jsxs)("span",{children:["Content-",t+1]}),(0,a.jsxs)("span",{children:["(",e.type,")"]})]}),"text"===e.type?(0,a.jsx)(en.oz,{remarkPlugins:[er.A],components:{h1:e=>{let{node:t,...s}=e;return(0,a.jsx)("h1",{className:"mb-4 text-2xl font-bold text-gray-900",...s})},h2:e=>{let{node:t,...s}=e;return(0,a.jsx)("h2",{className:"mb-3 text-xl font-semibold text-gray-800",...s})},h3:e=>{let{node:t,...s}=e;return(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-800",...s})},h4:e=>{let{node:t,...s}=e;return(0,a.jsx)("h4",{className:"mb-2 text-base font-semibold text-gray-700",...s})},p:e=>{let{node:t,...s}=e;return(0,a.jsx)("p",{className:"mb-3 leading-relaxed",...s})},ul:e=>{let{node:t,...s}=e;return(0,a.jsx)("ul",{className:"mb-3 ml-4 list-disc",...s})},ol:e=>{let{node:t,...s}=e;return(0,a.jsx)("ol",{className:"mb-3 ml-4 list-decimal",...s})},li:e=>{let{node:t,...s}=e;return(0,a.jsx)("li",{className:"mb-1",...s})},blockquote:e=>{let{node:t,...s}=e;return(0,a.jsx)("blockquote",{className:"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic",...s})},code:e=>{let{node:t,className:s,children:n,...r}=e;return/language-(\w+)/.exec(s||"")?(0,a.jsx)("code",{className:"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100",...r,children:n}):(0,a.jsx)("code",{className:"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm",...r,children:n})},pre:e=>{let{node:t,...s}=e;return(0,a.jsx)("pre",{className:"mb-4",...s})},table:e=>{let{node:t,...s}=e;return(0,a.jsx)("div",{className:"mb-4 overflow-x-auto",children:(0,a.jsx)("table",{className:"min-w-full rounded border border-gray-200",...s})})},thead:e=>{let{node:t,...s}=e;return(0,a.jsx)("thead",{className:"bg-gray-50",...s})},th:e=>{let{node:t,...s}=e;return(0,a.jsx)("th",{className:"border border-gray-200 px-3 py-2 text-left font-semibold",...s})},td:e=>{let{node:t,...s}=e;return(0,a.jsx)("td",{className:"border border-gray-200 px-3 py-2",...s})},hr:e=>{let{node:t,...s}=e;return(0,a.jsx)("hr",{className:"my-6 border-gray-300",...s})},strong:e=>{let{node:t,...s}=e;return(0,a.jsx)("strong",{className:"font-semibold text-gray-900",...s})},em:e=>{let{node:t,...s}=e;return(0,a.jsx)("em",{className:"italic",...s})}},children:e.value}):"image"===e.type?(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)("img",{src:e.value,alt:"Content",className:"max-w-full h-auto rounded-md"})}):"video"===e.type?(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)("video",{src:e.value,controls:!0,className:"max-w-full rounded-md"})}):"pdf"===e.type?(0,a.jsx)("div",{className:"my-4",children:(0,a.jsx)("iframe",{src:e.value,className:"w-full h-96 rounded-md",title:"PDF Content"})}):(0,a.jsx)("div",{className:"my-4 p-4 bg-muted rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["zoom-recording"===e.type?"Zoom Recording: ":"File: ",(0,a.jsx)("a",{href:e.value,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:e.value})]})})]},e.id||t))}):(0,a.jsx)("p",{className:"text-muted-foreground italic",children:"Belum ada konten untuk chapter ini"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"max-h-[60vh] overflow-y-auto pr-4",children:(0,a.jsx)(G,{initialContent:(null==el?void 0:el.content)||[],onContentChange:e=>{ei&&el&&k({modules:N.modules.map(t=>{if(t.id===w){let s=t.chapters.map(t=>t.id===D?{...t,content:e}:t);return{...t,chapters:s}}return t})})},contentRefs:W})}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:"Mendukung Markdown formatting"}),(0,a.jsxs)("span",{children:[(null==el||null==(s=el.content)?void 0:s.length)||0," blok konten"]})]})]})})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(R.A,{className:"w-5 h-5 text-primary"}),(0,a.jsx)("span",{children:"Final Exam"}),N.finalExam&&(0,a.jsx)(f.E,{variant:"secondary",children:"Sudah dibuat"})]}),(0,a.jsx)(i.BT,{children:"Ujian akhir untuk menguji pemahaman siswa terhadap seluruh materi course"})]}),(0,a.jsx)(i.Wu,{children:N.finalExam?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:N.finalExam.questions.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Pertanyaan"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary",children:[N.finalExam.minimumScore,"%"]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Nilai Minimum"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:N.finalExam.questions.reduce((e,t)=>e+t.points,0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Poin"})]}),N.finalExam.timeLimit&&(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:N.finalExam.timeLimit}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Menit"})]})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsxs)(r.$,{onClick:()=>em("final",N.finalExam),className:"flex-1",children:[(0,a.jsx)(F.A,{className:"w-4 h-4 mr-2"}),"Edit Final Exam"]})})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(R.A,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Belum ada Final Exam"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Final Exam adalah ujian akhir yang menguji pemahaman siswa terhadap seluruh materi course"}),(0,a.jsxs)(r.$,{onClick:()=>eu("final"),children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2"}),"Buat Final Exam"]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Overview Modul"}),(0,a.jsx)(i.BT,{children:"Pilih chapter dari navigasi di sebelah kiri untuk mulai menambahkan konten"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:N.modules.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.name}),e.moduleQuiz&&(0,a.jsxs)(f.E,{variant:"secondary",children:[(0,a.jsx)(R.A,{className:"w-3 h-3 mr-1"}),"Quiz"]})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground mb-3",children:[e.chapters.length," chapters"]}),(0,a.jsx)("div",{className:"space-y-1",children:e.chapters.map(e=>{let t=e.content&&e.content.length>0;return(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsx)("span",{children:e.name}),t?(0,a.jsx)(Y.A,{className:"w-3 h-3 text-green-600"}):(0,a.jsx)(ee.A,{className:"w-3 h-3 text-muted-foreground"})]},e.id)})})]},e.id))})})]})]})})]}),(0,a.jsx)(E.lG,{open:P,onOpenChange:L,"data-sentry-element":"Dialog","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-4xl max-h-[80vh] overflow-y-auto p-6","data-sentry-element":"DialogContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsxs)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"content-creation-step.tsx",children:(null==(l=S.quiz)?void 0:l.questions.length)?"Edit Quiz":"Buat Quiz Baru"}),(0,a.jsx)(E.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"content-creation-step.tsx",children:"Buat pertanyaan untuk menguji pemahaman siswa"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"quizName","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Nama Quiz *"}),(0,a.jsx)(x.p,{id:"quizName",placeholder:"Masukkan nama quiz",value:(null==(d=S.quiz)?void 0:d.name)||"",onChange:e=>T(t=>({...t,quiz:t.quiz?{...t.quiz,name:e.target.value}:null})),"data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"minimumScore","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Nilai Minimum (%)"}),(0,a.jsx)(x.p,{id:"minimumScore",type:"number",min:"0",max:"100",value:(null==(c=S.quiz)?void 0:c.minimumScore)||70,onChange:e=>T(t=>({...t,quiz:t.quiz?{...t.quiz,minimumScore:parseInt(e.target.value)}:null})),"data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"timeLimit","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Batas Waktu (menit)"}),(0,a.jsx)(x.p,{id:"timeLimit",type:"number",min:"1",value:(null==(o=S.quiz)?void 0:o.timeLimit)||"",onChange:e=>T(t=>({...t,quiz:t.quiz?{...t.quiz,timeLimit:e.target.value?parseInt(e.target.value):void 0}:null})),placeholder:"Tanpa batas waktu","data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"quizDescription","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Deskripsi"}),(0,a.jsx)(h.T,{id:"quizDescription",placeholder:"Jelaskan tentang quiz ini...",value:(null==(u=S.quiz)?void 0:u.description)||"",onChange:e=>T(t=>({...t,quiz:t.quiz?{...t.quiz,description:e.target.value}:null})),rows:2,"data-sentry-element":"Textarea","data-sentry-source-file":"content-creation-step.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold",children:"Pertanyaan"}),(0,a.jsxs)(r.$,{onClick:()=>{var e;U({id:(null==$?void 0:$.id)||"question-".concat(Date.now()),type:"multiple_choice",question:[{type:"text",value:""}],options:(null==$?void 0:$.type)==="true_false"?[{content:[{type:"text",value:"True"}],isCorrect:!1},{content:[{type:"text",value:"False"}],isCorrect:!1}]:[{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1}],essayAnswer:"",explanation:[],points:1,orderIndex:(null==(e=S.quiz)?void 0:e.questions.length)||0}),Z(!0)},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Plus","data-sentry-source-file":"content-creation-step.tsx"}),"Tambah Pertanyaan"]})]}),(null==(j=S.quiz)?void 0:j.questions.length)===0?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(R.A,{className:"w-8 h-8 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"Belum ada pertanyaan"})]}):(0,a.jsx)("div",{className:"space-y-3",children:null==(g=S.quiz)?void 0:g.questions.map((e,t)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(f.E,{variant:"outline",children:t+1}),(0,a.jsx)(f.E,{variant:"secondary",children:"multiple_choice"===e.type?"Pilihan Ganda":"true_false"===e.type?"Benar/Salah":"Essay"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.points," poin"]})]}),(0,a.jsx)("div",{className:"text-sm",children:e.question.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:["text"===e.type&&(0,a.jsx)("p",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:"Question image ".concat(t),className:"max-w-xs max-h-32 object-contain mt-2"})]},t))}),"multiple_choice"===e.type&&e.options&&(0,a.jsx)("div",{className:"mt-2 space-y-1",children:e.options.map((e,t)=>(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[String.fromCharCode(65+t),".",e.content.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:"Option image ".concat(t),className:"inline-block max-h-8 object-contain ml-1"})]},t))]},t))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>ex(e),children:(0,a.jsx)(F.A,{className:"w-4 h-4"})}),(0,a.jsxs)(B.Lt,{children:[(0,a.jsx)(B.tv,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})})}),(0,a.jsxs)(B.EO,{children:[(0,a.jsxs)(B.wd,{children:[(0,a.jsx)(B.r7,{children:"Hapus Pertanyaan"}),(0,a.jsx)(B.$v,{children:"Apakah Anda yakin ingin menghapus pertanyaan ini?"})]}),(0,a.jsxs)(B.ck,{children:[(0,a.jsx)(B.Zr,{children:"Batal"}),(0,a.jsx)(B.Rx,{onClick:()=>ep(e.id),children:"Hapus"})]})]})]})]})]})})},e.id))})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(r.$,{variant:"outline",onClick:()=>L(!1),"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:"Batal"}),(0,a.jsxs)(r.$,{onClick:()=>{if(!S.quiz||!S.quiz.name.trim())return void A.oR.error("Nama quiz harus diisi");"final"===S.type?k({finalExam:S.quiz}):k({modules:N.modules.map(e=>{if(e.id===w)if("module"===S.type)return{...e,moduleQuiz:S.quiz};else{let t=e.chapters.map(e=>e.id===D?{...e,chapterQuiz:S.quiz}:e);return{...e,chapters:t}}return e})}),L(!1),T({type:"chapter",quiz:null}),A.oR.success("Quiz berhasil disimpan")},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(ea.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Save","data-sentry-source-file":"content-creation-step.tsx"}),"Simpan Quiz"]})]})]})}),(0,a.jsx)(E.lG,{open:J,onOpenChange:Z,"data-sentry-element":"Dialog","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsxs)(E.Cf,{className:"sm:max-w-2xl max-h-[80vh] overflow-y-auto p-6","data-sentry-element":"DialogContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(E.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsx)(E.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"content-creation-step.tsx",children:(null==$?void 0:$.question)?"Edit Pertanyaan":"Tambah Pertanyaan Baru"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"questionType","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Tipe Pertanyaan"}),(0,a.jsxs)(y.l6,{value:(null==$?void 0:$.type)||"multiple_choice",onValueChange:e=>{U(t=>{if(!t)return null;let s={...t,type:e};return"true_false"===e?s.options=[{content:[{type:"text",value:"True"}],isCorrect:!1},{content:[{type:"text",value:"False"}],isCorrect:!1}]:"multiple_choice"===e?s.options=[{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1},{content:[{type:"text",value:""}],isCorrect:!1}]:s.options=void 0,s})},"data-sentry-element":"Select","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(y.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"content-creation-step.tsx",children:(0,a.jsx)(y.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"content-creation-step.tsx"})}),(0,a.jsxs)(y.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(y.eb,{value:"multiple_choice","data-sentry-element":"SelectItem","data-sentry-source-file":"content-creation-step.tsx",children:"Pilihan Ganda"}),(0,a.jsx)(y.eb,{value:"true_false","data-sentry-element":"SelectItem","data-sentry-source-file":"content-creation-step.tsx",children:"Benar/Salah"}),(0,a.jsx)(y.eb,{value:"essay","data-sentry-element":"SelectItem","data-sentry-source-file":"content-creation-step.tsx",children:"Essay"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"questionPoints","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Poin"}),(0,a.jsx)(x.p,{id:"questionPoints",type:"number",min:"1",value:(null==$?void 0:$.points)||1,onChange:e=>U(t=>t?{...t,points:parseInt(e.target.value)}:null),"data-sentry-element":"Input","data-sentry-source-file":"content-creation-step.tsx"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"questionText","data-sentry-element":"Label","data-sentry-source-file":"content-creation-step.tsx",children:"Pertanyaan *"}),(0,a.jsx)(G,{initialContent:(null==$?void 0:$.question)||[],onContentChange:e=>U(t=>t?{...t,question:e}:null),allowImages:!0,"data-sentry-element":"DynamicContentEditor","data-sentry-source-file":"content-creation-step.tsx"})]}),((null==$?void 0:$.type)==="multiple_choice"||(null==$?void 0:$.type)==="true_false")&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(p.J,{children:"Pilihan Jawaban"}),null==(b=$.options)?void 0:b.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col space-y-2 border p-3 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["multiple_choice"===$.type&&(0,a.jsxs)("span",{className:"text-sm font-medium w-6",children:[String.fromCharCode(65+t),"."]}),"multiple_choice"===$.type?(0,a.jsx)(G,{initialContent:e.content||[],onContentChange:e=>{let s=[...$.options||[]];s[t]={...s[t],content:e},U(e=>e?{...e,options:s}:null)},allowImages:!0}):(0,a.jsx)("span",{className:"text-base font-medium",children:e.content[0].value})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsx)(v.S,{id:"option-correct-".concat(t),checked:e.isCorrect,onCheckedChange:e=>{let s=[...$.options||[]];s[t]={...s[t],isCorrect:e},U(e=>e?{...e,options:s}:null)}}),(0,a.jsx)(p.J,{htmlFor:"option-correct-".concat(t),children:"Jawaban Benar"})]})]},t))]}),$&&"essay"===$.type&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"essay-answer",children:"Jawaban Esai"}),(0,a.jsx)(h.T,{id:"essay-answer",placeholder:"Masukkan jawaban esai untuk pertanyaan ini",value:$.essayAnswer||"",onChange:e=>U(t=>t?{...t,essayAnswer:e.target.value}:null),rows:4})]}),$&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"explanation",children:"Penjelasan Jawaban (Opsional)"}),(0,a.jsx)(G,{initialContent:(null==$?void 0:$.explanation)||[],onContentChange:e=>{U(t=>t?{...t,explanation:e}:null)},placeholder:"Jelaskan jawaban yang benar atau berikan informasi tambahan",allowImages:!0})]})]}),(0,a.jsxs)(E.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"content-creation-step.tsx",children:[(0,a.jsx)(r.$,{variant:"outline",onClick:()=>Z(!1),"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:"Batal"}),(0,a.jsxs)(r.$,{onClick:()=>{if(!$||0===$.question.length||"text"===$.question[0].type&&!$.question[0].value.trim())return void A.oR.error("Pertanyaan harus diisi");if(!S.quiz)return;let e=[...S.quiz.questions],t=e.findIndex(e=>e.id===$.id);t>=0?e[t]=$:e.push($),T(t=>({...t,quiz:t.quiz?{...t.quiz,questions:e}:null})),Z(!1),U(null),A.oR.success("Pertanyaan berhasil disimpan")},"data-sentry-element":"Button","data-sentry-source-file":"content-creation-step.tsx",children:[(null==$?void 0:$.question)?"Perbarui":"Tambah"," Pertanyaan"]})]})]})})]})}var el=s(4662),ed=s(20639),ec=s(6132),eo=s(30814),eu=s(9602);function em(e){let{data:t,onPublish:s,isPublishing:c}=e,[o,u]=(0,n.useState)(!1),x=(()=>{let e=[];e.push({id:"course-name",label:"Nama Course",status:t.name.trim()?"complete":"incomplete",description:t.name.trim()?'"'.concat(t.name,'"'):"Nama course harus diisi",required:!0}),e.push({id:"course-description",label:"Deskripsi Course",status:t.description.trim()?"complete":"incomplete",description:t.description.trim()?"".concat(t.description.length," karakter"):"Deskripsi course harus diisi",required:!0}),e.push({id:"course-code",label:"Kode Course",status:t.courseCode.trim()?"complete":"incomplete",description:t.courseCode.trim()?t.courseCode:"Kode course harus diisi",required:!0}),e.push({id:"cover-image",label:"Cover Image",status:t.coverImage?"complete":"warning",description:t.coverImage?"Cover image telah diupload":"Disarankan menambahkan cover image",required:!1}),e.push({id:"course-dates",label:"Tanggal Course",status:t.startDate&&t.endDate?"complete":"warning",description:t.startDate&&t.endDate?"".concat(new Date(t.startDate).toLocaleDateString()," - ").concat(new Date(t.endDate).toLocaleDateString()):"Tanggal mulai dan selesai belum diatur",required:!1});let s=t.modules.length;e.push({id:"modules",label:"Struktur Modul",status:s>0?"complete":"incomplete",description:s>0?"".concat(s," modul telah dibuat"):"Minimal 1 modul harus dibuat",required:!0});let a=t.modules.reduce((e,t)=>e+t.chapters.length,0);e.push({id:"chapters",label:"Chapter",status:a>0?"complete":"incomplete",description:a>0?"".concat(a," chapter telah dibuat"):"Minimal 1 chapter harus dibuat",required:!0});let n=t.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.content&&e.content.length>0).length,0);e.push({id:"content",label:"Konten Chapter",status:n===a?"complete":n>0?"warning":"incomplete",description:"".concat(n," dari ").concat(a," chapter memiliki konten"),required:!0});let r=t.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.hasChapterQuiz&&e.chapterQuiz).length,0),i=t.modules.filter(e=>e.hasModuleQuiz&&e.moduleQuiz).length;return e.push({id:"quizzes",label:"Quiz",status:r>0||i>0?"complete":"warning",description:"".concat(r," chapter quiz, ").concat(i," module quiz"),required:!1}),e.push({id:"final-exam",label:"Final Exam",status:t.finalExam?"complete":"warning",description:t.finalExam?"".concat(t.finalExam.questions.length," pertanyaan"):"Final exam belum dibuat",required:!1}),e})(),p=x.filter(e=>e.required),h=p.filter(e=>"complete"===e.status).length,y=h===p.length,j=x.filter(e=>"complete"===e.status).length,g=Math.round(j/x.length*100),v=(()=>{let e=t.modules.reduce((e,t)=>e+t.chapters.length,0),s=t.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.hasChapterQuiz).length+ +!!t.hasModuleQuiz,0)+ +!!t.finalExam,a=t.modules.reduce((e,t)=>e+t.chapters.reduce((e,t)=>e+5*Math.ceil(t.content.filter(e=>"text"===e.type).reduce((e,t)=>e+t.value.length,0)/1e3),0),0);return{modules:t.modules.length,chapters:e,quizzes:s,estimatedDuration:Math.max(a,30)}})(),b=async()=>{if(!y)return void A.oR.error("Lengkapi semua item yang wajib diisi terlebih dahulu");try{await s(),A.oR.success("Course berhasil dipublikasi!")}catch(e){A.oR.error("Gagal mempublikasi course")}};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"PublishingStep","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("div",{className:(0,m.cn)("w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",y?"bg-green-100 text-green-600":"bg-orange-100 text-orange-600"),children:y?(0,a.jsx)(ed.A,{className:"w-8 h-8"}):(0,a.jsx)(ec.A,{className:"w-8 h-8"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold",children:y?"Siap untuk Dipublikasi!":"Hampir Selesai"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:y?"Course Anda sudah siap untuk dipublikasi dan dapat diakses oleh siswa":"Lengkapi beberapa item berikut untuk mempublikasi course"})]}),(0,a.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(eo.A,{className:"w-5 h-5","data-sentry-element":"Target","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:"Progress Kelengkapan"})]}),(0,a.jsxs)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"publishing-step.tsx",children:[j," dari ",x.length," item selesai"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[g,"%"]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Selesai"})]})]})}),(0,a.jsxs)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(l.k,{value:g,className:"mb-4","data-sentry-element":"Progress","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(I.A,{className:"w-5 h-5","data-sentry-element":"BookOpen","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:v.modules}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Modul"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(Q.A,{className:"w-5 h-5","data-sentry-element":"FileText","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:v.chapters}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Chapter"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-purple-100 text-purple-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(R.A,{className:"w-5 h-5","data-sentry-element":"HelpCircle","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:v.quizzes}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Quiz"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-orange-100 text-orange-600 rounded-full mx-auto mb-2",children:(0,a.jsx)(ee.A,{className:"w-5 h-5","data-sentry-element":"Clock","data-sentry-source-file":"publishing-step.tsx"})}),(0,a.jsx)("div",{className:"text-sm font-medium",children:v.estimatedDuration}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"Menit"})]})]})]})]}),(0,a.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(Y.A,{className:"w-5 h-5","data-sentry-element":"CheckCircle","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:"Checklist Publikasi"})]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>u(!o),"data-sentry-element":"Button","data-sentry-source-file":"publishing-step.tsx",children:[o?"Sembunyikan":"Lihat"," Detail"]})]})}),(0,a.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsx)("div",{className:"space-y-3",children:x.map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:(0,m.cn)("w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5","complete"===e.status?"bg-green-100 text-green-600":"warning"===e.status?"bg-orange-100 text-orange-600":"bg-gray-100 text-gray-400"),children:"complete"===e.status?(0,a.jsx)(Y.A,{className:"w-3 h-3"}):"warning"===e.status?(0,a.jsx)(ec.A,{className:"w-3 h-3"}):(0,a.jsx)("div",{className:"w-2 h-2 bg-current rounded-full"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:(0,m.cn)("text-sm font-medium","complete"===e.status?"text-green-700":"warning"===e.status?"text-orange-700":"text-gray-500"),children:e.label}),e.required&&(0,a.jsx)(f.E,{variant:"destructive",className:"text-xs px-1 py-0",children:"Wajib"})]}),o&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.description})]})]},e.id))})})]}),(0,a.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsxs)(i.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(es.A,{className:"w-5 h-5","data-sentry-element":"Eye","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:"Preview Course"})]}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"publishing-step.tsx",children:"Begini tampilan course Anda untuk siswa"})]}),(0,a.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"publishing-step.tsx",children:(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[t.coverImage?(0,a.jsx)("img",{src:"string"==typeof t.coverImage?t.coverImage:URL.createObjectURL(t.coverImage),alt:t.name,className:"w-20 h-20 object-cover rounded-lg"}):(0,a.jsx)("div",{className:"w-20 h-20 bg-muted rounded-lg flex items-center justify-center",children:(0,a.jsx)(O.A,{className:"w-8 h-8 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-semibold text-lg",children:t.name||"Nama Course"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:t.description||"Deskripsi course"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(eu.A,{className:"w-3 h-3","data-sentry-element":"Code","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsx)("span",{children:t.courseCode||"COURSE-CODE"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(I.A,{className:"w-3 h-3","data-sentry-element":"BookOpen","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("span",{children:[v.modules," Modul"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(ee.A,{className:"w-3 h-3","data-sentry-element":"Clock","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("span",{children:["~",v.estimatedDuration," Menit"]})]}),t.startDate&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(z.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:new Date(t.startDate).toLocaleDateString()})]})]})]})]}),(0,a.jsx)(d.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"publishing-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h5",{className:"font-medium text-sm",children:"Struktur Course:"}),t.modules.length>0?(0,a.jsxs)("div",{className:"space-y-2",children:[t.modules.slice(0,3).map((e,t)=>(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{className:"font-medium",children:[t+1,". ",e.name]}),(0,a.jsxs)("div",{className:"ml-4 text-xs text-muted-foreground",children:[e.chapters.length," chapter",e.hasModuleQuiz&&" • Quiz modul"]})]},e.id)),t.modules.length>3&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["... dan ",t.modules.length-3," modul lainnya"]})]}):(0,a.jsx)("p",{className:"text-sm text-muted-foreground italic",children:"Belum ada modul"})]})]})})]}),!y&&(0,a.jsxs)(el.Fc,{children:[(0,a.jsx)(ec.A,{className:"h-4 w-4"}),(0,a.jsxs)(el.TN,{children:[(0,a.jsx)("strong",{children:"Perhatian:"})," Beberapa item wajib belum lengkap. Course tidak dapat dipublikasi sampai semua item wajib diselesaikan."]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-6",children:[(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:y?"Course siap dipublikasi dan dapat diakses siswa":"".concat(h,"/").concat(p.length," item wajib selesai")}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(r.$,{variant:"outline",disabled:c,"data-sentry-element":"Button","data-sentry-source-file":"publishing-step.tsx",children:[(0,a.jsx)(es.A,{className:"w-4 h-4 mr-2","data-sentry-element":"Eye","data-sentry-source-file":"publishing-step.tsx"}),"Preview"]}),(0,a.jsx)(r.$,{onClick:b,disabled:!y||c,className:"min-w-[120px]","data-sentry-element":"Button","data-sentry-source-file":"publishing-step.tsx",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Publishing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed.A,{className:"w-4 h-4 mr-2"}),"Publikasi Course"]})})]})]})]})}var ex=s(12800),ep=s(87066);function eh(e){let{data:t,onUpdate:s}=e,l=t.admissions||{requirements:[],applicationDeadline:"",prerequisites:[]},[d,c]=(0,n.useState)(""),[o,u]=(0,n.useState)(""),m=(e,t)=>{s({admissions:{...l,[e]:t}})},h=()=>{""===d.trim()||l.requirements.includes(d.trim())||(m("requirements",[...l.requirements,d.trim()]),c(""))},y=e=>{m("requirements",l.requirements.filter((t,s)=>s!==e))},j=()=>{""===o.trim()||l.prerequisites.includes(o.trim())||(m("prerequisites",[...l.prerequisites,o.trim()]),u(""))},g=e=>{m("prerequisites",l.prerequisites.filter((t,s)=>s!==e))};return(0,a.jsxs)(i.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"AdmissionsStep","data-sentry-source-file":"admissions-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"admissions-step.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"admissions-step.tsx",children:"Informasi Pendaftaran"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"admissions-step.tsx",children:"Detail terkait persyaratan pendaftaran dan prasyarat kursus."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"admissions-step.tsx",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ep.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"ClipboardList","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newRequirement","data-sentry-element":"Label","data-sentry-source-file":"admissions-step.tsx",children:"Persyaratan"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newRequirement",value:d,onChange:e=>c(e.target.value),placeholder:"Tambahkan persyaratan baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),h())},"data-sentry-element":"Input","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:h,"data-sentry-element":"Button","data-sentry-source-file":"admissions-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.requirements.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>y(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Calendar","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"applicationDeadline","data-sentry-element":"Label","data-sentry-source-file":"admissions-step.tsx",children:"Batas Waktu Pendaftaran"})]}),(0,a.jsx)(x.p,{id:"applicationDeadline",type:"text",value:l.applicationDeadline,onChange:e=>m("applicationDeadline",e.target.value),placeholder:"Contoh: 2024-12-31","data-sentry-element":"Input","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(I.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"BookOpen","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newPrerequisite","data-sentry-element":"Label","data-sentry-source-file":"admissions-step.tsx",children:"Prasyarat"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newPrerequisite",value:o,onChange:e=>u(e.target.value),placeholder:"Tambahkan prasyarat baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),j())},"data-sentry-element":"Input","data-sentry-source-file":"admissions-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:j,"data-sentry-element":"Button","data-sentry-source-file":"admissions-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.prerequisites.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>g(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}var ey=s(28446),ef=s(97415),ej=s(52472);function eg(e){let{data:t,onUpdate:s}=e,l=t.academics||{credits:0,workload:"",assessment:[]},[d,c]=(0,n.useState)(""),o=(e,t)=>{s({academics:{...l,[e]:t}})},u=()=>{""===d.trim()||l.assessment.includes(d.trim())||(o("assessment",[...l.assessment,d.trim()]),c(""))},m=e=>{o("assessment",l.assessment.filter((t,s)=>s!==e))};return(0,a.jsxs)(i.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"AcademicsStep","data-sentry-source-file":"academics-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"academics-step.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"academics-step.tsx",children:"Informasi Akademik"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"academics-step.tsx",children:"Detail terkait struktur akademik dan penilaian kursus."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"academics-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ey.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Book","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"credits","data-sentry-element":"Label","data-sentry-source-file":"academics-step.tsx",children:"Kredit"})]}),(0,a.jsx)(x.p,{id:"credits",type:"number",value:l.credits,onChange:e=>o("credits",parseInt(e.target.value)),placeholder:"Contoh: 12","data-sentry-element":"Input","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ef.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Hourglass","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"workload","data-sentry-element":"Label","data-sentry-source-file":"academics-step.tsx",children:"Beban Kerja"})]}),(0,a.jsx)(x.p,{id:"workload",type:"text",value:l.workload,onChange:e=>o("workload",e.target.value),placeholder:"Contoh: 12-15 jam/minggu","data-sentry-element":"Input","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ej.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Award","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newAssessment","data-sentry-element":"Label","data-sentry-source-file":"academics-step.tsx",children:"Penilaian"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newAssessment",value:d,onChange:e=>c(e.target.value),placeholder:"Tambahkan metode penilaian baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),u())},"data-sentry-element":"Input","data-sentry-source-file":"academics-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:u,"data-sentry-element":"Button","data-sentry-source-file":"academics-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.assessment.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>m(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}var ev=s(91761),eb=s(78519),eN=s(60709);function ek(e){let{data:t,onUpdate:s}=e,l=t.tuitionAndFinancing||{totalCost:0,paymentOptions:[],scholarships:[]},[d,c]=(0,n.useState)(""),[o,u]=(0,n.useState)(""),m=(e,t)=>{s({tuitionAndFinancing:{...l,[e]:t}})},h=()=>{""===d.trim()||l.paymentOptions.includes(d.trim())||(m("paymentOptions",[...l.paymentOptions,d.trim()]),c(""))},y=e=>{m("paymentOptions",l.paymentOptions.filter((t,s)=>s!==e))},j=()=>{""===o.trim()||l.scholarships.includes(o.trim())||(m("scholarships",[...l.scholarships,o.trim()]),u(""))},g=e=>{m("scholarships",l.scholarships.filter((t,s)=>s!==e))};return(0,a.jsxs)(i.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"TuitionFinancingStep","data-sentry-source-file":"tuition-financing-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"tuition-financing-step.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"tuition-financing-step.tsx",children:"Biaya & Pembiayaan"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"tuition-financing-step.tsx",children:"Detail terkait biaya kursus, opsi pembayaran, dan peluang beasiswa."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"tuition-financing-step.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ev.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"DollarSign","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"totalCost","data-sentry-element":"Label","data-sentry-source-file":"tuition-financing-step.tsx",children:"Total Biaya"})]}),(0,a.jsx)(x.p,{id:"totalCost",type:"number",value:l.totalCost,onChange:e=>m("totalCost",parseFloat(e.target.value)),placeholder:"Contoh: 6000000","data-sentry-element":"Input","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eb.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"CreditCard","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newPaymentOption","data-sentry-element":"Label","data-sentry-source-file":"tuition-financing-step.tsx",children:"Opsi Pembayaran"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newPaymentOption",value:d,onChange:e=>c(e.target.value),placeholder:"Tambahkan opsi pembayaran baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),h())},"data-sentry-element":"Input","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:h,"data-sentry-element":"Button","data-sentry-source-file":"tuition-financing-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.paymentOptions.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>y(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eN.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Gift","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newScholarship","data-sentry-element":"Label","data-sentry-source-file":"tuition-financing-step.tsx",children:"Beasiswa"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newScholarship",value:o,onChange:e=>u(e.target.value),placeholder:"Tambahkan beasiswa baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),j())},"data-sentry-element":"Input","data-sentry-source-file":"tuition-financing-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:j,"data-sentry-element":"Button","data-sentry-source-file":"tuition-financing-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.scholarships.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>g(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}var ew=s(29633),eC=s(71871);function eD(e){let{data:t,onUpdate:s}=e,l=t.careers||{outcomes:[],industries:[],averageSalary:""},[d,c]=(0,n.useState)(""),[o,u]=(0,n.useState)(""),m=(e,t)=>{s({careers:{...l,[e]:t}})},h=()=>{""===d.trim()||l.outcomes.includes(d.trim())||(m("outcomes",[...l.outcomes,d.trim()]),c(""))},y=e=>{m("outcomes",l.outcomes.filter((t,s)=>s!==e))},j=()=>{""===o.trim()||l.industries.includes(o.trim())||(m("industries",[...l.industries,o.trim()]),u(""))},g=e=>{m("industries",l.industries.filter((t,s)=>s!==e))};return(0,a.jsxs)(i.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"CareersStep","data-sentry-source-file":"careers-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"careers-step.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"careers-step.tsx",children:"Peluang Karir"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"careers-step.tsx",children:"Detail terkait hasil karir dan industri yang relevan setelah kursus."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"careers-step.tsx",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ew.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Briefcase","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newOutcome","data-sentry-element":"Label","data-sentry-source-file":"careers-step.tsx",children:"Hasil"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newOutcome",value:d,onChange:e=>c(e.target.value),placeholder:"Tambahkan hasil karir baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),h())},"data-sentry-element":"Input","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:h,"data-sentry-element":"Button","data-sentry-source-file":"careers-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.outcomes.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>y(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eC.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"Building","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newIndustry","data-sentry-element":"Label","data-sentry-source-file":"careers-step.tsx",children:"Industri"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newIndustry",value:o,onChange:e=>u(e.target.value),placeholder:"Tambahkan industri baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),j())},"data-sentry-element":"Input","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:j,"data-sentry-element":"Button","data-sentry-source-file":"careers-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.industries.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>g(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ev.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"DollarSign","data-sentry-source-file":"careers-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"averageSalary","data-sentry-element":"Label","data-sentry-source-file":"careers-step.tsx",children:"Rata-rata Gaji"})]}),(0,a.jsx)(x.p,{id:"averageSalary",type:"text",value:l.averageSalary,onChange:e=>m("averageSalary",e.target.value),placeholder:"Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun","data-sentry-element":"Input","data-sentry-source-file":"careers-step.tsx"})]})]})}var ez=s(37586),eA=s(18042),eS=s(14005);function eT(e){let{data:t,onUpdate:s}=e,l=t.studentExperience||{testimonials:[],facilities:[],support:[]},[d,c]=(0,n.useState)(""),[o,u]=(0,n.useState)(""),m=(e,t)=>{s({studentExperience:{...l,[e]:t}})},y=(e,t,s)=>{let a=[...l.testimonials];a[e]={...a[e],[t]:s},m("testimonials",a)},j=e=>{m("testimonials",l.testimonials.filter((t,s)=>s!==e))},g=()=>{""===d.trim()||l.facilities.includes(d.trim())||(m("facilities",[...l.facilities,d.trim()]),c(""))},v=e=>{m("facilities",l.facilities.filter((t,s)=>s!==e))},b=()=>{""===o.trim()||l.support.includes(o.trim())||(m("support",[...l.support,o.trim()]),u(""))},N=e=>{m("support",l.support.filter((t,s)=>s!==e))};return(0,a.jsxs)(i.Zp,{className:"w-full","data-sentry-element":"Card","data-sentry-component":"StudentExperienceStep","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"student-experience-step.tsx",children:"Pengalaman Mahasiswa"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"student-experience-step.tsx",children:"Detail terkait pengalaman, fasilitas, dan dukungan yang akan didapat mahasiswa."})]}),(0,a.jsxs)(i.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)(ez.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"MessageSquare","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(p.J,{"data-sentry-element":"Label","data-sentry-source-file":"student-experience-step.tsx",children:"Testimoni"})]}),l.testimonials.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-end space-x-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex-grow space-y-2",children:[(0,a.jsx)(x.p,{placeholder:"Nama",value:e.name,onChange:e=>y(t,"name",e.target.value)}),(0,a.jsx)(h.T,{placeholder:"Umpan Balik",value:e.feedback,onChange:e=>y(t,"feedback",e.target.value)})]}),(0,a.jsx)(r.$,{variant:"destructive",size:"icon",onClick:()=>j(t),children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]},t)),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{m("testimonials",[...l.testimonials,{name:"",feedback:""}])},"data-sentry-element":"Button","data-sentry-source-file":"student-experience-step.tsx",children:[(0,a.jsx)(q.A,{className:"h-4 w-4 mr-2","data-sentry-element":"Plus","data-sentry-source-file":"student-experience-step.tsx"})," Tambah Testimoni"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eA.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"HardHat","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newFacility","data-sentry-element":"Label","data-sentry-source-file":"student-experience-step.tsx",children:"Fasilitas"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newFacility",value:d,onChange:e=>c(e.target.value),placeholder:"Tambahkan fasilitas baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),g())},"data-sentry-element":"Input","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:g,"data-sentry-element":"Button","data-sentry-source-file":"student-experience-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.facilities.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>v(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eS.A,{className:"h-5 w-5 text-gray-500","data-sentry-element":"LifeBuoy","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(p.J,{htmlFor:"newSupport","data-sentry-element":"Label","data-sentry-source-file":"student-experience-step.tsx",children:"Dukungan"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.p,{id:"newSupport",value:o,onChange:e=>u(e.target.value),placeholder:"Tambahkan dukungan baru",onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),b())},"data-sentry-element":"Input","data-sentry-source-file":"student-experience-step.tsx"}),(0,a.jsx)(r.$,{type:"button",onClick:b,"data-sentry-element":"Button","data-sentry-source-file":"student-experience-step.tsx",children:"Tambah"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:l.support.map((e,t)=>(0,a.jsxs)(f.E,{variant:"secondary",className:"pr-1",children:[e,(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"ml-1 h-auto px-1 py-0.5",onClick:()=>N(t),children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]},t))})]})]})]})}function eP(e){let{data:t,onUpdate:s}=e;return(0,a.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-component":"CourseDetailsStep","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"course-details-step.tsx",children:"Detail Course"}),(0,a.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"course-details-step.tsx",children:"Kelola detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa."})]}),(0,a.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsxs)(ex.tU,{defaultValue:"admissions",className:"w-full","data-sentry-element":"Tabs","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsxs)(ex.j7,{className:"grid w-full grid-cols-5","data-sentry-element":"TabsList","data-sentry-source-file":"course-details-step.tsx",children:[(0,a.jsx)(ex.Xi,{value:"admissions","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Penerimaan"}),(0,a.jsx)(ex.Xi,{value:"academics","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Akademik"}),(0,a.jsx)(ex.Xi,{value:"tuition-financing","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Biaya & Pembiayaan"}),(0,a.jsx)(ex.Xi,{value:"careers","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Karir"}),(0,a.jsx)(ex.Xi,{value:"student-experience","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-details-step.tsx",children:"Pengalaman Siswa"})]}),(0,a.jsxs)("div",{className:"h-[400px] overflow-y-auto pr-4",children:[" ",(0,a.jsx)(ex.av,{value:"admissions","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eh,{data:t,onUpdate:s,"data-sentry-element":"AdmissionsStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ex.av,{value:"academics","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eg,{data:t,onUpdate:s,"data-sentry-element":"AcademicsStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ex.av,{value:"tuition-financing","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(ek,{data:t,onUpdate:s,"data-sentry-element":"TuitionFinancingStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ex.av,{value:"careers","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eD,{data:t,onUpdate:s,"data-sentry-element":"CareersStep","data-sentry-source-file":"course-details-step.tsx"})}),(0,a.jsx)(ex.av,{value:"student-experience","data-sentry-element":"TabsContent","data-sentry-source-file":"course-details-step.tsx",children:(0,a.jsx)(eT,{data:t,onUpdate:s,"data-sentry-element":"StudentExperienceStep","data-sentry-source-file":"course-details-step.tsx"})})]})]})})]})}let eE=[{id:"basic-info",title:"Informasi Dasar",description:"Detail course dan pengaturan dasar"},{id:"module-structure",title:"Struktur Modul",description:"Buat modul dan chapter untuk course"},{id:"content-creation",title:"Pembuatan Konten",description:"Tambahkan konten dan quiz untuk setiap chapter"},{id:"course-details",title:"Informasi Tambahan",description:"Detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa"},{id:"publishing",title:"Publikasi",description:"Review dan publikasikan course"}];function eB(e){var t,s,x;let{onComplete:p,onCancel:h,initialData:y}=e,[f,j]=(0,n.useState)(0),[g,v]=(0,n.useState)({name:(null==y?void 0:y.name)||"",description:(null==y?void 0:y.description)||"",instructor:(null==y?void 0:y.instructor)||"",courseCode:(null==y?void 0:y.courseCode)||"",type:(null==y?void 0:y.type)||"self_paced",enrollmentType:(null==y?void 0:y.enrollmentType)||"code",startDate:null==y?void 0:y.startDate,endDate:null==y?void 0:y.endDate,coverImage:null==y?void 0:y.coverImage,coverImagePreview:null==y?void 0:y.coverImagePreview,isPurchasable:null!=(t=null==y?void 0:y.isPurchasable)&&t,price:null==y?void 0:y.price,currency:(null==y?void 0:y.currency)||"",previewMode:null!=(s=null==y?void 0:y.previewMode)&&s,modules:(null==y?void 0:y.modules)||[],isPublished:null!=(x=null==y?void 0:y.isPublished)&&x,assignedClasses:(null==y?void 0:y.assignedClasses)||[],finalExam:null==y?void 0:y.finalExam,admissions:(null==y?void 0:y.admissions)||{requirements:[],applicationDeadline:"",prerequisites:[]},academics:(null==y?void 0:y.academics)||{credits:0,workload:"",assessment:[]},tuitionAndFinancing:(null==y?void 0:y.tuitionAndFinancing)||{totalCost:0,paymentOptions:[],scholarships:[]},careers:(null==y?void 0:y.careers)||{outcomes:[],industries:[],averageSalary:""},studentExperience:(null==y?void 0:y.studentExperience)||{testimonials:[],facilities:[],support:[]}}),[b,N]=(0,n.useState)(!1);(0,n.useEffect)(()=>{try{let e=sessionStorage.getItem("ai_generated_course_data");if(e){let t=JSON.parse(e);v(e=>({...e,name:t.name||e.name,description:t.description||e.description,courseCode:t.courseCode||e.courseCode,modules:t.modules||e.modules,finalExam:t.finalExam||e.finalExam,...y})),sessionStorage.removeItem("ai_generated_course_data"),t.modules&&t.modules.length>0&&j(2)}}catch(e){console.error("Error loading AI generated data:",e)}},[y]);let k=e=>{v(t=>({...t,...e}))},w=e=>{switch(e){case 0:let t=!!g.name&&!!g.description&&!!g.instructor&&!!g.courseCode;if("purchase"===g.enrollmentType)return t&&!!g.price&&g.price>0&&!!g.currency;return t;case 1:return g.modules.length>0&&g.modules.every(e=>!!e.name&&e.chapters.length>0);case 2:return g.modules.every(e=>e.chapters.every(e=>!!e.content));case 3:let s=!!g.admissions&&(g.admissions.requirements.length>0||!!g.admissions.applicationDeadline||g.admissions.prerequisites.length>0),a=!!g.academics&&(g.academics.credits>0||!!g.academics.workload||g.academics.assessment.length>0),n=!!g.tuitionAndFinancing&&(!!g.tuitionAndFinancing.totalCost||g.tuitionAndFinancing.paymentOptions.length>0||g.tuitionAndFinancing.scholarships.length>0),r=!!g.careers&&(g.careers.outcomes.length>0||g.careers.industries.length>0||!!g.careers.averageSalary),i=!!g.studentExperience&&(g.studentExperience.testimonials.length>0||g.studentExperience.facilities.length>0||g.studentExperience.support.length>0);return s||a||n||r||i;case 4:return!0;default:return!1}},C=()=>w(f),D=async()=>{N(!0);try{await p(g)}catch(e){console.error("Error creating course:",e)}finally{N(!1)}},z=(f+1)/eE.length*100;return(0,a.jsxs)("div",{className:"w-full p-6 space-y-6","data-sentry-component":"CourseCreationWizard","data-sentry-source-file":"course-creation-wizard.tsx",children:[(0,a.jsx)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-creation-wizard.tsx",children:(0,a.jsxs)(i.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"course-creation-wizard.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between gap-x-4 overflow-x-auto pb-4 px-4",children:eE.map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-col items-center flex-grow",children:[(0,a.jsx)("div",{className:(0,m.cn)("w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors duration-200",t===f?"bg-primary text-primary-foreground":"bg-muted text-muted-foreground",t<f&&"bg-green-500 text-white"),children:t<f?(0,a.jsx)(c.A,{className:"w-4 h-4"}):t+1}),(0,a.jsx)("span",{className:(0,m.cn)("mt-1 text-xs text-center whitespace-nowrap",t===f?"text-primary font-medium":"text-muted-foreground"),children:e.title})]},e.id))}),(0,a.jsx)(d.Separator,{className:"my-4","data-sentry-element":"Separator","data-sentry-source-file":"course-creation-wizard.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{children:["Langkah ",f+1," dari ",eE.length]}),(0,a.jsxs)("span",{children:[Math.round(z),"% selesai"]})]}),(0,a.jsx)(l.k,{value:z,className:"h-2","data-sentry-element":"Progress","data-sentry-source-file":"course-creation-wizard.tsx"})]})]})}),(0,a.jsx)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-creation-wizard.tsx",children:(0,a.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"course-creation-wizard.tsx",children:(()=>{switch(f){case 0:return(0,a.jsx)(S,{data:g,onUpdate:k});case 1:return(0,a.jsx)(U,{data:g,onUpdate:k});case 2:return(0,a.jsx)(ei,{data:g,onUpdate:k});case 3:return(0,a.jsx)(eP,{data:g,onUpdate:k});case 4:return(0,a.jsx)(em,{data:g,onPublish:D,isPublishing:b});default:return null}})()})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{f>0&&j(f-1)},disabled:0===f,"data-sentry-element":"Button","data-sentry-source-file":"course-creation-wizard.tsx",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2","data-sentry-element":"ChevronLeft","data-sentry-source-file":"course-creation-wizard.tsx"}),"Sebelumnya"]}),(0,a.jsx)("div",{className:"flex space-x-2",children:f===eE.length-1?(0,a.jsx)(r.$,{onClick:D,disabled:!C()||b,children:b?"Membuat Course...":"Selesai & Buat Course"}):(0,a.jsxs)(r.$,{onClick:()=>{f<eE.length-1&&j(f+1)},disabled:!C(),children:["Selanjutnya",(0,a.jsx)(u.A,{className:"w-4 h-4 ml-2"})]})})]})]})}},97655:(e,t,s)=>{s.d(t,{S:()=>l});var a=s(95155);s(12115);var n=s(38162),r=s(5917),i=s(64269);function l(e){let{className:t,...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(r.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}}}]);