try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b6c0c130-78a2-4487-9da3-e4389ba9bdd7",e._sentryDebugIdIdentifier="sentry-dbid-b6c0c130-78a2-4487-9da3-e4389ba9bdd7")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7423],{47423:(e,t,n)=>{n.r(t),n.d(t,{protobuf:()=>s});var a=["package","message","import","syntax","required","optional","repeated","reserved","default","extensions","packed","bool","bytes","double","enum","float","string","int32","int64","uint32","uint64","sint32","sint64","fixed32","fixed64","sfixed32","sfixed64","option","service","rpc","returns"],d=RegExp("^(("+a.join(")|(")+"))\\b","i"),i=RegExp("^[_A-Za-z\xa1-￿][_A-Za-z0-9\xa1-￿]*");let s={name:"protobuf",token:function(e){return e.eatSpace()?null:e.match("//")?(e.skipToEnd(),"comment"):e.match(/^[0-9\.+-]/,!1)&&(e.match(/^[+-]?0x[0-9a-fA-F]+/)||e.match(/^[+-]?\d*\.\d+([EeDd][+-]?\d+)?/)||e.match(/^[+-]?\d+([EeDd][+-]?\d+)?/))?"number":e.match(/^"([^"]|(""))*"/)||e.match(/^'([^']|(''))*'/)?"string":e.match(d)?"keyword":e.match(i)?"variable":(e.next(),null)},languageData:{autocomplete:a}}}}]);