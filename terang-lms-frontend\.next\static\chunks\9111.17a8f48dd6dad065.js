try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="5e300742-b6a6-49f1-81ef-032dba5ce98a",e._sentryDebugIdIdentifier="sentry-dbid-5e300742-b6a6-49f1-81ef-032dba5ce98a")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9111],{89111:(e,r,t)=>{function n(e){for(var r={},t=e.split(" "),n=0;n<t.length;++n)r[t[n]]=!0;return r}t.r(r),t.d(r,{tcl:()=>c});var a=n("Tcl safe after append array auto_execok auto_import auto_load auto_mkindex auto_mkindex_old auto_qualify auto_reset bgerror binary break catch cd close concat continue dde eof encoding error eval exec exit expr fblocked fconfigure fcopy file fileevent filename filename flush for foreach format gets glob global history http if incr info interp join lappend lindex linsert list llength load lrange lreplace lsearch lset lsort memory msgcat namespace open package parray pid pkg::create pkg_mkIndex proc puts pwd re_syntax read regex regexp registry regsub rename resource return scan seek set socket source split string subst switch tcl_endOfWord tcl_findLibrary tcl_startOfNextWord tcl_wordBreakAfter tcl_startOfPreviousWord tcl_wordBreakBefore tcltest tclvars tell time trace unknown unset update uplevel upvar variable vwait"),o=n("if elseif else and not or eq ne in ni for foreach while switch"),i=/[+\-*&%=<>!?^\/\|]/;function l(e,r,t){return r.tokenize=t,t(e,r)}function f(e,r){var t,n=r.beforeParams;r.beforeParams=!1;var c=e.next();if(('"'==c||"'"==c)&&r.inParams){return l(e,r,(t=c,function(e,r){for(var n,a=!1,o=!1;null!=(n=e.next());){if(n==t&&!a){o=!0;break}a=!a&&"\\"==n}return o&&(r.tokenize=f),"string"}))}if(/[\[\]{}\(\),;\.]/.test(c))return"("==c&&n?r.inParams=!0:")"==c&&(r.inParams=!1),null;if(/\d/.test(c))return e.eatWhile(/[\w\.]/),"number";if("#"==c)return e.eat("*")?l(e,r,s):"#"==c&&e.match(/ *\[ *\[/)?l(e,r,u):(e.skipToEnd(),"comment");if('"'==c)return e.skipTo(/"/),"comment";if("$"==c)return e.eatWhile(/[$_a-z0-9A-Z\.{:]/),e.eatWhile(/}/),r.beforeParams=!0,"builtin";if(i.test(c))return e.eatWhile(i),"comment";e.eatWhile(/[\w\$_{}\xa1-\uffff]/);var d=e.current().toLowerCase();return a&&a.propertyIsEnumerable(d)?"keyword":o&&o.propertyIsEnumerable(d)?(r.beforeParams=!0,"keyword"):null}function s(e,r){for(var t,n=!1;t=e.next();){if("#"==t&&n){r.tokenize=f;break}n="*"==t}return"comment"}function u(e,r){for(var t,n=0;t=e.next();){if("#"==t&&2==n){r.tokenize=f;break}"]"==t?n++:" "!=t&&(n=0)}return"meta"}let c={name:"tcl",startState:function(){return{tokenize:f,beforeParams:!1,inParams:!1}},token:function(e,r){return e.eatSpace()?null:r.tokenize(e,r)},languageData:{commentTokens:{line:"#"}}}}}]);