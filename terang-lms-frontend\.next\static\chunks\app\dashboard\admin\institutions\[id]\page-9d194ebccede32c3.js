try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="6dea53d5-1b1d-4419-af2e-6159b95b4adc",e._sentryDebugIdIdentifier="sentry-dbid-6dea53d5-1b1d-4419-af2e-6159b95b4adc")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[409],{12489:(e,t,a)=>{Promise.resolve().then(a.bind(a,66546))},35299:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},66546:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var s=a(95155),n=a(12115),r=a(20063),l=a(66094),i=a(20764),d=a(31936),c=a(42526),u=a(25532),o=a(35299),y=a(35626),p=a(46046),m=a(52619),h=a.n(m),x=a(98857),g=a(15894);function f(){let e=(0,r.useRouter)(),t=(0,r.useParams)().id,{toast:a}=(0,g.d)(),[m,f]=(0,n.useState)(!1),[j,b]=(0,n.useState)(!0),[v,C]=(0,n.useState)(null),[S,D]=(0,n.useState)(!1),[N,I]=(0,n.useState)({name:"",type:"",subscriptionPlan:"basic",billingCycle:"monthly",studentCount:0,teacherCount:0,paymentStatus:"unpaid",paymentDueDate:""});(0,n.useEffect)(()=>{t&&!S&&(async()=>{try{let s=await fetch("/api/institutions/".concat(t)),n=await s.json();if(n.success){let e=n.data;C(e),S||(I({name:e.name,type:e.type,subscriptionPlan:e.subscription_plan,billingCycle:e.billing_cycle,studentCount:e.student_count,teacherCount:e.teacher_count,paymentStatus:e.payment_status,paymentDueDate:e.payment_due_date?new Date(e.payment_due_date).toISOString().split("T")[0]:""}),D(!0))}else a({title:"Error",description:n.error||"Failed to fetch institution",variant:"destructive"}),e.push("/dashboard/admin/institutions")}catch(t){console.error("Error fetching institution:",t),a({title:"Error",description:"Failed to fetch institution",variant:"destructive"}),e.push("/dashboard/admin/institutions")}finally{b(!1)}})()},[t,S,e,a]);let _=async s=>{s.preventDefault(),f(!0);try{let s=await fetch("/api/institutions/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:N.name,type:N.type,subscriptionPlan:N.subscriptionPlan,billingCycle:N.billingCycle,studentCount:N.studentCount,teacherCount:N.teacherCount,paymentStatus:N.paymentStatus,paymentDueDate:N.paymentDueDate?new Date(N.paymentDueDate).toISOString():null})}),n=await s.json();n.success?(a({title:"Success",description:"Institution updated successfully"}),e.push("/dashboard/admin/institutions")):a({title:"Error",description:n.error||"Failed to update institution",variant:"destructive"})}catch(e){console.error("Error updating institution:",e),a({title:"Error",description:"Failed to update institution",variant:"destructive"})}finally{f(!1)}},w=(e,t)=>{I(a=>({...a,[e]:t}))};return j?(0,s.jsxs)("div",{className:"flex min-h-[400px] items-center justify-center",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("p",{className:"ml-2",children:"Loading institution..."})]}):v?(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"EditInstitutionPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(h(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Institution"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Update institution information"})]})]}),(0,s.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Institution Details"}),(0,s.jsxs)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:["Update the information for ",v.name]})]}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Name"}),(0,s.jsx)(d.p,{id:"name",value:N.name,onChange:e=>w("name",e.target.value),placeholder:"Enter institution name",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"type","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Type"}),(0,s.jsxs)(u.l6,{value:N.type,onValueChange:e=>w("type",e),required:!0,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{placeholder:"Select institution type","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:x.g0.map(e=>(0,s.jsx)(u.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"subscriptionPlan","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Subscription Plan"}),(0,s.jsxs)(u.l6,{value:N.subscriptionPlan,onValueChange:e=>w("subscriptionPlan",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:Object.entries(x.Ap).map(e=>{let[t,a]=e;return(0,s.jsxs)(u.eb,{value:t,children:[a.name," - Rp"," ",a.pricePerStudent.monthly.toLocaleString(),"/student/month"]},t)})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"billingCycle","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Billing Cycle"}),(0,s.jsxs)(u.l6,{value:N.billingCycle,onValueChange:e=>w("billingCycle",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.eb,{value:"monthly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Monthly"}),(0,s.jsx)(u.eb,{value:"yearly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Yearly (25% discount)"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"studentCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Students"}),(0,s.jsx)(d.p,{id:"studentCount",type:"number",value:N.studentCount,onChange:e=>w("studentCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"teacherCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Teachers"}),(0,s.jsx)(d.p,{id:"teacherCount",type:"number",value:N.teacherCount,onChange:e=>w("teacherCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"paymentStatus","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,s.jsxs)(u.l6,{value:N.paymentStatus,onValueChange:e=>w("paymentStatus",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.eb,{value:"paid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Paid"}),(0,s.jsx)(u.eb,{value:"unpaid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Unpaid"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"paymentDueDate","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Payment Due Date"}),(0,s.jsx)(d.p,{id:"paymentDueDate",type:"date",value:N.paymentDueDate,onChange:e=>w("paymentDueDate",e.target.value),"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)(h(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(i.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,s.jsxs)(i.$,{type:"submit",disabled:m,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),m?"Updating...":"Update Institution"]})]})]})})]})]}):(0,s.jsxs)("div",{className:"py-8 text-center",children:[(0,s.jsx)("p",{children:"Institution not found"}),(0,s.jsx)(h(),{href:"/dashboard/admin/institutions",children:(0,s.jsx)(i.$,{className:"mt-4",children:"Back to Institutions"})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,9568,2254,4850,8441,3840,7358],()=>t(12489)),_N_E=e.O()}]);