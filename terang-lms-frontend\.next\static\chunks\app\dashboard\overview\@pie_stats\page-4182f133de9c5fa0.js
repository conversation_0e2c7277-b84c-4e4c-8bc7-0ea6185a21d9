try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f9f9b9ba-7779-4ba5-b889-ac94cf62e9a8",e._sentryDebugIdIdentifier="sentry-dbid-f9f9b9ba-7779-4ba5-b889-ac94cf62e9a8")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[464],{34160:(e,t,r)=>{"use strict";r.d(t,{II:()=>h,Nt:()=>m,at:()=>u});var a=r(95155),s=r(12115),n=r(26991),o=r(23508),l=r(7620),i=r(64269);let d={light:"",dark:".dark"},c=s.createContext(null);function u(e){let{id:t,className:r,children:o,config:l,...d}=e,u=s.useId(),h="chart-".concat(t||u.replace(/:/g,""));return(0,a.jsx)(c.Provider,{value:{config:l},"data-sentry-element":"ChartContext.Provider","data-sentry-component":"ChartContainer","data-sentry-source-file":"chart.tsx",children:(0,a.jsxs)("div",{"data-slot":"chart","data-chart":h,className:(0,i.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",r),...d,children:[(0,a.jsx)(f,{id:h,config:l,"data-sentry-element":"ChartStyle","data-sentry-source-file":"chart.tsx"}),(0,a.jsx)(n.u,{debounce:2e3,"data-sentry-element":"RechartsPrimitive.ResponsiveContainer","data-sentry-source-file":"chart.tsx",children:o})]})})}let f=e=>{let{id:t,config:r}=e,s=Object.entries(r).filter(e=>{let[,t]=e;return t.theme||t.color});return s.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(d).map(e=>{let[r,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(s.map(e=>{var t;let[a,s]=e,n=(null==(t=s.theme)?void 0:t[r])||s.color;return n?"  --color-".concat(a,": ").concat(n,";"):null}).join("\n"),"\n}\n")}).join("\n")},"data-sentry-component":"ChartStyle","data-sentry-source-file":"chart.tsx"}):null},h=o.m;function m(e){let{active:t,payload:r,className:n,indicator:o="dot",hideLabel:l=!1,hideIndicator:d=!1,label:u,labelFormatter:f,labelClassName:h,formatter:m,color:p,nameKey:y,labelKey:g}=e,{config:v}=function(){let e=s.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),b=s.useMemo(()=>{var e;if(l||!(null==r?void 0:r.length))return null;let[t]=r,s="".concat(g||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),n=x(v,t,s),o=g||"string"!=typeof u?null==n?void 0:n.label:(null==(e=v[u])?void 0:e.label)||u;return f?(0,a.jsx)("div",{className:(0,i.cn)("font-medium",h),children:f(o,r)}):o?(0,a.jsx)("div",{className:(0,i.cn)("font-medium",h),children:o}):null},[u,f,r,l,h,v,g]);if(!t||!(null==r?void 0:r.length))return null;let j=1===r.length&&"dot"!==o;return(0,a.jsxs)("div",{className:(0,i.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",n),"data-sentry-component":"ChartTooltipContent","data-sentry-source-file":"chart.tsx",children:[j?null:b,(0,a.jsx)("div",{className:"grid gap-1.5",children:r.map((e,t)=>{let r="".concat(y||e.name||e.dataKey||"value"),s=x(v,e,r),n=p||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,i.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===o&&"items-center"),children:m&&(null==e?void 0:e.value)!==void 0&&e.name?m(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==s?void 0:s.icon)?(0,a.jsx)(s.icon,{}):!d&&(0,a.jsx)("div",{className:(0,i.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===o,"w-1":"line"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"my-0.5":j&&"dashed"===o}),style:{"--color-bg":n,"--color-border":n}}),(0,a.jsxs)("div",{className:(0,i.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[j?b:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:(null==s?void 0:s.label)||e.name})]}),e.value&&(0,a.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function x(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,s=r;return r in t&&"string"==typeof t[r]?s=t[r]:a&&r in a&&"string"==typeof a[r]&&(s=a[r]),s in e?e[s]:e[r]}l.s},52868:(e,t,r)=>{"use strict";r.d(t,{PieGraph:()=>h});var a=r(95155),s=r(12115),n=r(1524),o=r(78765),l=r(12235),i=r(63296),d=r(66094),c=r(34160);let u=[{browser:"chrome",visitors:275,fill:"var(--primary)"},{browser:"safari",visitors:200,fill:"var(--primary-light)"},{browser:"firefox",visitors:287,fill:"var(--primary-lighter)"},{browser:"edge",visitors:173,fill:"var(--primary-dark)"},{browser:"other",visitors:190,fill:"var(--primary-darker)"}],f={visitors:{label:"Visitors"},chrome:{label:"Chrome",color:"var(--primary)"},safari:{label:"Safari",color:"var(--primary)"},firefox:{label:"Firefox",color:"var(--primary)"},edge:{label:"Edge",color:"var(--primary)"},other:{label:"Other",color:"var(--primary)"}};function h(){let e=s.useMemo(()=>u.reduce((e,t)=>e+t.visitors,0),[]);return(0,a.jsxs)(d.Zp,{className:"@container/card","data-sentry-element":"Card","data-sentry-component":"PieGraph","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsxs)(d.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsx)(d.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"pie-graph.tsx",children:"Pie Chart - Donut with Text"}),(0,a.jsxs)(d.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsx)("span",{className:"hidden @[540px]/card:block",children:"Total visitors by browser for the last 6 months"}),(0,a.jsx)("span",{className:"@[540px]/card:hidden",children:"Browser distribution"})]})]}),(0,a.jsx)(d.Wu,{className:"px-2 pt-4 sm:px-6 sm:pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"pie-graph.tsx",children:(0,a.jsx)(c.at,{config:f,className:"mx-auto aspect-square h-[250px]","data-sentry-element":"ChartContainer","data-sentry-source-file":"pie-graph.tsx",children:(0,a.jsxs)(o.r,{"data-sentry-element":"PieChart","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsx)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"pie-graph.tsx",children:["chrome","safari","firefox","edge","other"].map((e,t)=>(0,a.jsxs)("linearGradient",{id:"fill".concat(e),x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"var(--primary)",stopOpacity:1-.15*t}),(0,a.jsx)("stop",{offset:"100%",stopColor:"var(--primary)",stopOpacity:.8-.15*t})]},e))}),(0,a.jsx)(c.II,{cursor:!1,content:(0,a.jsx)(c.Nt,{hideLabel:!0}),"data-sentry-element":"ChartTooltip","data-sentry-source-file":"pie-graph.tsx"}),(0,a.jsx)(l.F,{data:u.map(e=>({...e,fill:"url(#fill".concat(e.browser,")")})),dataKey:"visitors",nameKey:"browser",innerRadius:60,strokeWidth:2,stroke:"var(--background)","data-sentry-element":"Pie","data-sentry-source-file":"pie-graph.tsx",children:(0,a.jsx)(i.J,{content:t=>{let{viewBox:r}=t;if(r&&"cx"in r&&"cy"in r)return(0,a.jsxs)("text",{x:r.cx,y:r.cy,textAnchor:"middle",dominantBaseline:"middle",children:[(0,a.jsx)("tspan",{x:r.cx,y:r.cy,className:"fill-foreground text-3xl font-bold",children:e.toLocaleString()}),(0,a.jsx)("tspan",{x:r.cx,y:(r.cy||0)+24,className:"fill-muted-foreground text-sm",children:"Total Visitors"})]})},"data-sentry-element":"Label","data-sentry-source-file":"pie-graph.tsx"})})]})})}),(0,a.jsxs)(d.wL,{className:"flex-col gap-2 text-sm","data-sentry-element":"CardFooter","data-sentry-source-file":"pie-graph.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 leading-none font-medium",children:["Chrome leads with"," ",(u[0].visitors/e*100).toFixed(1),"%"," ",(0,a.jsx)(n.A,{className:"h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"pie-graph.tsx"})]}),(0,a.jsx)("div",{className:"text-muted-foreground leading-none",children:"Based on data from January - June 2024"})]})]})}},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n,z:()=>o});var a=r(2821),s=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function o(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:s=0,sizeType:n="normal"}=a;if(0===e)return"0 Byte";let o=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,o)).toFixed(s)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][o])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][o])?r:"Bytes")}},66094:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>c});var a=r(95155);r(12115);var s=r(64269);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},78623:(e,t,r)=>{Promise.resolve().then(r.bind(r,52868))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,5608,5556,4850,8441,3840,7358],()=>t(78623)),_N_E=e.O()}]);