try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="02c3ca96-ceab-4d15-8465-a760dd996c8e",e._sentryDebugIdIdentifier="sentry-dbid-02c3ca96-ceab-4d15-8465-a760dd996c8e")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6980],{46980:(e,a,t)=>{t.r(a),t.d(a,{mathematica:()=>u});var n="[a-zA-Z\\$][a-zA-Z0-9\\$]*",c="(?:\\.\\d+|\\d+\\.\\d*|\\d+)",o="(?:`(?:`?"+c+")?)",r=RegExp("(?:(?:\\d+)(?:\\^\\^(?:\\.\\w+|\\w+\\.\\w*|\\w+)"+o+"?(?:\\*\\^[+-]?\\d+)?))"),m=RegExp("(?:"+c+o+"?(?:\\*\\^[+-]?\\d+)?)"),i=RegExp("(?:`?)(?:"+n+")(?:`(?:"+n+"))*(?:`?)");function z(e,a){var t;return'"'===(t=e.next())?(a.tokenize=l,a.tokenize(e,a)):"("===t&&e.eat("*")?(a.commentLevel++,a.tokenize=d,a.tokenize(e,a)):(e.backUp(1),e.match(r,!0,!1)||e.match(m,!0,!1))?"number":e.match(/(?:In|Out)\[[0-9]*\]/,!0,!1)?"atom":e.match(/([a-zA-Z\$][a-zA-Z0-9\$]*(?:`[a-zA-Z0-9\$]+)*::usage)/,!0,!1)?"meta":e.match(/([a-zA-Z\$][a-zA-Z0-9\$]*(?:`[a-zA-Z0-9\$]+)*::[a-zA-Z\$][a-zA-Z0-9\$]*):?/,!0,!1)?"string.special":e.match(/([a-zA-Z\$][a-zA-Z0-9\$]*\s*:)(?:(?:[a-zA-Z\$][a-zA-Z0-9\$]*)|(?:[^:=>~@\^\&\*\)\[\]'\?,\|])).*/,!0,!1)||e.match(/[a-zA-Z\$][a-zA-Z0-9\$]*_+[a-zA-Z\$][a-zA-Z0-9\$]*/,!0,!1)||e.match(/[a-zA-Z\$][a-zA-Z0-9\$]*_+/,!0,!1)||e.match(/_+[a-zA-Z\$][a-zA-Z0-9\$]*/,!0,!1)?"variableName.special":e.match(/\\\[[a-zA-Z\$][a-zA-Z0-9\$]*\]/,!0,!1)?"character":e.match(/(?:\[|\]|{|}|\(|\))/,!0,!1)?"bracket":e.match(/(?:#[a-zA-Z\$][a-zA-Z0-9\$]*|#+[0-9]?)/,!0,!1)?"variableName.constant":e.match(i,!0,!1)?"keyword":e.match(/(?:\\|\+|\-|\*|\/|,|;|\.|:|@|~|=|>|<|&|\||_|`|'|\^|\?|!|%)/,!0,!1)?"operator":(e.next(),"error")}function l(e,a){for(var t,n=!1,c=!1;null!=(t=e.next());){if('"'===t&&!c){n=!0;break}c=!c&&"\\"===t}return n&&!c&&(a.tokenize=z),"string"}function d(e,a){for(var t,n;a.commentLevel>0&&null!=(n=e.next());)"("===t&&"*"===n&&a.commentLevel++,"*"===t&&")"===n&&a.commentLevel--,t=n;return a.commentLevel<=0&&(a.tokenize=z),"comment"}let u={name:"mathematica",startState:function(){return{tokenize:z,commentLevel:0}},token:function(e,a){return e.eatSpace()?null:a.tokenize(e,a)},languageData:{commentTokens:{block:{open:"(*",close:"*)"}}}}}}]);