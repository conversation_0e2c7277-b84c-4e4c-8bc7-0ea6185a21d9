try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8a914e2f-d307-4a71-a2d5-a61ae5d40f61",e._sentryDebugIdIdentifier="sentry-dbid-8a914e2f-d307-4a71-a2d5-a61ae5d40f61")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3148],{10849:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(95155),n=s(12115),r=s(20063),l=s(66094),i=s(20764),d=s(31936),c=s(42526),o=s(25532),u=s(35626),y=s(46046),p=s(52619),m=s.n(p),x=s(98857),h=s(15894);function g(){let e=(0,r.useRouter)(),{toast:t}=(0,h.d)(),[s,p]=(0,n.useState)(!1),[g,f]=(0,n.useState)({name:"",type:"",subscriptionPlan:"basic",billingCycle:"monthly",studentCount:0,teacherCount:0,paymentStatus:"unpaid"}),j=async s=>{s.preventDefault(),p(!0);try{let s=await fetch("/api/institutions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(g)}),a=await s.json();a.success?(t({title:"Success",description:"Institution created successfully"}),e.push("/dashboard/admin/institutions")):t({title:"Error",description:a.error||"Failed to create institution",variant:"destructive"})}catch(e){console.error("Error creating institution:",e),t({title:"Error",description:"Failed to create institution",variant:"destructive"})}finally{p(!1)}},b=(e,t)=>{f(s=>({...s,[e]:t}))};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewInstitutionPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(m(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Add New Institution"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Create a new educational institution on the platform"})]})]}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Institution Details"}),(0,a.jsx)(l.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter the basic information for the new institution"})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Name"}),(0,a.jsx)(d.p,{id:"name",value:g.name,onChange:e=>b("name",e.target.value),placeholder:"Enter institution name",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"type","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Type"}),(0,a.jsxs)(o.l6,{value:g.type,onValueChange:e=>b("type",e),required:!0,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(o.yv,{placeholder:"Select institution type","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(o.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:x.g0.map(e=>(0,a.jsx)(o.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"subscriptionPlan","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Subscription Plan"}),(0,a.jsxs)(o.l6,{value:g.subscriptionPlan,onValueChange:e=>b("subscriptionPlan",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(o.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(o.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:Object.entries(x.Ap).map(e=>{let[t,s]=e;return(0,a.jsxs)(o.eb,{value:t,children:[s.name," - Rp"," ",s.pricePerStudent.monthly.toLocaleString(),"/student/month"]},t)})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"billingCycle","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Billing Cycle"}),(0,a.jsxs)(o.l6,{value:g.billingCycle,onValueChange:e=>b("billingCycle",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(o.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsxs)(o.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.eb,{value:"monthly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Monthly"}),(0,a.jsx)(o.eb,{value:"yearly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Yearly (25% discount)"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"studentCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Students"}),(0,a.jsx)(d.p,{id:"studentCount",type:"number",value:g.studentCount,onChange:e=>b("studentCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"teacherCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Teachers"}),(0,a.jsx)(d.p,{id:"teacherCount",type:"number",value:g.teacherCount,onChange:e=>b("teacherCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"paymentStatus","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,a.jsxs)(o.l6,{value:g.paymentStatus,onValueChange:e=>b("paymentStatus",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(o.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsxs)(o.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.eb,{value:"paid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Paid"}),(0,a.jsx)(o.eb,{value:"unpaid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Unpaid"})]})]})]})]}),g.subscriptionPlan&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"text-lg",children:[x.Ap[g.subscriptionPlan].name," ","Plan Preview"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Features:"}),(0,a.jsx)("ul",{className:"space-y-1 text-sm",children:x.Ap[g.subscriptionPlan].features.slice(0,5).map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"mr-2 h-2 w-2 rounded-full bg-green-500"}),e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Pricing:"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,a.jsxs)("p",{children:["Monthly: Rp"," ",x.Ap[g.subscriptionPlan].pricePerStudent.monthly.toLocaleString(),"/student"]}),(0,a.jsxs)("p",{children:["Yearly: Rp"," ",x.Ap[g.subscriptionPlan].pricePerStudent.yearly.toLocaleString(),"/student"]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Student Range:"," ",x.Ap[g.subscriptionPlan].minStudents," ","-"," ",x.Ap[g.subscriptionPlan].maxStudents]})]})]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,a.jsx)(m(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(i.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,a.jsxs)(i.$,{type:"submit",disabled:s,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),s?"Creating...":"Create Institution"]})]})]})})]})]})}},88360:(e,t,s)=>{Promise.resolve().then(s.bind(s,10849))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,9568,2254,4850,8441,3840,7358],()=>t(88360)),_N_E=e.O()}]);