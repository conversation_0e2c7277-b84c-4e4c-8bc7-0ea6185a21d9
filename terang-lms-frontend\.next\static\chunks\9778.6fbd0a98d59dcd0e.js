try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="b1be270b-6846-4197-8913-137bb37106a6",e._sentryDebugIdIdentifier="sentry-dbid-b1be270b-6846-4197-8913-137bb37106a6")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9778],{89778:(e,n,t)=>{function i(e,n){for(var t=0;t<e.length;t++)n(e[t],t)}t.r(n),t.d(n,{dylan:()=>h});var r={unnamedDefinition:["interface"],namedDefinition:["module","library","macro","C-struct","C-union","C-function","C-callable-wrapper"],typeParameterizedDefinition:["class","C-subtype","C-mapped-subtype"],otherParameterizedDefinition:["method","function","C-variable","C-address"],constantSimpleDefinition:["constant"],variableSimpleDefinition:["variable"],otherSimpleDefinition:["generic","domain","C-pointer-type","table"],statement:["if","block","begin","method","case","for","select","when","unless","until","while","iterate","profiling","dynamic-bind"],separator:["finally","exception","cleanup","else","elseif","afterwards"],other:["above","below","by","from","handler","in","instance","let","local","otherwise","slot","subclass","then","to","keyed-by","virtual"],signalingCalls:["signal","error","cerror","break","check-type","abort"]};r.otherDefinition=r.unnamedDefinition.concat(r.namedDefinition).concat(r.otherParameterizedDefinition),r.definition=r.typeParameterizedDefinition.concat(r.otherDefinition),r.parameterizedDefinition=r.typeParameterizedDefinition.concat(r.otherParameterizedDefinition),r.simpleDefinition=r.constantSimpleDefinition.concat(r.variableSimpleDefinition).concat(r.otherSimpleDefinition),r.keyword=r.statement.concat(r.separator).concat(r.other);var a="[-_a-zA-Z?!*@<>$%]+",o=RegExp("^"+a),l={symbolKeyword:a+":",symbolClass:"<"+a+">",symbolGlobal:"\\*"+a+"\\*",symbolConstant:"\\$"+a},f={symbolKeyword:"atom",symbolClass:"tag",symbolGlobal:"variableName.standard",symbolConstant:"variableName.constant"};for(var s in l)l.hasOwnProperty(s)&&(l[s]=RegExp("^"+l[s]));l.keyword=[/^with(?:out)?-[-_a-zA-Z?!*@<>$%]+/];var u={};u.keyword="keyword",u.definition="def",u.simpleDefinition="def",u.signalingCalls="builtin";var c={},d={};function b(e,n,t){return n.tokenize=t,t(e,n)}function m(e,n){var t=e.peek();if("'"==t||'"'==t)return e.next(),b(e,n,y(t,"string"));if("/"==t){if(e.next(),e.eat("*"))return b(e,n,p);if(e.eat("/"))return e.skipToEnd(),"comment";e.backUp(1)}else if(/[+\-\d\.]/.test(t)){if(e.match(/^[+-]?[0-9]*\.[0-9]*([esdx][+-]?[0-9]+)?/i)||e.match(/^[+-]?[0-9]+([esdx][+-]?[0-9]+)/i)||e.match(/^[+-]?\d+/))return"number"}else if("#"==t){if(e.next(),'"'==(t=e.peek()))return e.next(),b(e,n,y('"',"string"));if("b"==t)return e.next(),e.eatWhile(/[01]/),"number";if("x"==t)return e.next(),e.eatWhile(/[\da-f]/i),"number";else if("o"==t)return e.next(),e.eatWhile(/[0-7]/),"number";else if("#"==t)return e.next(),"punctuation";else if("["==t||"("==t)return e.next(),"bracket";else return e.match(/f|t|all-keys|include|key|next|rest/i)?"atom":(e.eatWhile(/[-a-zA-Z]/),"error")}else if("~"==t)return e.next(),"="==(t=e.peek())&&(e.next(),"="==(t=e.peek())&&e.next()),"operator";else if(":"==t){if(e.next(),"="==(t=e.peek()))return e.next(),"operator";if(":"==t)return e.next(),"punctuation"}else if(-1!="[](){}".indexOf(t))return e.next(),"bracket";else if(-1!=".,".indexOf(t))return e.next(),"punctuation";else if(e.match("end"))return"keyword";for(var i in l)if(l.hasOwnProperty(i)){var r=l[i];if(r instanceof Array&&function(e,n){for(var t=0;t<e.length;t++)if(n(e[t],t))return!0;return!1}(r,function(n){return e.match(n)})||e.match(r))return f[i]}return/[+\-*\/^=<>&|]/.test(t)?(e.next(),"operator"):e.match("define")?"def":(e.eatWhile(/[\w\-]/),c.hasOwnProperty(e.current()))?d[e.current()]:e.current().match(o)?"variable":(e.next(),"variableName.standard")}function p(e,n){for(var t,i=!1,r=!1,a=0;t=e.next();){if("/"==t&&i)if(a>0)a--;else{n.tokenize=m;break}else"*"==t&&r&&a++;i="*"==t,r="/"==t}return"comment"}function y(e,n){return function(t,i){for(var r,a=!1,o=!1;null!=(r=t.next());){if(r==e&&!a){o=!0;break}a=!a&&"\\"==r}return(o||!a)&&(i.tokenize=m),n}}i(["keyword","definition","simpleDefinition","signalingCalls"],function(e){i(r[e],function(n){c[n]=e,d[n]=u[e]})});let h={name:"dylan",startState:function(){return{tokenize:m,currentIndent:0}},token:function(e,n){return e.eatSpace()?null:n.tokenize(e,n)},languageData:{commentTokens:{block:{open:"/*",close:"*/"}}}}}}]);