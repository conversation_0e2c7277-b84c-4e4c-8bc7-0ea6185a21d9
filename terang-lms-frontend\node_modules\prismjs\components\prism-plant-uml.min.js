!function(e){var t=/\$\w+|%[a-z]+%/;e.languages["plant-uml"]={comment:{pattern:/(^[ \t]*)(?:'.*|\/'[\s\S]*?'\/)/m,lookbehind:!0,greedy:!0},preprocessor:{pattern:/(^[ \t]*)!.*/m,lookbehind:!0,greedy:!0,alias:"property",inside:{variable:t}},delimiter:{pattern:/(^[ \t]*)@(?:end|start)uml\b/m,lookbehind:!0,greedy:!0,alias:"punctuation"},arrow:{pattern:RegExp("(^|[^-.<>?|\\\\[\\]ox])[[?]?[ox]?(?:(?:-+(?:[drlu]|do|down|le|left|ri|right|up)-+|\\.+(?:[drlu]|do|down|le|left|ri|right|up)\\.+|-+(?:\\[[^[\\]]*\\]-*)?|\\[[^[\\]]*\\]-+|\\.+(?:\\[[^[\\]]*\\]\\.*)?|\\[[^[\\]]*\\]\\.+)(?:>{1,2}|/{1,2}|\\\\{1,2}|\\|>|[#*^+{xo])|(?:<{1,2}|/{1,2}|\\\\{1,2}|<\\||[#*^+}xo])(?:-+(?:[drlu]|do|down|le|left|ri|right|up)-+|\\.+(?:[drlu]|do|down|le|left|ri|right|up)\\.+|-+(?:\\[[^[\\]]*\\]-*)?|\\[[^[\\]]*\\]-+|\\.+(?:\\[[^[\\]]*\\]\\.*)?|\\[[^[\\]]*\\]\\.+)(?:(?:>{1,2}|/{1,2}|\\\\{1,2}|\\|>|[#*^+{xo]))?)[ox]?[\\]?]?(?![-.<>?|\\\\\\]ox])"),lookbehind:!0,greedy:!0,alias:"operator",inside:{expression:{pattern:/(\[)[^[\]]+(?=\])/,lookbehind:!0,inside:null},punctuation:/\[(?=$|\])|^\]/}},string:{pattern:/"[^"]*"/,greedy:!0},text:{pattern:/(\[[ \t]*[\r\n]+(?![\r\n]))[^\]]*(?=\])/,lookbehind:!0,greedy:!0,alias:"string"},keyword:[{pattern:/^([ \t]*)(?:abstract\s+class|end\s+(?:box|fork|group|merge|note|ref|split|title)|(?:fork|split)(?:\s+again)?|activate|actor|agent|alt|annotation|artifact|autoactivate|autonumber|backward|binary|boundary|box|break|caption|card|case|circle|class|clock|cloud|collections|component|concise|control|create|critical|database|deactivate|destroy|detach|diamond|else|elseif|end|end[hr]note|endif|endswitch|endwhile|entity|enum|file|folder|footer|frame|group|[hr]?note|header|hexagon|hide|if|interface|label|legend|loop|map|namespace|network|newpage|node|nwdiag|object|opt|package|page|par|participant|person|queue|rectangle|ref|remove|repeat|restore|return|robust|scale|set|show|skinparam|stack|start|state|stop|storage|switch|title|together|usecase|usecase\/|while)(?=\s|$)/m,lookbehind:!0,greedy:!0},/\b(?:elseif|equals|not|while)(?=\s*\()/,/\b(?:as|is|then)\b/],divider:{pattern:/^==.+==$/m,greedy:!0,alias:"important"},time:{pattern:/@(?:\d+(?:[:/]\d+){2}|[+-]?\d+|:[a-z]\w*(?:[+-]\d+)?)\b/i,greedy:!0,alias:"number"},color:{pattern:/#(?:[a-z_]+|[a-fA-F0-9]+)\b/,alias:"symbol"},variable:t,punctuation:/[:,;()[\]{}]|\.{3}/},e.languages["plant-uml"].arrow.inside.expression.inside=e.languages["plant-uml"],e.languages.plantuml=e.languages["plant-uml"]}(Prism);