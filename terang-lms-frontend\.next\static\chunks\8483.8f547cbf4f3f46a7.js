try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c7edf64b-f416-4558-92dc-9ae14f46e701",e._sentryDebugIdIdentifier="sentry-dbid-c7edf64b-f416-4558-92dc-9ae14f46e701")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8483],{8483:(e,t,n)=>{n.r(t),n.d(t,{yacas:()=>d});var r=function(e){for(var t={},n=e.split(" "),r=0;r<n.length;++r)t[n[r]]=!0;return t}("Assert BackQuote D Defun Deriv For ForEach FromFile FromString Function Integrate InverseTaylor Limit LocalSymbols Macro MacroRule MacroRulePattern NIntegrate Rule RulePattern Subst TD TExplicitSum TSum Taylor Taylor1 Taylor2 Taylor3 ToFile ToStdout ToString TraceRule Until While"),o="(?:[a-zA-Z\\$'][a-zA-Z0-9\\$']*)",a=RegExp("(?:(?:\\.\\d+|\\d+\\.\\d*|\\d+)(?:[eE][+-]?\\d+)?)"),i=new RegExp(o),c=RegExp(o+"?_"+o),l=RegExp(o+"\\s*\\(");function u(e,t){if('"'===(n=e.next()))return t.tokenize=s,t.tokenize(e,t);if("/"===n){if(e.eat("*"))return t.tokenize=f,t.tokenize(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}e.backUp(1);var n,o=e.match(/^(\w+)\s*\(/,!1);null!==o&&r.hasOwnProperty(o[1])&&t.scopes.push("bodied");var u=p(t);if("bodied"===u&&"["===n&&t.scopes.pop(),("["===n||"{"===n||"("===n)&&t.scopes.push(n),("["===(u=p(t))&&"]"===n||"{"===u&&"}"===n||"("===u&&")"===n)&&t.scopes.pop(),";"===n)for(;"bodied"===u;)t.scopes.pop(),u=p(t);return e.match(/\d+ *#/,!0,!1)?"qualifier":e.match(a,!0,!1)?"number":e.match(c,!0,!1)?"variableName.special":e.match(/(?:\[|\]|{|}|\(|\))/,!0,!1)?"bracket":e.match(l,!0,!1)?(e.backUp(1),"variableName.function"):e.match(i,!0,!1)?"variable":e.match(/(?:\\|\+|\-|\*|\/|,|;|\.|:|@|~|=|>|<|&|\||_|`|'|\^|\?|!|%|#)/,!0,!1)?"operator":"error"}function s(e,t){for(var n,r=!1,o=!1;null!=(n=e.next());){if('"'===n&&!o){r=!0;break}o=!o&&"\\"===n}return r&&!o&&(t.tokenize=u),"string"}function f(e,t){for(var n,r;null!=(r=e.next());){if("*"===n&&"/"===r){t.tokenize=u;break}n=r}return"comment"}function p(e){var t=null;return e.scopes.length>0&&(t=e.scopes[e.scopes.length-1]),t}let d={name:"yacas",startState:function(){return{tokenize:u,scopes:[]}},token:function(e,t){return e.eatSpace()?null:t.tokenize(e,t)},indent:function(e,t,n){if(e.tokenize!==u&&null!==e.tokenize)return null;var r=0;return("]"===t||"];"===t||"}"===t||"};"===t||");"===t)&&(r=-1),(e.scopes.length+r)*n.unit},languageData:{electricInput:/[{}\[\]()\;]/,commentTokens:{line:"//",block:{open:"/*",close:"*/"}}}}}}]);