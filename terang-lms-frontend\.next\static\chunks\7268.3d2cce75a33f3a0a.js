try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="3fe3ab0b-ee8b-4bee-ba73-167bd852b8c4",e._sentryDebugIdIdentifier="sentry-dbid-3fe3ab0b-ee8b-4bee-ba73-167bd852b8c4")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7268],{67268:(e,n,t)=>{function r(e,n){return e.skipToEnd(),n.cur=a,"error"}function u(e,n){return e.match(/^HTTP\/\d\.\d/)?(n.cur=o,"keyword"):e.match(/^[A-Z]+/)&&/[ \t]/.test(e.peek())?(n.cur=i,"keyword"):r(e,n)}function o(e,n){var t=e.match(/^\d+/);if(!t)return r(e,n);n.cur=c;var u=Number(t[0]);return u>=100&&u<400?"atom":"error"}function c(e,n){return e.skipToEnd(),n.cur=a,null}function i(e,n){return e.eatWhile(/\S/),n.cur=d,"string.special"}function d(e,n){return e.match(/^HTTP\/\d\.\d$/)?(n.cur=a,"keyword"):r(e,n)}function a(e){return!e.sol()||e.eat(/[ \t]/)?(e.skipToEnd(),"string"):e.match(/^.*?:/)?"atom":(e.skipToEnd(),"error")}function s(e){return e.skipToEnd(),null}t.r(n),t.d(n,{http:()=>b});let b={name:"http",token:function(e,n){var t=n.cur;return t!=a&&t!=s&&e.eatSpace()?null:t(e,n)},blankLine:function(e){e.cur=s},startState:function(){return{cur:u}}}}}]);