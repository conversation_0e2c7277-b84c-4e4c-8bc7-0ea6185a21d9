try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4bda5cf8-accb-48c6-8d57-3a45829b390b",e._sentryDebugIdIdentifier="sentry-dbid-4bda5cf8-accb-48c6-8d57-3a45829b390b")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[970],{12346:(e,t,r)=>{Promise.resolve().then(r.bind(r,52901))},47886:(e,t,r)=>{"use strict";r.d(t,{WG:()=>n,cl:()=>l,qs:()=>s});let s={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==s.getUser(),hasRole:e=>{let t=s.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>s.hasRole("super_admin"),isTeacher:()=>s.hasRole("teacher"),isStudent:()=>s.hasRole("student")},n=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},a=()=>{let e=s.getUser();return e||(window.location.href="/auth/sign-in",null)},l=e=>{let t=a();return t?t.role!==e?(window.location.href=n(t),null):t:null}},52901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(95155),n=r(12115),a=r(47886);function l(e){let{children:t}=e;return(0,n.useEffect)(()=>{(0,a.cl)("super_admin")},[]),(0,s.jsx)(s.Fragment,{children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4850,8441,3840,7358],()=>t(12346)),_N_E=e.O()}]);