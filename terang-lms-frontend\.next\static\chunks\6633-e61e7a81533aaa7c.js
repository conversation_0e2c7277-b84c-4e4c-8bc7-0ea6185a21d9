try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="70d377e2-710b-48fc-a6f9-29e666062a70",t._sentryDebugIdIdentifier="sentry-dbid-70d377e2-710b-48fc-a6f9-29e666062a70")}catch(t){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6633],{1473:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("<PERSON>",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},3468:(t,e,i)=>{"use strict";i.d(e,{A:()=>o,q:()=>s});var r=i(12115),n=i(95155);function s(t,e){let i=r.createContext(e),s=t=>{let{children:e,...s}=t,o=r.useMemo(()=>s,Object.values(s));return(0,n.jsx)(i.Provider,{value:o,children:e})};return s.displayName=t+"Provider",[s,function(n){let s=r.useContext(i);if(s)return s;if(void 0!==e)return e;throw Error(`\`${n}\` must be used within \`${t}\``)}]}function o(t,e=[]){let i=[],s=()=>{let e=i.map(t=>r.createContext(t));return function(i){let n=i?.[t]||e;return r.useMemo(()=>({[`__scope${t}`]:{...i,[t]:n}}),[i,n])}};return s.scopeName=t,[function(e,s){let o=r.createContext(s),a=i.length;i=[...i,s];let l=e=>{let{scope:i,children:s,...l}=e,u=i?.[t]?.[a]||o,h=r.useMemo(()=>l,Object.values(l));return(0,n.jsx)(u.Provider,{value:h,children:s})};return l.displayName=e+"Provider",[l,function(i,n){let l=n?.[t]?.[a]||o,u=r.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${i}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let i=()=>{let i=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let n=i.reduce((e,{useScope:i,scopeName:r})=>{let n=i(t)[`__scope${r}`];return{...e,...n}},{});return r.useMemo(()=>({[`__scope${e.scopeName}`]:n}),[n])}};return i.scopeName=e.scopeName,i}(s,...e)]}},4129:(t,e,i)=>{"use strict";i.d(e,{N:()=>n});var r=i(12115),n=globalThis?.document?r.useLayoutEffect:()=>{}},6132:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6191:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},7125:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},9484:(t,e,i)=>{"use strict";i.d(e,{C1:()=>b,bL:()=>w});var r=i(12115),n=i(3468),s=i(97602),o=i(95155),a="Progress",[l,u]=(0,n.A)(a),[h,c]=l(a),f=r.forwardRef((t,e)=>{var i,r,n,a;let{__scopeProgress:l,value:u=null,max:c,getValueLabel:f=m,...d}=t;(c||0===c)&&!v(c)&&console.error((i="".concat(c),r="Progress","Invalid prop `max` of value `".concat(i,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=v(c)?c:100;null===u||x(u,p)||console.error((n="".concat(u),a="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(a,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let w=x(u,p)?u:null,b=g(w)?f(w,p):void 0;return(0,o.jsx)(h,{scope:l,value:w,max:p,children:(0,o.jsx)(s.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":g(w)?w:void 0,"aria-valuetext":b,role:"progressbar","data-state":y(w,p),"data-value":null!=w?w:void 0,"data-max":p,...d,ref:e})})});f.displayName=a;var d="ProgressIndicator",p=r.forwardRef((t,e)=>{var i;let{__scopeProgress:r,...n}=t,a=c(d,r);return(0,o.jsx)(s.sG.div,{"data-state":y(a.value,a.max),"data-value":null!=(i=a.value)?i:void 0,"data-max":a.max,...n,ref:e})});function m(t,e){return"".concat(Math.round(t/e*100),"%")}function y(t,e){return null==t?"indeterminate":t===e?"complete":"loading"}function g(t){return"number"==typeof t}function v(t){return g(t)&&!isNaN(t)&&t>0}function x(t,e){return g(t)&&!isNaN(t)&&t<=e&&t>=0}p.displayName=d;var w=f,b=p},10489:(t,e,i)=>{"use strict";i.d(e,{b:()=>a});var r=i(12115),n=i(97602),s=i(95155),o=r.forwardRef((t,e)=>(0,s.jsx)(n.sG.label,{...t,ref:e,onMouseDown:e=>{var i;e.target.closest("button, input, select, textarea")||(null==(i=t.onMouseDown)||i.call(t,e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));o.displayName="Label";var a=o},20063:(t,e,i)=>{"use strict";var r=i(47260);i.o(r,"useParams")&&i.d(e,{useParams:function(){return r.useParams}}),i.o(r,"usePathname")&&i.d(e,{usePathname:function(){return r.usePathname}}),i.o(r,"useRouter")&&i.d(e,{useRouter:function(){return r.useRouter}}),i.o(r,"useSearchParams")&&i.d(e,{useSearchParams:function(){return r.useSearchParams}})},21786:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},26983:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},30814:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},32467:(t,e,i)=>{"use strict";i.d(e,{DX:()=>a,Dc:()=>u,TL:()=>o});var r=i(12115),n=i(94446),s=i(95155);function o(t){let e=function(t){let e=r.forwardRef((t,e)=>{let{children:i,...s}=t;if(r.isValidElement(i)){var o;let t,a,l=(o=i,(a=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(a=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(t,e){let i={...e};for(let r in e){let n=t[r],s=e[r];/^on[A-Z]/.test(r)?n&&s?i[r]=(...t)=>{let e=s(...t);return n(...t),e}:n&&(i[r]=n):"style"===r?i[r]={...n,...s}:"className"===r&&(i[r]=[n,s].filter(Boolean).join(" "))}return{...t,...i}}(s,i.props);return i.type!==r.Fragment&&(u.ref=e?(0,n.t)(e,l):l),r.cloneElement(i,u)}return r.Children.count(i)>1?r.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=r.forwardRef((t,i)=>{let{children:n,...o}=t,a=r.Children.toArray(n),l=a.find(h);if(l){let t=l.props.children,n=a.map(e=>e!==l?e:r.Children.count(t)>1?r.Children.only(null):r.isValidElement(t)?t.props.children:null);return(0,s.jsx)(e,{...o,ref:i,children:r.isValidElement(t)?r.cloneElement(t,void 0,n):null})}return(0,s.jsx)(e,{...o,ref:i,children:n})});return i.displayName=`${t}.Slot`,i}var a=o("Slot"),l=Symbol("radix.slottable");function u(t){let e=({children:t})=>(0,s.jsx)(s.Fragment,{children:t});return e.displayName=`${t}.Slottable`,e.__radixId=l,e}function h(t){return r.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},33577:(t,e,i)=>{"use strict";i.d(e,{B:()=>r});let r="undefined"!=typeof window},34212:(t,e,i)=>{"use strict";function r(t,[e,i]){return Math.min(i,Math.max(e,t))}i.d(e,{q:()=>r})},34486:(t,e,i)=>{"use strict";let r;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>sm});let s=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,r){if("function"==typeof e){let[n,s]=l(r);e=e(void 0!==i?i:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=l(r);e=e(void 0!==i?i:t.custom,n,s)}return e}function h(t,e,i){let r=t.getProps();return u(r,e,void 0!==i?i:r.custom,t)}let c=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],f=["initial",...c];function d(t){let e;return()=>(void 0===e&&(e=t()),e)}let p=d(()=>void 0!==window.ScrollTimeline);class m{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>p()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class y extends m{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function g(t,e){return t?t[e]||t.default||t:void 0}function v(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function x(t){return"function"==typeof t}function w(t,e){t.timeline=e,t.onfinish=null}let b=t=>Array.isArray(t)&&"number"==typeof t[0],A={linearEasing:void 0},T=function(t,e){let i=d(t);return()=>{var t;return null!=(t=A[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),P=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r},E=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(P(0,n-1,e))+", ";return`linear(${r.substring(0,r.length-2)})`},S=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:S([0,.65,.55,1]),circOut:S([.55,0,1,.45]),backIn:S([.31,.01,.66,-.59]),backOut:S([.33,1.53,.69,.99])},R={x:!1,y:!1};function C(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function k(t){return e=>{"touch"===e.pointerType||R.x||R.y||t(e)}}let B=(t,e)=>!!e&&(t===e||B(t,e.parentElement)),V=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,D=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function I(t){return e=>{"Enter"===e.key&&t(e)}}function j(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let U=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=I(()=>{if(L.has(i))return;j(i,"down");let t=I(()=>{j(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>j(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function O(t){return V(t)&&!(R.x||R.y)}let F=t=>1e3*t,N=t=>t/1e3,$=t=>t,_=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],W=new Set(_),z=new Set(["width","height","top","left","right","bottom",..._]),H=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),Y=t=>s(t)?t[t.length-1]||0:t,q={skipAnimations:!1,useManualTiming:!1},G=["read","resolveKeyframes","update","preRender","render","postRender"];function X(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=G.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,r=!1,n=!1,s=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){s.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,n=!1,o=!1)=>{let a=o&&r?e:i;return n&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),s.delete(t)},process:t=>{if(o=t,r){n=!0;return}r=!0,[e,i]=[i,e],e.forEach(a),e.clear(),r=!1,n&&(n=!1,l.process(t))}};return l}(s),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:c,postRender:f}=o,d=()=>{let s=q.useManualTiming?n.timestamp:performance.now();i=!1,n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),c.process(n),f.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(d))},p=()=>{i=!0,r=!0,n.isProcessing||t(d)};return{schedule:G.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,n=!1)=>(i||p(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<G.length;e++)o[G[e]].cancel(t)},state:n,steps:o}}let{schedule:K,cancel:Z,state:Q,steps:J}=X("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:$,!0);function tt(){r=void 0}let te={now:()=>(void 0===r&&te.set(Q.isProcessing||q.useManualTiming?Q.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(tt)}};function ti(t,e){-1===t.indexOf(e)&&t.push(e)}function tr(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class tn{constructor(){this.subscriptions=[]}add(t){return ti(this.subscriptions,t),()=>tr(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ts=t=>!isNaN(parseFloat(t)),to={current:void 0};class ta{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=te.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ts(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new tn);let i=this.events[t].add(e);return"change"===t?()=>{i(),K.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return to.current&&to.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function th(t,e){let i=t.getValue("willChange");if(tu(i)&&i.add)return i.add(e)}let tc=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tf="data-"+tc("framerAppearId"),td={current:!1},tp=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tm(t,e,i,r){if(t===e&&i===r)return $;let n=e=>(function(t,e,i,r,n){let s,o,a=0;do(s=tp(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tp(n(t),e,r)}let ty=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tg=t=>e=>1-t(1-e),tv=tm(.33,1.53,.69,.99),tx=tg(tv),tw=ty(tx),tb=t=>(t*=2)<1?.5*tx(t):.5*(2-Math.pow(2,-10*(t-1))),tA=t=>1-Math.sin(Math.acos(t)),tT=tg(tA),tP=ty(tA),tE=t=>/^0[^.\s]+$/u.test(t),tS=(t,e,i)=>i>e?e:i<t?t:i,tM={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tR={...tM,transform:t=>tS(0,1,t)},tC={...tM,default:1},tk=t=>Math.round(1e5*t)/1e5,tB=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tV=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tD=(t,e)=>i=>!!("string"==typeof i&&tV.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tL=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(tB);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tI=t=>tS(0,255,t),tj={...tM,transform:t=>Math.round(tI(t))},tU={test:tD("rgb","red"),parse:tL("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tj.transform(t)+", "+tj.transform(e)+", "+tj.transform(i)+", "+tk(tR.transform(r))+")"},tO={test:tD("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tU.transform},tF=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tN=tF("deg"),t$=tF("%"),t_=tF("px"),tW=tF("vh"),tz=tF("vw"),tH={...t$,parse:t=>t$.parse(t)/100,transform:t=>t$.transform(100*t)},tY={test:tD("hsl","hue"),parse:tL("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+t$.transform(tk(e))+", "+t$.transform(tk(i))+", "+tk(tR.transform(r))+")"},tq={test:t=>tU.test(t)||tO.test(t)||tY.test(t),parse:t=>tU.test(t)?tU.parse(t):tY.test(t)?tY.parse(t):tO.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tU.transform(t):tY.transform(t)},tG=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tX="number",tK="color",tZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tQ(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tZ,t=>(tq.test(t)?(r.color.push(s),n.push(tK),i.push(tq.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tX),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tJ(t){return tQ(t).values}function t0(t){let{split:e,types:i}=tQ(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tX?n+=tk(t[s]):e===tK?n+=tq.transform(t[s]):n+=t[s]}return n}}let t1=t=>"number"==typeof t?0:t,t2={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(tB))?void 0:e.length)||0)+((null==(i=t.match(tG))?void 0:i.length)||0)>0},parse:tJ,createTransformer:t0,getAnimatableNone:function(t){let e=tJ(t);return t0(t)(e.map(t1))}},t5=new Set(["brightness","contrast","saturate","opacity"]);function t6(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(tB)||[];if(!r)return t;let n=i.replace(r,""),s=+!!t5.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let t3=/\b([a-z-]*)\(.*?\)/gu,t8={...t2,getAnimatableNone:t=>{let e=t.match(t3);return e?e.map(t6).join(" "):t}},t4={...tM,transform:Math.round},t9={borderWidth:t_,borderTopWidth:t_,borderRightWidth:t_,borderBottomWidth:t_,borderLeftWidth:t_,borderRadius:t_,radius:t_,borderTopLeftRadius:t_,borderTopRightRadius:t_,borderBottomRightRadius:t_,borderBottomLeftRadius:t_,width:t_,maxWidth:t_,height:t_,maxHeight:t_,top:t_,right:t_,bottom:t_,left:t_,padding:t_,paddingTop:t_,paddingRight:t_,paddingBottom:t_,paddingLeft:t_,margin:t_,marginTop:t_,marginRight:t_,marginBottom:t_,marginLeft:t_,backgroundPositionX:t_,backgroundPositionY:t_,rotate:tN,rotateX:tN,rotateY:tN,rotateZ:tN,scale:tC,scaleX:tC,scaleY:tC,scaleZ:tC,skew:tN,skewX:tN,skewY:tN,distance:t_,translateX:t_,translateY:t_,translateZ:t_,x:t_,y:t_,z:t_,perspective:t_,transformPerspective:t_,opacity:tR,originX:tH,originY:tH,originZ:t_,zIndex:t4,size:t_,fillOpacity:tR,strokeOpacity:tR,numOctaves:t4},t7={...t9,color:tq,backgroundColor:tq,outlineColor:tq,fill:tq,stroke:tq,borderColor:tq,borderTopColor:tq,borderRightColor:tq,borderBottomColor:tq,borderLeftColor:tq,filter:t8,WebkitFilter:t8},et=t=>t7[t];function ee(t,e){let i=et(t);return i!==t8&&(i=t2),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ei=new Set(["auto","none","0"]),er=t=>t===tM||t===t_,en=(t,e)=>parseFloat(t.split(", ")[e]),es=(t,e)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let n=r.match(/^matrix3d\((.+)\)$/u);if(n)return en(n[1],e);{let e=r.match(/^matrix\((.+)\)$/u);return e?en(e[1],t):0}},eo=new Set(["x","y","z"]),ea=_.filter(t=>!eo.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:es(4,13),y:es(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,eh=!1,ec=!1;function ef(){if(ec){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var r;null==(r=t.getValue(e))||r.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ec=!1,eh=!1,eu.forEach(t=>t.complete()),eu.clear()}function ed(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ec=!0)})}class ep{constructor(t,e,i,r,n,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),eh||(eh=!0,K.read(ed),K.resolveKeyframes(ef))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;for(let n=0;n<t.length;n++)if(null===t[n])if(0===n){let n=null==r?void 0:r.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}else t[n]=t[n-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let em=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ey=t=>e=>"string"==typeof e&&e.startsWith(t),eg=ey("--"),ev=ey("var(--"),ex=t=>!!ev(t)&&ew.test(t.split("/*")[0].trim()),ew=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eA=t=>e=>e.test(t),eT=[tM,t_,t$,tN,tz,tW,{test:t=>"auto"===t,parse:t=>t}],eP=t=>eT.find(eA(t));class eE extends ep{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&ex(r=r.trim())){let n=function t(e,i,r=1){$(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=eb.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${null!=i?i:r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return em(t)?parseFloat(t):t}return ex(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!z.has(i)||2!==t.length)return;let[r,n]=t,s=eP(r),o=eP(n);if(s!==o)if(er(s)&&er(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tE(r))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!ei.has(e)&&tQ(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=ee(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let s=r.length-1,o=r[s];r[s]=el[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eS=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t2.test(t)||"0"===t)&&!t.startsWith("url(")),eM=t=>null!==t;function eR(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(eM),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return s&&void 0!==r?r:n[s]}class eC{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ed(),ef()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:i,type:r,velocity:n,delay:s,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eS(n,e),a=eS(s,e);return $(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||x(i))&&r)}(t,i,r,n))if(td.current||!s){a&&a(eR(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let ek=(t,e,i)=>t+(e-t)*i;function eB(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eV(t,e){return i=>i>0?e:t}let eD=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},eL=[tO,tU,tY],eI=t=>eL.find(e=>e.test(t));function ej(t){let e=eI(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tY&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=eB(a,r,t+1/3),s=eB(a,r,t),o=eB(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let eU=(t,e)=>{let i=ej(t),r=ej(e);if(!i||!r)return eV(t,e);let n={...i};return t=>(n.red=eD(i.red,r.red,t),n.green=eD(i.green,r.green,t),n.blue=eD(i.blue,r.blue,t),n.alpha=ek(i.alpha,r.alpha,t),tU.transform(n))},eO=(t,e)=>i=>e(t(i)),eF=(...t)=>t.reduce(eO),eN=new Set(["none","hidden"]);function e$(t,e){return i=>ek(t,e,i)}function e_(t){return"number"==typeof t?e$:"string"==typeof t?ex(t)?eV:tq.test(t)?eU:eH:Array.isArray(t)?eW:"object"==typeof t?tq.test(t)?eU:ez:eV}function eW(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>e_(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function ez(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=e_(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let eH=(t,e)=>{let i=t2.createTransformer(e),r=tQ(t),n=tQ(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?eN.has(t)&&!n.values.length||eN.has(e)&&!r.values.length?function(t,e){return eN.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eF(eW(function(t,e){var i;let r=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let o=e.types[s],a=t.indexes[o][n[o]],l=null!=(i=t.values[a])?i:0;r[s]=l,n[o]++}return r}(r,n),n.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eV(t,e))};function eY(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?ek(t,e,i):e_(t)(t,e)}function eq(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let eG={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eX(t,e){return t*Math.sqrt(1-e*e)}let eK=["duration","bounce"],eZ=["stiffness","damping","mass"];function eQ(t,e){return e.some(e=>void 0!==t[e])}function eJ(t=eG.visualDuration,e=eG.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:f,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:eG.velocity,stiffness:eG.stiffness,damping:eG.damping,mass:eG.mass,isResolvedFromDuration:!1,...t};if(!eQ(t,eZ)&&eQ(t,eK))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*tS(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:eG.mass,stiffness:r,damping:n}}else{let i=function({duration:t=eG.duration,bounce:e=eG.bounce,velocity:i=eG.velocity,mass:r=eG.mass}){let n,s;$(t<=F(eG.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tS(eG.minDamping,eG.maxDamping,o),t=tS(eG.minDuration,eG.maxDuration,N(t)),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/eX(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=eX(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=F(t),isNaN(a))return{stiffness:eG.stiffness,damping:eG.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:eG.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-N(r.velocity||0)}),m=d||0,y=h/(2*Math.sqrt(u*c)),g=a-o,x=N(Math.sqrt(u/c)),w=5>Math.abs(g);if(n||(n=w?eG.restSpeed.granular:eG.restSpeed.default),s||(s=w?eG.restDelta.granular:eG.restDelta.default),y<1){let t=eX(x,y);i=e=>a-Math.exp(-y*x*e)*((m+y*x*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-x*t)*(g+(m+x*g)*t);else{let t=x*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*x*e),r=Math.min(t*e,300);return a-i*((m+y*x*g)*Math.sinh(r)+t*g*Math.cosh(r))/t}}let b={calculatedDuration:p&&f||null,next:t=>{let e=i(t);if(p)l.done=t>=f;else{let r=0;y<1&&(r=0===t?F(m):eq(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(r)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(v(b),2e4),e=E(e=>b.next(t*e).value,t,30);return t+"ms "+e}};return b}function e0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,f,d=t[0],p={done:!1,value:d},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,v=d+g,x=void 0===o?v:o(v);x!==v&&(g=x-d);let w=t=>-g*Math.exp(-t/r),b=t=>x+w(t),A=t=>{let e=w(t),i=b(t);p.done=Math.abs(e)<=u,p.value=p.done?x:i},T=t=>{m(p.value)&&(c=t,f=eJ({keyframes:[p.value,y(p.value)],velocity:eq(b,t,p.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(f||void 0!==c||(e=!0,A(t),T(t)),void 0!==c&&t>=c)?f.next(t-c):(e||A(t),p)}}}let e1=tm(.42,0,1,1),e2=tm(0,0,.58,1),e5=tm(.42,0,.58,1),e6=t=>Array.isArray(t)&&"number"!=typeof t[0],e3={linear:$,easeIn:e1,easeInOut:e5,easeOut:e2,circIn:tA,circInOut:tP,circOut:tT,backIn:tx,backInOut:tw,backOut:tv,anticipate:tb},e8=t=>{if(b(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return tm(e,i,r,n)}return"string"==typeof t?($(void 0!==e3[t],`Invalid easing type '${t}'`),e3[t]):t};function e4({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=e6(r)?r.map(e8):e8(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if($(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||eY,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=eF(Array.isArray(e)?e[i]||$:e,s)),r.push(s)}return r}(e,r,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=P(t[r],t[r+1],i);return a[r](n)};return i?e=>u(tS(t[0],t[s-1],e)):u}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=P(0,e,r);t.push(ek(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||e5).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let e9=t=>{let e=({timestamp:e})=>t(e);return{start:()=>K.update(e,!0),stop:()=>Z(e),now:()=>Q.isProcessing?Q.timestamp:te.now()}},e7={decay:e0,inertia:e0,tween:e4,keyframes:e4,spring:eJ},it=t=>t/100;class ie extends eC{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:r,keyframes:n}=this.options,s=(null==r?void 0:r.KeyframeResolver)||ep;this.resolver=new s(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:r="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=x(r)?r:e7[r]||e4;l!==e4&&"number"!=typeof t[0]&&(e=eF(it,eY(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=v(u));let{calculatedDuration:h}=u,c=h+s;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:c,totalDuration:c*(n+1)-s}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:r,generator:n,mirroredGenerator:s,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:c,repeat:f,repeatType:d,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let y=this.currentTime-c*(this.speed>=0?1:-1),g=this.speed>=0?y<0:y>u;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,x=n;if(f){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,f+1))%2&&("reverse"===d?(i=1-i,p&&(i-=p/h)):"mirror"===d&&(x=s)),v=tS(0,1,i)*h}let w=g?{done:!1,value:a[0]}:x.next(v);o&&(w.value=o(w.value));let{done:b}=w;g||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return A&&void 0!==r&&(w.value=eR(a,this.options,r)),m&&m(w.value),A&&this.finish(),w}get duration(){let{resolved:t}=this;return t?N(t.calculatedDuration):0}get time(){return N(this.currentTime)}set time(t){t=F(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e9,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(t=this.currentTime)?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),ir=d(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:tb,backInOut:tw,circInOut:tP};class io extends eC{constructor(t){super(t);let{name:e,motionValue:i,element:r,keyframes:n}=this.options;this.resolver=new eE(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:r=300,times:n,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof s&&T()&&s in is&&(s=is[s]),x((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&T()||!e||"string"==typeof e&&(e in M||T())||b(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ie({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:t[0]},n=[],s=0;for(;!r.done&&s<2e4;)n.push((r=i.sample(s)).value),s+=10;return{times:void 0,keyframes:n,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),r=h.duration,n=h.times,s=h.ease,o="keyframes"}let h=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&T()?E(e,i):b(e)?S(e):Array.isArray(e)?e.map(e=>t(e,i)||M.easeOut):M[e]}(a,n);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:r,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:r,times:n,ease:s});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(w(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eR(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:r,times:n,type:o,ease:s,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return N(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return N(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=F(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return $;let{animation:i}=e;w(i,t)}else this.pendingTimeline=t;return $}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:r,type:n,ease:s,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ie({...u,keyframes:i,duration:r,type:n,ease:s,times:o,isGenerator:!0}),c=F(this.time);t.setWithVelocity(h.sample(c-10).value,h.sample(c).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ir()&&i&&ii.has(i)&&!a&&!l&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}}let ia={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ih={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ic=(t,{keyframes:e})=>e.length>2?iu:W.has(t)?t.startsWith("scale")?il(e[1]):ia:ih,id=(t,e,i,r={},n,s)=>o=>{let a=g(r,t)||{},l=a.delay||r.delay||0,{elapsed:u=0}=r;u-=F(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...ic(t,h)}),h.duration&&(h.duration=F(h.duration)),h.repeatDelay&&(h.repeatDelay=F(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(c=!0)),(td.current||q.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),c&&!s&&void 0!==e.get()){let t=eR(h.keyframes,a);if(void 0!==t)return K.update(()=>{h.onUpdate(t),h.onComplete()}),new y([])}return!s&&io.supports(h)?new io(h):new ie(h)};function ip(t,e,{delay:i=0,transitionOverride:r,type:n}={}){var s;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;r&&(o=r);let u=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in l){let r=t.getValue(e,null!=(s=t.latestValues[e])?s:null),n=l[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(c,e))continue;let a={delay:i,...g(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tf];if(i){let t=window.MotionHandoffAnimation(i,e,K);null!==t&&(a.startTime=t,h=!0)}}th(t,e),r.start(id(e,r,n,t.shouldReduceMotion&&z.has(e)?{type:!1}:a,t,h));let f=r.animation;f&&u.push(f)}return a&&Promise.all(u).then(()=>{K.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=h(t,e)||{};for(let e in n={...n,...i}){let i=Y(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tl(i))}}(t,a)})}),u}function im(t,e,i={}){var r;let n=h(t,e,"exit"===i.type?null==(r=t.presenceContext)?void 0:r.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let o=n?()=>Promise.all(ip(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(iy).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(im(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+r,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function iy(t,e){return t.sortNodePosition(e)}let ig=f.length,iv=[...c].reverse(),ix=c.length;function iw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ib(){return{animate:iw(!0),whileInView:iw(),whileHover:iw(),whileTap:iw(),whileDrag:iw(),whileFocus:iw(),exit:iw()}}class iA{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iT extends iA{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>im(t,e,i)));else if("string"==typeof e)r=im(t,e,i);else{let n="function"==typeof e?h(t,e,i.custom):e;r=Promise.all(ip(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ib(),r=!0,l=e=>(i,r)=>{var n;let s=h(t,r,"exit"===e?null==(n=t.presenceContext)?void 0:n.custom:void 0);if(s){let{transition:t,transitionEnd:e,...r}=s;i={...i,...r,...e}}return i};function u(u){let{props:h}=t,c=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ig;t++){let r=f[t],n=e.props[r];(a(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},d=[],p=new Set,m={},y=1/0;for(let e=0;e<ix;e++){var g,v;let f=iv[e],x=i[f],w=void 0!==h[f]?h[f]:c[f],b=a(w),A=f===u?x.isActive:null;!1===A&&(y=e);let T=w===c[f]&&w!==h[f]&&b;if(T&&r&&t.manuallyAnimateOnMount&&(T=!1),x.protectedKeys={...m},!x.isActive&&null===A||!w&&!x.prevProp||n(w)||"boolean"==typeof w)continue;let P=(g=x.prevProp,"string"==typeof(v=w)?v!==g:!!Array.isArray(v)&&!o(v,g)),E=P||f===u&&x.isActive&&!T&&b||e>y&&b,S=!1,M=Array.isArray(w)?w:[w],R=M.reduce(l(f),{});!1===A&&(R={});let{prevResolvedValues:C={}}=x,k={...C,...R},B=e=>{E=!0,p.has(e)&&(S=!0,p.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in k){let e=R[t],i=C[t];if(m.hasOwnProperty(t))continue;let r=!1;(s(e)&&s(i)?o(e,i):e===i)?void 0!==e&&p.has(t)?B(t):x.protectedKeys[t]=!0:null!=e?B(t):p.add(t)}x.prevProp=w,x.prevResolvedValues=R,x.isActive&&(m={...m,...R}),r&&t.blockInitialAnimation&&(E=!1);let V=!(T&&P)||S;E&&V&&d.push(...M.map(t=>({animation:t,options:{type:f}})))}if(p.size){let e={};p.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=r?r:null}),d.push({animation:e})}let x=!!d.length;return r&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(x=!1),r=!1,x?e(d):Promise.resolve()}return{animateChanges:u,setActive:function(e,r){var n;if(i[e].isActive===r)return Promise.resolve();null==(n=t.variantChildren)||n.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,r)}),i[e].isActive=r;let s=u(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ib(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}let iP=0;class iE extends iA{constructor(){super(...arguments),this.id=iP++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function iS(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}function iM(t){return{point:{x:t.pageX,y:t.pageY}}}let iR=t=>e=>V(e)&&t(e,iM(e));function iC(t,e,i,r){return iS(t,e,iR(i),r)}let ik=(t,e)=>Math.abs(t-e);class iB{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iL(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(ik(t.x,e.x)**2+ik(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=Q;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iV(e,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iL("pointercancel"===t.type?this.lastMoveEventInfo:iV(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!V(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=iV(iM(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=Q;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iL(s,this.history)),this.removeListeners=eF(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function iV(t,e){return e?{point:e(t.point)}:t}function iD(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iL({point:t},e){return{point:t,delta:iD(t,iI(e)),offset:iD(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=iI(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>F(.1)));)i--;if(!r)return{x:0,y:0};let s=N(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iI(t){return t[t.length-1]}function ij(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iU(t){return t.max-t.min}function iO(t,e,i,r=.5){t.origin=r,t.originPoint=ek(e.min,e.max,t.origin),t.scale=iU(i)/iU(e),t.translate=ek(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iF(t,e,i,r){iO(t.x,e.x,i.x,r?r.originX:void 0),iO(t.y,e.y,i.y,r?r.originY:void 0)}function iN(t,e,i){t.min=i.min+e.min,t.max=t.min+iU(e)}function i$(t,e,i){t.min=e.min-i.min,t.max=t.min+iU(e)}function i_(t,e,i){i$(t.x,e.x,i.x),i$(t.y,e.y,i.y)}function iW(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iz(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function iH(t,e,i){return{min:iY(t,e),max:iY(t,i)}}function iY(t,e){return"number"==typeof t?t:t[e]||0}let iq=()=>({translate:0,scale:1,origin:0,originPoint:0}),iG=()=>({x:iq(),y:iq()}),iX=()=>({min:0,max:0}),iK=()=>({x:iX(),y:iX()});function iZ(t){return[t("x"),t("y")]}function iQ({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iJ(t){return void 0===t||1===t}function i0({scale:t,scaleX:e,scaleY:i}){return!iJ(t)||!iJ(e)||!iJ(i)}function i1(t){return i0(t)||i2(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i2(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i5(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function i6(t,e=0,i=1,r,n){t.min=i5(t.min,e,i,r,n),t.max=i5(t.max,e,i,r,n)}function i3(t,{x:e,y:i}){i6(t.x,e.translate,e.scale,e.originPoint),i6(t.y,i.translate,i.scale,i.originPoint)}function i8(t,e){t.min=t.min+e,t.max=t.max+e}function i4(t,e,i,r,n=.5){let s=ek(t.min,t.max,n);i6(t,e,i,s,r)}function i9(t,e){i4(t.x,e.x,e.scaleX,e.scale,e.originX),i4(t.y,e.y,e.scaleY,e.scale,e.originY)}function i7(t,e){return iQ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let rt=({current:t})=>t?t.ownerDocument.defaultView:null,re=new WeakMap;class ri{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iK(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iB(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iM(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(R[t])return null;else return R[t]=!0,()=>{R[t]=!1};return R.x||R.y?null:(R.x=R.y=!0,()=>{R.x=R.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iZ(t=>{let e=this.getAxisMotionValue(t).get()||0;if(t$.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iU(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&K.postRender(()=>n(t,e)),th(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iZ(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:rt(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&K.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!rr(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?ek(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?ek(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,n=this.constraints;e&&ij(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:iW(t.x,i,n),y:iW(t.y,e,r)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iH(t,"left","right"),y:iH(t,"top","bottom")}}(i),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&iZ(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(r.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ij(e))return!1;let r=e.current;$(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=i7(t,i),{scroll:n}=e;return n&&(i8(r.x,n.offset.x),i8(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:iz(t.x,s.x),y:iz(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iQ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iZ(o=>{if(!rr(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return th(this.visualElement,t),i.start(id(t,i,0,e,this.visualElement,!1))}stopAnimation(){iZ(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iZ(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iZ(e=>{let{drag:i}=this.getProps();if(!rr(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-ek(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ij(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iZ(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iU(t),n=iU(e);return n>r?i=P(e.min,e.max-r,t.min):r>n&&(i=P(t.min,t.max-n,e.min)),tS(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iZ(e=>{if(!rr(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(ek(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;re.set(this.visualElement,this);let t=iC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ij(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),K.read(e);let n=iS(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iZ(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function rr(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rn extends iA{constructor(t){super(t),this.removeGroupControls=$,this.removeListeners=$,this.controls=new ri(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$}unmount(){this.removeGroupControls(),this.removeListeners()}}let rs=t=>(e,i)=>{t&&K.postRender(()=>t(e,i))};class ro extends iA{constructor(){super(...arguments),this.removePointerDownListener=$}onPointerDown(t){this.session=new iB(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rt(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rs(t),onStart:rs(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&K.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ra,rl,ru=i(95155),rh=i(12115),rc=i(75601),rf=i(60296);let rd=(0,rh.createContext)({}),rp={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rm(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ry={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!t_.test(t))return t;else t=parseFloat(t);let i=rm(t,e.target.x),r=rm(t,e.target.y);return`${i}% ${r}%`}},rg={},{schedule:rv,cancel:rx}=X(queueMicrotask,!1);class rw extends rh.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;Object.assign(rg,rA),n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rp.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,s=i.projection;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||K.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),rv.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rb(t){let[e,i]=(0,rc.xQ)(),r=(0,rh.useContext)(rf.L);return(0,ru.jsx)(rw,{...t,layoutGroup:r,switchLayoutGroup:(0,rh.useContext)(rd),isPresent:e,safeToRemove:i})}let rA={borderRadius:{...ry,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ry,borderTopRightRadius:ry,borderBottomLeftRadius:ry,borderBottomRightRadius:ry,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=t2.parse(t);if(r.length>5)return t;let n=t2.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=ek(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}},rT=(t,e)=>t.depth-e.depth;class rP{constructor(){this.children=[],this.isDirty=!1}add(t){ti(this.children,t),this.isDirty=!0}remove(t){tr(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rT),this.isDirty=!1,this.children.forEach(t)}}function rE(t){let e=tu(t)?t.get():t;return H(e)?e.toValue():e}let rS=["TopLeft","TopRight","BottomLeft","BottomRight"],rM=rS.length,rR=t=>"string"==typeof t?parseFloat(t):t,rC=t=>"number"==typeof t||t_.test(t);function rk(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rB=rD(0,.5,tT),rV=rD(.5,.95,$);function rD(t,e,i){return r=>r<t?0:r>e?1:i(P(t,e,r))}function rL(t,e){t.min=e.min,t.max=e.max}function rI(t,e){rL(t.x,e.x),rL(t.y,e.y)}function rj(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rU(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rO(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(t$.test(e)&&(e=parseFloat(e),e=ek(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=ek(s.min,s.max,r);t===s&&(a-=e),t.min=rU(t.min,e,i,a,n),t.max=rU(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let rF=["x","scaleX","originX"],rN=["y","scaleY","originY"];function r$(t,e,i,r){rO(t.x,e,rF,i?i.x:void 0,r?r.x:void 0),rO(t.y,e,rN,i?i.y:void 0,r?r.y:void 0)}function r_(t){return 0===t.translate&&1===t.scale}function rW(t){return r_(t.x)&&r_(t.y)}function rz(t,e){return t.min===e.min&&t.max===e.max}function rH(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rY(t,e){return rH(t.x,e.x)&&rH(t.y,e.y)}function rq(t){return iU(t.x)/iU(t.y)}function rG(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rX{constructor(){this.members=[]}add(t){ti(this.members,t),t.scheduleRender()}remove(t){if(tr(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rK={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rZ="undefined"!=typeof window&&void 0!==window.MotionDebug,rQ=["","X","Y","Z"],rJ={visibility:"hidden"},r0=0;function r1(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function r2({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=r0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rZ&&(rK.totalNodes=rK.resolvedTargetDeltas=rK.recalculatedProjection=0),this.nodes.forEach(r3),this.nodes.forEach(ni),this.nodes.forEach(nr),this.nodes.forEach(r8),rZ&&window.MotionDebug.record(rK)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rP)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tn),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:r,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||r)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=te.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(Z(r),t(s-e))};return K.read(r,!0),()=>Z(r)}(r,250),rp.hasAnimatedSinceResize&&(rp.hasAnimatedSinceResize=!1,this.nodes.forEach(ne))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&s&&(r||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nu,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!rY(this.targetLayout,r)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...g(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ne(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nn),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[tf];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",K,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r9);return}this.isUpdating||this.nodes.forEach(r7),this.isUpdating=!1,this.nodes.forEach(nt),this.nodes.forEach(r5),this.nodes.forEach(r6),this.clearAllSnapshots();let t=te.now();Q.delta=tS(0,1e3/60,t-Q.timestamp),Q.timestamp=t,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rv.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r4),this.sharedNodes.forEach(ns)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iK(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rW(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&(e||i1(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nf((e=r).x),nf(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iK();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(np))){let{scroll:t}=this.root;t&&(i8(i.x,t.offset.x),i8(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iK();if(rI(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let r=this.path[e],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rI(i,t),i8(i.x,n.offset.x),i8(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=iK();rI(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i9(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),i1(r.latestValues)&&i9(i,r.latestValues)}return i1(this.latestValues)&&i9(i,this.latestValues),i}removeTransform(t){let e=iK();rI(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let r=iK();rI(r,i.measurePageBox()),r$(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return i1(this.latestValues)&&r$(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,r,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iK(),this.relativeTargetOrigin=iK(),i_(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iK(),this.targetWithTransforms=iK()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,r=this.relativeTarget,n=this.relativeParent.target,iN(i.x,r.x,n.x),iN(i.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rI(this.target,this.layout.layoutBox),i3(this.target,this.targetDelta)):rI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iK(),this.relativeTargetOrigin=iK(),i_(this.relativeTargetOrigin,this.target,t.target),rI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rZ&&rK.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||i0(this.parent.latestValues)||i2(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(r=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(r=!1),r)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;rI(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,r=!1){let n,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i9(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,i3(t,s)),r&&i1(n.latestValues)&&i9(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iK());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rj(this.prevProjectionDelta.x,this.projectionDelta.x),rj(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iF(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rG(this.projectionDelta.x,this.prevProjectionDelta.x)&&rG(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rZ&&rK.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iG(),this.projectionDelta=iG(),this.projectionDeltaWithTransform=iG()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=iG();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iK(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nl));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(no(o.x,t.x,r),no(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,d,p,m,y;i_(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,p=this.relativeTargetOrigin,m=a,y=r,na(d.x,p.x,m.x,y),na(d.y,p.y,m.y,y),i&&(u=this.relativeTarget,f=i,rz(u.x,f.x)&&rz(u.y,f.y))&&(this.isProjectionDirty=!1),i||(i=iK()),rI(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=ek(0,void 0!==i.opacity?i.opacity:1,rB(r)),t.opacityExit=ek(void 0!==e.opacity?e.opacity:1,0,rV(r))):s&&(t.opacity=ek(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,r));for(let n=0;n<rM;n++){let s=`border${rS[n]}Radius`,o=rk(e,s),a=rk(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rC(o)===rC(a)?(t[s]=Math.max(ek(rR(o),rR(a),r),0),(t$.test(a)||t$.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=ek(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{rp.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let r=tu(0)?0:tl(t);return r.start(id("",r,1e3,i)),r.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&nd(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iK();let e=iU(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iU(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rI(e,i),i9(e,n),iF(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rX),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&r1("z",t,r,this.animationValues);for(let e=0;e<rQ.length;e++)r1(`rotate${rQ[e]}`,t,r,this.animationValues),r1(`skew${rQ[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rJ;let r={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rE(null==t?void 0:t.pointerEvents)||"",r.transform=n?n(this.latestValues,""):"none",r;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rE(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),r.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(r.transform=n(o,r.transform));let{x:a,y:l}=this.projectionDelta;for(let t in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?r.opacity=s===this?null!=(i=null!=(e=o.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,rg){if(void 0===o[t])continue;let{correct:e,applyTo:i}=rg[t],n="none"===r.transform?o[t]:e(o[t],s);if(i){let t=i.length;for(let e=0;e<t;e++)r[i[e]]=n}else r[t]=n}return this.options.layoutId&&(r.pointerEvents=s===this?rE(null==t?void 0:t.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(r9),this.root.sharedNodes.clear()}}}function r5(t){t.updateLayout()}function r6(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:r}=t.layout,{animationType:n}=t.options,s=i.source!==t.layout.source;"size"===n?iZ(t=>{let r=s?i.measuredBox[t]:i.layoutBox[t],n=iU(r);r.min=e[t].min,r.max=r.min+n}):nd(n,i.layoutBox,e)&&iZ(r=>{let n=s?i.measuredBox[r]:i.layoutBox[r],o=iU(e[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iG();iF(o,e,i.layoutBox);let a=iG();s?iF(a,t.applyTransform(r,!0),i.measuredBox):iF(a,e,i.layoutBox);let l=!rW(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=iK();i_(o,i.layoutBox,n.layoutBox);let a=iK();i_(a,e,s.layoutBox),rY(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function r3(t){rZ&&rK.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function r8(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function r4(t){t.clearSnapshot()}function r9(t){t.clearMeasurements()}function r7(t){t.isLayoutDirty=!1}function nt(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ne(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ni(t){t.resolveTargetDelta()}function nr(t){t.calcProjection()}function nn(t){t.resetSkewAndRotation()}function ns(t){t.removeLeadSnapshot()}function no(t,e,i){t.translate=ek(e.translate,0,i),t.scale=ek(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function na(t,e,i,r){t.min=ek(e.min,i.min,r),t.max=ek(e.max,i.max,r)}function nl(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nu={duration:.45,ease:[.4,0,.1,1]},nh=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nc=nh("applewebkit/")&&!nh("chrome/")?Math.round:$;function nf(t){t.min=nc(t.min),t.max=nc(t.max)}function nd(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rq(e)-rq(i)))}function np(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let nm=r2({attachResizeListener:(t,e)=>iS(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ny={current:void 0},ng=r2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ny.current){let t=new nm({});t.mount(window),t.setOptions({layoutScroll:!0}),ny.current=t}return ny.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nv(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&K.postRender(()=>n(e,iM(e)))}class nx extends iA{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=C(t,i),o=k(t=>{let{target:i}=t,r=e(t);if("function"!=typeof r||!i)return;let s=k(t=>{r(t),i.removeEventListener("pointerleave",s)});i.addEventListener("pointerleave",s,n)});return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,t=>(nv(this.node,t,"Start"),t=>nv(this.node,t,"End"))))}unmount(){}}class nw extends iA{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eF(iS(this.node.current,"focus",()=>this.onFocus()),iS(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nb(t,e,i){let{props:r}=t;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&K.postRender(()=>n(e,iM(e)))}class nA extends iA{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=C(t,i),o=t=>{let r=t.currentTarget;if(!O(t)||L.has(r))return;L.add(r);let s=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),O(t)&&L.has(r)&&(L.delete(r),"function"==typeof s&&s(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||B(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{D.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t.addEventListener("focus",t=>U(t,n),n)}),s}(t,t=>(nb(this.node,t,"Start"),(t,{success:e})=>nb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nT=new WeakMap,nP=new WeakMap,nE=t=>{let e=nT.get(t.target);e&&e(t)},nS=t=>{t.forEach(nE)},nM={some:0,all:1};class nR extends iA{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nM[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nP.has(i)||nP.set(i,{});let r=nP.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nS,{root:t,...e})),r[n]}(e);return nT.set(t,i),r.observe(t),()=>{nT.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nC=(0,rh.createContext)({strict:!1});var nk=i(53127);let nB=(0,rh.createContext)({});function nV(t){return n(t.animate)||f.some(e=>a(t[e]))}function nD(t){return!!(nV(t)||t.variants)}function nL(t){return Array.isArray(t)?t.join(" "):t}var nI=i(33577);let nj={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nU={};for(let t in nj)nU[t]={isEnabled:e=>nj[t].some(t=>!!e[t])};let nO=Symbol.for("motionComponentSymbol");var nF=i(59686),nN=i(86553);let n$=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n_(t){if("string"!=typeof t||t.includes("-"));else if(n$.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nW=i(94416);let nz=t=>(e,i)=>{let r=(0,rh.useContext)(nB),s=(0,rh.useContext)(nF.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},r,s,o){let a={latestValues:function(t,e,i,r){let s={},o=r(t,{});for(let t in o)s[t]=rE(o[t]);let{initial:a,animate:l}=t,h=nV(t),c=nD(t);e&&c&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let f=!!i&&!1===i.initial,d=(f=f||!1===a)?l:a;if(d&&"boolean"!=typeof d&&!n(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let r=u(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=f?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(r,s,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:r,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,r,s);return i?o():(0,nW.M)(o)},nH=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nY={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nq=_.length;function nG(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(W.has(t)){o=!0;continue}if(eg(t)){n[t]=i;continue}{let e=nH(i,t9[t]);t.startsWith("origin")?(a=!0,s[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nq;s++){let o=_[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nH(a,t9[o]);if(!l){n=!1;let e=nY[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let nX={offset:"stroke-dashoffset",array:"stroke-dasharray"},nK={offset:"strokeDashoffset",array:"strokeDasharray"};function nZ(t,e,i){return"string"==typeof t?t:t_.transform(e+i*t)}function nQ(t,{attrX:e,attrY:i,attrScale:r,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c){if(nG(t,u,c),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:f,style:d,dimensions:p}=t;f.transform&&(p&&(d.transform=f.transform),delete f.transform),p&&(void 0!==n||void 0!==s||d.transform)&&(d.transformOrigin=function(t,e,i){let r=nZ(e,t.x,t.width),n=nZ(i,t.y,t.height);return`${r} ${n}`}(p,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(f.x=e),void 0!==i&&(f.y=i),void 0!==r&&(f.scale=r),void 0!==o&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?nX:nK;t[s.offset]=t_.transform(-r);let o=t_.transform(e),a=t_.transform(i);t[s.array]=`${o} ${a}`}(f,o,a,l,!1)}let nJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),n0=()=>({...nJ(),attrs:{}}),n1=t=>"string"==typeof t&&"svg"===t.toLowerCase();function n2(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}let n5=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function n6(t,e,i,r){for(let i in n2(t,e,void 0,r),e.attrs)t.setAttribute(n5.has(i)?i:tc(i),e.attrs[i])}function n3(t,{layout:e,layoutId:i}){return W.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!rg[t]||"opacity"===t)}function n8(t,e,i){var r;let{style:n}=t,s={};for(let o in n)(tu(n[o])||e.style&&tu(e.style[o])||n3(o,t)||(null==(r=null==i?void 0:i.getValue(o))?void 0:r.liveStyle)!==void 0)&&(s[o]=n[o]);return s}function n4(t,e,i){let r=n8(t,e,i);for(let i in t)(tu(t[i])||tu(e[i]))&&(r[-1!==_.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let n9=["x","y","width","height","cx","cy","r"],n7={useVisualState:nz({scrapeMotionValuesFromProps:n4,createRenderState:n0,onUpdate:({props:t,prevProps:e,current:i,renderState:r,latestValues:n})=>{if(!i)return;let s=!!t.drag;if(!s){for(let t in n)if(W.has(t)){s=!0;break}}if(!s)return;let o=!e;if(e)for(let i=0;i<n9.length;i++){let r=n9[i];t[r]!==e[r]&&(o=!0)}o&&K.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,r),K.render(()=>{nQ(r,n,n1(i.tagName),t.transformTemplate),n6(i,r)})})}})},st={useVisualState:nz({scrapeMotionValuesFromProps:n8,createRenderState:nJ})};function se(t,e,i){for(let r in e)tu(e[r])||n3(r,i)||(t[r]=e[r])}let si=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function sr(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||si.has(t)}let sn=t=>!sr(t);try{!function(t){t&&(sn=e=>e.startsWith("on")?!sr(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let ss={current:null},so={current:!1},sa=[...eT,tq,t2],sl=t=>sa.find(eA(t)),su=new WeakMap,sh=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sc{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ep,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nV(e),this.isVariantNode=nD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,su.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),so.current||function(){if(so.current=!0,nI.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ss.current=t.matches;t.addListener(e),e()}else ss.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ss.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in su.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=W.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nU){let e=nU[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iK()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sh.length;e++){let i=sh[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(tu(n))t.addValue(r,n);else if(tu(s))t.addValue(r,tl(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,tl(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tl(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let r=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=r&&("string"==typeof r&&(em(r)||tE(r))?r=parseFloat(r):!sl(r)&&t2.test(e)&&(r=ee(t,e)),this.setBaseTarget(t,tu(r)?r.get():r)),tu(r)?r.get():r}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=u(this.props,r,null==(e=this.presenceContext)?void 0:e.custom);n&&(i=n[t])}if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tu(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new tn),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sf extends sc{constructor(){super(...arguments),this.KeyframeResolver=eE}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class sd extends sf{constructor(){super(...arguments),this.type="html",this.renderInstance=n2}readValueFromInstance(t,e){if(W.has(e)){let t=et(e);return t&&t.default||0}{let i=window.getComputedStyle(t),r=(eg(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i7(t,e)}build(t,e,i){nG(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n8(t,e,i)}}class sp extends sf{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iK}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(W.has(e)){let t=et(e);return t&&t.default||0}return e=n5.has(e)?e:tc(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n4(t,e,i)}build(t,e,i){nQ(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,r){n6(t,e,i,r)}mount(t){this.isSVGTag=n1(t.tagName),super.mount(t)}}let sm=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((ra={animation:{Feature:iT},exit:{Feature:iE},inView:{Feature:nR},tap:{Feature:nA},focus:{Feature:nw},hover:{Feature:nx},pan:{Feature:ro},drag:{Feature:rn,ProjectionNode:ng,MeasureLayout:rb},layout:{ProjectionNode:ng,MeasureLayout:rb}},rl=(t,e)=>n_(t)?new sp(e):new sd(e,{allowProjection:t!==rh.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:r,createVisualElement:n,useRender:s,useVisualState:o,Component:l}=t;function u(t,e){var i,r,u;let h,c={...(0,rh.useContext)(nk.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,rh.useContext)(rf.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:f}=c,d=function(t){let{initial:e,animate:i}=function(t,e){if(nV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,rh.useContext)(nB));return(0,rh.useMemo)(()=>({initial:e,animate:i}),[nL(e),nL(i)])}(t),p=o(t,f);if(!f&&nI.B){r=0,u=0,(0,rh.useContext)(nC).strict;let t=function(t){let{drag:e,layout:i}=nU;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);h=t.MeasureLayout,d.visualElement=function(t,e,i,r,n){var s,o;let{visualElement:a}=(0,rh.useContext)(nB),l=(0,rh.useContext)(nC),u=(0,rh.useContext)(nF.t),h=(0,rh.useContext)(nk.Q).reducedMotion,c=(0,rh.useRef)(null);r=r||l.renderer,!c.current&&r&&(c.current=r(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let f=c.current,d=(0,rh.useContext)(rd);f&&!f.projection&&n&&("html"===f.type||"svg"===f.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&ij(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}(c.current,i,n,d);let p=(0,rh.useRef)(!1);(0,rh.useInsertionEffect)(()=>{f&&p.current&&f.update(i,u)});let m=i[tf],y=(0,rh.useRef)(!!m&&!(null==(s=window.MotionHandoffIsComplete)?void 0:s.call(window,m))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,m)));return(0,nN.E)(()=>{f&&(p.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),rv.render(f.render),y.current&&f.animationState&&f.animationState.animateChanges())}),(0,rh.useEffect)(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,m)}),y.current=!1))}),f}(l,p,c,n,t.ProjectionNode)}return(0,ru.jsxs)(nB.Provider,{value:d,children:[h&&d.visualElement?(0,ru.jsx)(h,{visualElement:d.visualElement,...c}):null,s(l,t,(i=d.visualElement,(0,rh.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):ij(e)&&(e.current=t))},[i])),p,f,d.visualElement)]})}r&&function(t){for(let e in t)nU[e]={...nU[e],...t[e]}}(r),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!=(i=null!=(e=l.displayName)?e:l.name)?i:"",")"));let h=(0,rh.forwardRef)(u);return h[nO]=l,h}({...n_(t)?n7:st,preloadedFeatures:ra,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(n_(e)?function(t,e,i,r){let n=(0,rh.useMemo)(()=>{let i=n0();return nQ(i,e,n1(r),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};se(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return se(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,rh.useMemo)(()=>{let i=nJ();return nG(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(sn(n)||!0===i&&sr(n)||!e&&!sr(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==rh.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,rh.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,rh.createElement)(e,{...l,children:h})}}(e),createVisualElement:rl,Component:t})}))},35299:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42529:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47937:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},51746:(t,e)=>{"use strict";e.byteLength=function(t){var e=l(t),i=e[0],r=e[1];return(i+r)*3/4-r},e.toByteArray=function(t){var e,i,s=l(t),o=s[0],a=s[1],u=new n((o+a)*3/4-a),h=0,c=a>0?o-4:o;for(i=0;i<c;i+=4)e=r[t.charCodeAt(i)]<<18|r[t.charCodeAt(i+1)]<<12|r[t.charCodeAt(i+2)]<<6|r[t.charCodeAt(i+3)],u[h++]=e>>16&255,u[h++]=e>>8&255,u[h++]=255&e;return 2===a&&(e=r[t.charCodeAt(i)]<<2|r[t.charCodeAt(i+1)]>>4,u[h++]=255&e),1===a&&(e=r[t.charCodeAt(i)]<<10|r[t.charCodeAt(i+1)]<<4|r[t.charCodeAt(i+2)]>>2,u[h++]=e>>8&255,u[h++]=255&e),u},e.fromByteArray=function(t){for(var e,r=t.length,n=r%3,s=[],o=0,a=r-n;o<a;o+=16383)s.push(function(t,e,r){for(var n,s=[],o=e;o<r;o+=3)n=(t[o]<<16&0xff0000)+(t[o+1]<<8&65280)+(255&t[o+2]),s.push(i[n>>18&63]+i[n>>12&63]+i[n>>6&63]+i[63&n]);return s.join("")}(t,o,o+16383>a?a:o+16383));return 1===n?s.push(i[(e=t[r-1])>>2]+i[e<<4&63]+"=="):2===n&&s.push(i[(e=(t[r-2]<<8)+t[r-1])>>10]+i[e>>4&63]+i[e<<2&63]+"="),s.join("")};for(var i=[],r=[],n="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)i[o]=s[o],r[s.charCodeAt(o)]=o;function l(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var i=t.indexOf("=");-1===i&&(i=e);var r=i===e?0:4-i%4;return[i,r]}r[45]=62,r[95]=63},53127:(t,e,i)=>{"use strict";i.d(e,{Q:()=>r});let r=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},55459:(t,e)=>{e.read=function(t,e,i,r,n){var s,o,a=8*n-r-1,l=(1<<a)-1,u=l>>1,h=-7,c=i?n-1:0,f=i?-1:1,d=t[e+c];for(c+=f,s=d&(1<<-h)-1,d>>=-h,h+=a;h>0;s=256*s+t[e+c],c+=f,h-=8);for(o=s&(1<<-h)-1,s>>=-h,h+=r;h>0;o=256*o+t[e+c],c+=f,h-=8);if(0===s)s=1-u;else{if(s===l)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,r),s-=u}return(d?-1:1)*o*Math.pow(2,s-r)},e.write=function(t,e,i,r,n,s){var o,a,l,u=8*s-n-1,h=(1<<u)-1,c=h>>1,f=5960464477539062e-23*(23===n),d=r?0:s-1,p=r?1:-1,m=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),o=h):(o=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-o))<1&&(o--,l*=2),o+c>=1?e+=f/l:e+=f*Math.pow(2,1-c),e*l>=2&&(o++,l/=2),o+c>=h?(a=0,o=h):o+c>=1?(a=(e*l-1)*Math.pow(2,n),o+=c):(a=e*Math.pow(2,c-1)*Math.pow(2,n),o=0));n>=8;t[i+d]=255&a,d+=p,a/=256,n-=8);for(o=o<<n|a,u+=n;u>0;t[i+d]=255&o,d+=p,o/=256,u-=8);t[i+d-p]|=128*m}},57268:(t,e,i)=>{"use strict";i.d(e,{b:()=>u});var r=i(12115),n=i(97602),s=i(95155),o="horizontal",a=["horizontal","vertical"],l=r.forwardRef((t,e)=>{var i;let{decorative:r,orientation:l=o,...u}=t,h=(i=l,a.includes(i))?l:o;return(0,s.jsx)(n.sG.div,{"data-orientation":h,...r?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...u,ref:e})});l.displayName="Separator";var u=l},57828:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},59686:(t,e,i)=>{"use strict";i.d(e,{t:()=>r});let r=(0,i(12115).createContext)(null)},60296:(t,e,i)=>{"use strict";i.d(e,{L:()=>r});let r=(0,i(12115).createContext)({})},66218:(t,e,i)=>{"use strict";i.d(e,{jH:()=>s});var r=i(12115);i(95155);var n=r.createContext(void 0);function s(t){let e=r.useContext(n);return t||e||"ltr"}},70222:(t,e,i)=>{"use strict";i.d(e,{c:()=>n});var r=i(12115);function n(t){let e=r.useRef(t);return r.useEffect(()=>{e.current=t}),r.useMemo(()=>(...t)=>e.current?.(...t),[])}},71408:(t,e,i)=>{"use strict";i.d(e,{N:()=>g});var r=i(95155),n=i(12115),s=i(60296),o=i(94416),a=i(59686),l=i(53127);class u extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h(t){let{children:e,isPresent:i}=t,s=(0,n.useId)(),o=(0,n.useRef)(null),a=(0,n.useRef)({width:0,height:0,top:0,left:0}),{nonce:h}=(0,n.useContext)(l.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:r,left:n}=a.current;if(i||!o.current||!t||!e)return;o.current.dataset.motionPopId=s;let l=document.createElement("style");return h&&(l.nonce=h),document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[i]),(0,r.jsx)(u,{isPresent:i,childRef:o,sizeRef:a,children:n.cloneElement(e,{ref:o})})}let c=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:l,custom:u,presenceAffectsLayout:c,mode:d}=t,p=(0,o.M)(f),m=(0,n.useId)(),y=(0,n.useCallback)(t=>{for(let e of(p.set(t,!0),p.values()))if(!e)return;l&&l()},[p,l]),g=(0,n.useMemo)(()=>({id:m,initial:i,isPresent:s,custom:u,onExitComplete:y,register:t=>(p.set(t,!1),()=>p.delete(t))}),c?[Math.random(),y]:[s,y]);return(0,n.useMemo)(()=>{p.forEach((t,e)=>p.set(e,!1))},[s]),n.useEffect(()=>{s||p.size||!l||l()},[s]),"popLayout"===d&&(e=(0,r.jsx)(h,{isPresent:s,children:e})),(0,r.jsx)(a.t.Provider,{value:g,children:e})};function f(){return new Map}var d=i(75601);let p=t=>t.key||"";function m(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}var y=i(86553);let g=t=>{let{children:e,custom:i,initial:a=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:h="sync",propagate:f=!1}=t,[g,v]=(0,d.xQ)(f),x=(0,n.useMemo)(()=>m(e),[e]),w=f&&!g?[]:x.map(p),b=(0,n.useRef)(!0),A=(0,n.useRef)(x),T=(0,o.M)(()=>new Map),[P,E]=(0,n.useState)(x),[S,M]=(0,n.useState)(x);(0,y.E)(()=>{b.current=!1,A.current=x;for(let t=0;t<S.length;t++){let e=p(S[t]);w.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}},[S,w.length,w.join("-")]);let R=[];if(x!==P){let t=[...x];for(let e=0;e<S.length;e++){let i=S[e],r=p(i);w.includes(r)||(t.splice(e,0,i),R.push(i))}"wait"===h&&R.length&&(t=R),M(m(t)),E(x);return}let{forceRender:C}=(0,n.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:S.map(t=>{let e=p(t),n=(!f||!!g)&&(x===S||w.includes(e));return(0,r.jsx)(c,{isPresent:n,initial:(!b.current||!!a)&&void 0,custom:n?void 0:i,presenceAffectsLayout:u,mode:h,onExitComplete:n?void 0:()=>{if(!T.has(e))return;T.set(e,!0);let t=!0;T.forEach(e=>{e||(t=!1)}),t&&(null==C||C(),M(A.current),f&&(null==v||v()),l&&l())},children:t},e)})})}},71847:(t,e,i)=>{"use strict";i.d(e,{A:()=>l});var r=i(12115);let n=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((t,e)=>{let{color:i="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:c,...f}=t;return(0,r.createElement)("svg",{ref:e,...o,width:n,height:n,stroke:i,strokeWidth:l?24*Number(a)/Number(n):a,className:s("lucide",u),...f},[...c.map(t=>{let[e,i]=t;return(0,r.createElement)(e,i)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let i=(0,r.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,r.createElement)(a,{ref:o,iconNode:e,className:s("lucide-".concat(n(t)),l),...u})});return i.displayName="".concat(t),i}},75601:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var r=i(12115),n=i(59686);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{t&&a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},76842:(t,e,i)=>{"use strict";i.d(e,{C:()=>o});var r=i(12115),n=i(94446),s=i(4129),o=t=>{let{present:e,children:i}=t,o=function(t){var e,i;let[n,o]=r.useState(),l=r.useRef(null),u=r.useRef(t),h=r.useRef("none"),[c,f]=(e=t?"mounted":"unmounted",i={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((t,e)=>{let r=i[t][e];return null!=r?r:t},e));return r.useEffect(()=>{let t=a(l.current);h.current="mounted"===c?t:"none"},[c]),(0,s.N)(()=>{let e=l.current,i=u.current;if(i!==t){let r=h.current,n=a(e);t?f("MOUNT"):"none"===n||(null==e?void 0:e.display)==="none"?f("UNMOUNT"):i&&r!==n?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=t}},[t,f]),(0,s.N)(()=>{if(n){var t;let e,i=null!=(t=n.ownerDocument.defaultView)?t:window,r=t=>{let r=a(l.current).includes(t.animationName);if(t.target===n&&r&&(f("ANIMATION_END"),!u.current)){let t=n.style.animationFillMode;n.style.animationFillMode="forwards",e=i.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=t)})}},s=t=>{t.target===n&&(h.current=a(l.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{i.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}f("ANIMATION_END")},[n,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(t=>{l.current=t?getComputedStyle(t):null,o(t)},[])}}(e),l="function"==typeof i?i({present:o.isPresent}):r.Children.only(i),u=(0,n.s)(o.ref,function(t){var e,i;let r=null==(e=Object.getOwnPropertyDescriptor(t.props,"ref"))?void 0:e.get,n=r&&"isReactWarning"in r&&r.isReactWarning;return n?t.ref:(n=(r=null==(i=Object.getOwnPropertyDescriptor(t,"ref"))?void 0:i.get)&&"isReactWarning"in r&&r.isReactWarning)?t.props.ref:t.props.ref||t.ref}(l));return"function"==typeof i||o.isPresent?r.cloneElement(l,{ref:u}):null};function a(t){return(null==t?void 0:t.animationName)||"none"}o.displayName="Presence"},78874:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},81029:(t,e,i)=>{"use strict";let r=i(51746),n=i(55459),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');let e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,i){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return h(t)}return l(t,e,i)}function l(t,e,i){if("string"==typeof t){var r=t,n=e;if(("string"!=typeof n||""===n)&&(n="utf8"),!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);let i=0|p(r,n),s=o(i),l=s.write(r,n);return l!==i&&(s=s.slice(0,l)),s}if(ArrayBuffer.isView(t)){var s=t;if(U(s,Uint8Array)){let t=new Uint8Array(s);return f(t.buffer,t.byteOffset,t.byteLength)}return c(s)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(U(t,ArrayBuffer)||t&&U(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(U(t,SharedArrayBuffer)||t&&U(t.buffer,SharedArrayBuffer)))return f(t,e,i);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');let l=t.valueOf&&t.valueOf();if(null!=l&&l!==t)return a.from(l,e,i);let u=function(t){if(a.isBuffer(t)){let e=0|d(t.length),i=o(e);return 0===i.length||t.copy(i,0,0,e),i}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?o(0):c(t):"Buffer"===t.type&&Array.isArray(t.data)?c(t.data):void 0}(t);if(u)return u;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,i);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function h(t){return u(t),o(t<0?0:0|d(t))}function c(t){let e=t.length<0?0:0|d(t.length),i=o(e);for(let r=0;r<e;r+=1)i[r]=255&t[r];return i}function f(t,e,i){let r;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(i||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(r=void 0===e&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,e):new Uint8Array(t,e,i),a.prototype),r}function d(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||U(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);let i=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===i)return 0;let n=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return i;case"utf8":case"utf-8":return L(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*i;case"hex":return i>>>1;case"base64":return I(t).length;default:if(n)return r?-1:L(t).length;e=(""+e).toLowerCase(),n=!0}}function m(t,e,i){let n=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===i||i>this.length)&&(i=this.length),i<=0||(i>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,i){let r=t.length;(!e||e<0)&&(e=0),(!i||i<0||i>r)&&(i=r);let n="";for(let r=e;r<i;++r)n+=O[t[r]];return n}(this,e,i);case"utf8":case"utf-8":return x(this,e,i);case"ascii":return function(t,e,i){let r="";i=Math.min(t.length,i);for(let n=e;n<i;++n)r+=String.fromCharCode(127&t[n]);return r}(this,e,i);case"latin1":case"binary":return function(t,e,i){let r="";i=Math.min(t.length,i);for(let n=e;n<i;++n)r+=String.fromCharCode(t[n]);return r}(this,e,i);case"base64":var s,o,a;return s=this,o=e,a=i,0===o&&a===s.length?r.fromByteArray(s):r.fromByteArray(s.slice(o,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,i){let r=t.slice(e,i),n="";for(let t=0;t<r.length-1;t+=2)n+=String.fromCharCode(r[t]+256*r[t+1]);return n}(this,e,i);default:if(n)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function y(t,e,i){let r=t[e];t[e]=t[i],t[i]=r}function g(t,e,i,r,n){var s;if(0===t.length)return -1;if("string"==typeof i?(r=i,i=0):i>0x7fffffff?i=0x7fffffff:i<-0x80000000&&(i=-0x80000000),(s=i*=1)!=s&&(i=n?0:t.length-1),i<0&&(i=t.length+i),i>=t.length)if(n)return -1;else i=t.length-1;else if(i<0)if(!n)return -1;else i=0;if("string"==typeof e&&(e=a.from(e,r)),a.isBuffer(e))return 0===e.length?-1:v(t,e,i,r,n);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(n)return Uint8Array.prototype.indexOf.call(t,e,i);else return Uint8Array.prototype.lastIndexOf.call(t,e,i);return v(t,[e],i,r,n)}throw TypeError("val must be string, number or Buffer")}function v(t,e,i,r,n){let s,o=1,a=t.length,l=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return -1;o=2,a/=2,l/=2,i/=2}function u(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(n){let r=-1;for(s=i;s<a;s++)if(u(t,s)===u(e,-1===r?0:s-r)){if(-1===r&&(r=s),s-r+1===l)return r*o}else -1!==r&&(s-=s-r),r=-1}else for(i+l>a&&(i=a-l),s=i;s>=0;s--){let i=!0;for(let r=0;r<l;r++)if(u(t,s+r)!==u(e,r)){i=!1;break}if(i)return s}return -1}function x(t,e,i){i=Math.min(t.length,i);let r=[],n=e;for(;n<i;){let e=t[n],s=null,o=e>239?4:e>223?3:e>191?2:1;if(n+o<=i){let i,r,a,l;switch(o){case 1:e<128&&(s=e);break;case 2:(192&(i=t[n+1]))==128&&(l=(31&e)<<6|63&i)>127&&(s=l);break;case 3:i=t[n+1],r=t[n+2],(192&i)==128&&(192&r)==128&&(l=(15&e)<<12|(63&i)<<6|63&r)>2047&&(l<55296||l>57343)&&(s=l);break;case 4:i=t[n+1],r=t[n+2],a=t[n+3],(192&i)==128&&(192&r)==128&&(192&a)==128&&(l=(15&e)<<18|(63&i)<<12|(63&r)<<6|63&a)>65535&&l<1114112&&(s=l)}}null===s?(s=65533,o=1):s>65535&&(s-=65536,r.push(s>>>10&1023|55296),s=56320|1023&s),r.push(s),n+=o}var s=r;let o=s.length;if(o<=4096)return String.fromCharCode.apply(String,s);let a="",l=0;for(;l<o;)a+=String.fromCharCode.apply(String,s.slice(l,l+=4096));return a}function w(t,e,i){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>i)throw RangeError("Trying to access beyond buffer length")}function b(t,e,i,r,n,s){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<s)throw RangeError('"value" argument is out of bounds');if(i+r>t.length)throw RangeError("Index out of range")}function A(t,e,i,r,n){k(e,r,n,t,i,7);let s=Number(e&BigInt(0xffffffff));t[i++]=s,s>>=8,t[i++]=s,s>>=8,t[i++]=s,s>>=8,t[i++]=s;let o=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[i++]=o,o>>=8,t[i++]=o,o>>=8,t[i++]=o,o>>=8,t[i++]=o,i}function T(t,e,i,r,n){k(e,r,n,t,i,7);let s=Number(e&BigInt(0xffffffff));t[i+7]=s,s>>=8,t[i+6]=s,s>>=8,t[i+5]=s,s>>=8,t[i+4]=s;let o=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[i+3]=o,o>>=8,t[i+2]=o,o>>=8,t[i+1]=o,o>>=8,t[i]=o,i+8}function P(t,e,i,r,n,s){if(i+r>t.length||i<0)throw RangeError("Index out of range")}function E(t,e,i,r,s){return e*=1,i>>>=0,s||P(t,e,i,4,34028234663852886e22,-34028234663852886e22),n.write(t,e,i,r,23,4),i+4}function S(t,e,i,r,s){return e*=1,i>>>=0,s||P(t,e,i,8,17976931348623157e292,-17976931348623157e292),n.write(t,e,i,r,52,8),i+8}e.hp=a,e.IS=50,a.TYPED_ARRAY_SUPPORT=function(){try{let t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,i){return l(t,e,i)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,i){return(u(t),t<=0)?o(t):void 0!==e?"string"==typeof i?o(t).fill(e,i):o(t).fill(e):o(t)},a.allocUnsafe=function(t){return h(t)},a.allocUnsafeSlow=function(t){return h(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(U(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),U(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let i=t.length,r=e.length;for(let n=0,s=Math.min(i,r);n<s;++n)if(t[n]!==e[n]){i=t[n],r=e[n];break}return i<r?-1:+(r<i)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){let i;if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(i=0,e=0;i<t.length;++i)e+=t[i].length;let r=a.allocUnsafe(e),n=0;for(i=0;i<t.length;++i){let e=t[i];if(U(e,Uint8Array))n+e.length>r.length?(a.isBuffer(e)||(e=a.from(e)),e.copy(r,n)):Uint8Array.prototype.set.call(r,e,n);else if(a.isBuffer(e))e.copy(r,n);else throw TypeError('"list" argument must be an Array of Buffers');n+=e.length}return r},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){let t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)y(this,e,e+1);return this},a.prototype.swap32=function(){let t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},a.prototype.swap64=function(){let t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},a.prototype.toString=function(){let t=this.length;return 0===t?"":0==arguments.length?x(this,0,t):m.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){let t="",i=e.IS;return t=this.toString("hex",0,i).replace(/(.{2})/g,"$1 ").trim(),this.length>i&&(t+=" ... "),"<Buffer "+t+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(t,e,i,r,n){if(U(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===i&&(i=t?t.length:0),void 0===r&&(r=0),void 0===n&&(n=this.length),e<0||i>t.length||r<0||n>this.length)throw RangeError("out of range index");if(r>=n&&e>=i)return 0;if(r>=n)return -1;if(e>=i)return 1;if(e>>>=0,i>>>=0,r>>>=0,n>>>=0,this===t)return 0;let s=n-r,o=i-e,l=Math.min(s,o),u=this.slice(r,n),h=t.slice(e,i);for(let t=0;t<l;++t)if(u[t]!==h[t]){s=u[t],o=h[t];break}return s<o?-1:+(o<s)},a.prototype.includes=function(t,e,i){return -1!==this.indexOf(t,e,i)},a.prototype.indexOf=function(t,e,i){return g(this,t,e,i,!0)},a.prototype.lastIndexOf=function(t,e,i){return g(this,t,e,i,!1)},a.prototype.write=function(t,e,i,r){var n,s,o,a,l,u,h,c;if(void 0===e)r="utf8",i=this.length,e=0;else if(void 0===i&&"string"==typeof e)r=e,i=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(i)?(i>>>=0,void 0===r&&(r="utf8")):(r=i,i=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let f=this.length-e;if((void 0===i||i>f)&&(i=f),t.length>0&&(i<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let d=!1;for(;;)switch(r){case"hex":return function(t,e,i,r){let n;i=Number(i)||0;let s=t.length-i;r?(r=Number(r))>s&&(r=s):r=s;let o=e.length;for(r>o/2&&(r=o/2),n=0;n<r;++n){var a;let r=parseInt(e.substr(2*n,2),16);if((a=r)!=a)break;t[i+n]=r}return n}(this,t,e,i);case"utf8":case"utf-8":return n=e,s=i,j(L(t,this.length-n),this,n,s);case"ascii":case"latin1":case"binary":return o=e,a=i,j(function(t){let e=[];for(let i=0;i<t.length;++i)e.push(255&t.charCodeAt(i));return e}(t),this,o,a);case"base64":return l=e,u=i,j(I(t),this,l,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return h=e,c=i,j(function(t,e){let i,r,n=[];for(let s=0;s<t.length&&!((e-=2)<0);++s)r=(i=t.charCodeAt(s))>>8,n.push(i%256),n.push(r);return n}(t,this.length-h),this,h,c);default:if(d)throw TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),d=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){let i=this.length;t=~~t,e=void 0===e?i:~~e,t<0?(t+=i)<0&&(t=0):t>i&&(t=i),e<0?(e+=i)<0&&(e=0):e>i&&(e=i),e<t&&(e=t);let r=this.subarray(t,e);return Object.setPrototypeOf(r,a.prototype),r},a.prototype.readUintLE=a.prototype.readUIntLE=function(t,e,i){t>>>=0,e>>>=0,i||w(t,e,this.length);let r=this[t],n=1,s=0;for(;++s<e&&(n*=256);)r+=this[t+s]*n;return r},a.prototype.readUintBE=a.prototype.readUIntBE=function(t,e,i){t>>>=0,e>>>=0,i||w(t,e,this.length);let r=this[t+--e],n=1;for(;e>0&&(n*=256);)r+=this[t+--e]*n;return r},a.prototype.readUint8=a.prototype.readUInt8=function(t,e){return t>>>=0,e||w(t,1,this.length),this[t]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readBigUInt64LE=F(function(t){B(t>>>=0,"offset");let e=this[t],i=this[t+7];(void 0===e||void 0===i)&&V(t,this.length-8);let r=e+256*this[++t]+65536*this[++t]+0x1000000*this[++t],n=this[++t]+256*this[++t]+65536*this[++t]+0x1000000*i;return BigInt(r)+(BigInt(n)<<BigInt(32))}),a.prototype.readBigUInt64BE=F(function(t){B(t>>>=0,"offset");let e=this[t],i=this[t+7];(void 0===e||void 0===i)&&V(t,this.length-8);let r=0x1000000*e+65536*this[++t]+256*this[++t]+this[++t],n=0x1000000*this[++t]+65536*this[++t]+256*this[++t]+i;return(BigInt(r)<<BigInt(32))+BigInt(n)}),a.prototype.readIntLE=function(t,e,i){t>>>=0,e>>>=0,i||w(t,e,this.length);let r=this[t],n=1,s=0;for(;++s<e&&(n*=256);)r+=this[t+s]*n;return r>=(n*=128)&&(r-=Math.pow(2,8*e)),r},a.prototype.readIntBE=function(t,e,i){t>>>=0,e>>>=0,i||w(t,e,this.length);let r=e,n=1,s=this[t+--r];for(;r>0&&(n*=256);)s+=this[t+--r]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*e)),s},a.prototype.readInt8=function(t,e){return(t>>>=0,e||w(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||w(t,2,this.length);let i=this[t]|this[t+1]<<8;return 32768&i?0xffff0000|i:i},a.prototype.readInt16BE=function(t,e){t>>>=0,e||w(t,2,this.length);let i=this[t+1]|this[t]<<8;return 32768&i?0xffff0000|i:i},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readBigInt64LE=F(function(t){B(t>>>=0,"offset");let e=this[t],i=this[t+7];return(void 0===e||void 0===i)&&V(t,this.length-8),(BigInt(this[t+4]+256*this[t+5]+65536*this[t+6]+(i<<24))<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+0x1000000*this[++t])}),a.prototype.readBigInt64BE=F(function(t){B(t>>>=0,"offset");let e=this[t],i=this[t+7];return(void 0===e||void 0===i)&&V(t,this.length-8),(BigInt((e<<24)+65536*this[++t]+256*this[++t]+this[++t])<<BigInt(32))+BigInt(0x1000000*this[++t]+65536*this[++t]+256*this[++t]+i)}),a.prototype.readFloatLE=function(t,e){return t>>>=0,e||w(t,4,this.length),n.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||w(t,4,this.length),n.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||w(t,8,this.length),n.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||w(t,8,this.length),n.read(this,t,!1,52,8)},a.prototype.writeUintLE=a.prototype.writeUIntLE=function(t,e,i,r){if(t*=1,e>>>=0,i>>>=0,!r){let r=Math.pow(2,8*i)-1;b(this,t,e,i,r,0)}let n=1,s=0;for(this[e]=255&t;++s<i&&(n*=256);)this[e+s]=t/n&255;return e+i},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(t,e,i,r){if(t*=1,e>>>=0,i>>>=0,!r){let r=Math.pow(2,8*i)-1;b(this,t,e,i,r,0)}let n=i-1,s=1;for(this[e+n]=255&t;--n>=0&&(s*=256);)this[e+n]=t/s&255;return e+i},a.prototype.writeUint8=a.prototype.writeUInt8=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeBigUInt64LE=F(function(t,e=0){return A(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),a.prototype.writeBigUInt64BE=F(function(t,e=0){return T(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),a.prototype.writeIntLE=function(t,e,i,r){if(t*=1,e>>>=0,!r){let r=Math.pow(2,8*i-1);b(this,t,e,i,r-1,-r)}let n=0,s=1,o=0;for(this[e]=255&t;++n<i&&(s*=256);)t<0&&0===o&&0!==this[e+n-1]&&(o=1),this[e+n]=(t/s|0)-o&255;return e+i},a.prototype.writeIntBE=function(t,e,i,r){if(t*=1,e>>>=0,!r){let r=Math.pow(2,8*i-1);b(this,t,e,i,r-1,-r)}let n=i-1,s=1,o=0;for(this[e+n]=255&t;--n>=0&&(s*=256);)t<0&&0===o&&0!==this[e+n+1]&&(o=1),this[e+n]=(t/s|0)-o&255;return e+i},a.prototype.writeInt8=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,i){return t*=1,e>>>=0,i||b(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeBigInt64LE=F(function(t,e=0){return A(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),a.prototype.writeBigInt64BE=F(function(t,e=0){return T(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),a.prototype.writeFloatLE=function(t,e,i){return E(this,t,e,!0,i)},a.prototype.writeFloatBE=function(t,e,i){return E(this,t,e,!1,i)},a.prototype.writeDoubleLE=function(t,e,i){return S(this,t,e,!0,i)},a.prototype.writeDoubleBE=function(t,e,i){return S(this,t,e,!1,i)},a.prototype.copy=function(t,e,i,r){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(i||(i=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<i&&(r=i),r===i||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(i<0||i>=this.length)throw RangeError("Index out of range");if(r<0)throw RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-i&&(r=t.length-e+i);let n=r-i;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,i,r):Uint8Array.prototype.set.call(t,this.subarray(i,r),e),n},a.prototype.fill=function(t,e,i,r){let n;if("string"==typeof t){if("string"==typeof e?(r=e,e=0,i=this.length):"string"==typeof i&&(r=i,i=this.length),void 0!==r&&"string"!=typeof r)throw TypeError("encoding must be a string");if("string"==typeof r&&!a.isEncoding(r))throw TypeError("Unknown encoding: "+r);if(1===t.length){let e=t.charCodeAt(0);("utf8"===r&&e<128||"latin1"===r)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<i)throw RangeError("Out of range index");if(i<=e)return this;if(e>>>=0,i=void 0===i?this.length:i>>>0,t||(t=0),"number"==typeof t)for(n=e;n<i;++n)this[n]=t;else{let s=a.isBuffer(t)?t:a.from(t,r),o=s.length;if(0===o)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(n=0;n<i-e;++n)this[n+e]=s[n%o]}return this};let M={};function R(t,e,i){M[t]=class extends i{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function C(t){let e="",i=t.length,r=+("-"===t[0]);for(;i>=r+4;i-=3)e=`_${t.slice(i-3,i)}${e}`;return`${t.slice(0,i)}${e}`}function k(t,e,i,r,n,s){if(t>i||t<e){let r,n="bigint"==typeof e?"n":"";throw r=s>3?0===e||e===BigInt(0)?`>= 0${n} and < 2${n} ** ${(s+1)*8}${n}`:`>= -(2${n} ** ${(s+1)*8-1}${n}) and < 2 ** ${(s+1)*8-1}${n}`:`>= ${e}${n} and <= ${i}${n}`,new M.ERR_OUT_OF_RANGE("value",r,t)}B(n,"offset"),(void 0===r[n]||void 0===r[n+s])&&V(n,r.length-(s+1))}function B(t,e){if("number"!=typeof t)throw new M.ERR_INVALID_ARG_TYPE(e,"number",t)}function V(t,e,i){if(Math.floor(t)!==t)throw B(t,i),new M.ERR_OUT_OF_RANGE(i||"offset","an integer",t);if(e<0)throw new M.ERR_BUFFER_OUT_OF_BOUNDS;throw new M.ERR_OUT_OF_RANGE(i||"offset",`>= ${+!!i} and <= ${e}`,t)}R("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),R("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),R("ERR_OUT_OF_RANGE",function(t,e,i){let r=`The value of "${t}" is out of range.`,n=i;return Number.isInteger(i)&&Math.abs(i)>0x100000000?n=C(String(i)):"bigint"==typeof i&&(n=String(i),(i>BigInt(2)**BigInt(32)||i<-(BigInt(2)**BigInt(32)))&&(n=C(n)),n+="n"),r+=` It must be ${e}. Received ${n}`},RangeError);let D=/[^+/0-9A-Za-z-_]/g;function L(t,e){let i;e=e||1/0;let r=t.length,n=null,s=[];for(let o=0;o<r;++o){if((i=t.charCodeAt(o))>55295&&i<57344){if(!n){if(i>56319||o+1===r){(e-=3)>-1&&s.push(239,191,189);continue}n=i;continue}if(i<56320){(e-=3)>-1&&s.push(239,191,189),n=i;continue}i=(n-55296<<10|i-56320)+65536}else n&&(e-=3)>-1&&s.push(239,191,189);if(n=null,i<128){if((e-=1)<0)break;s.push(i)}else if(i<2048){if((e-=2)<0)break;s.push(i>>6|192,63&i|128)}else if(i<65536){if((e-=3)<0)break;s.push(i>>12|224,i>>6&63|128,63&i|128)}else if(i<1114112){if((e-=4)<0)break;s.push(i>>18|240,i>>12&63|128,i>>6&63|128,63&i|128)}else throw Error("Invalid code point")}return s}function I(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(D,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function j(t,e,i,r){let n;for(n=0;n<r&&!(n+i>=e.length)&&!(n>=t.length);++n)e[n+i]=t[n];return n}function U(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}let O=function(){let t="0123456789abcdef",e=Array(256);for(let i=0;i<16;++i){let r=16*i;for(let n=0;n<16;++n)e[r+n]=t[i]+t[n]}return e}();function F(t){return"undefined"==typeof BigInt?N:t}function N(){throw Error("BigInt not supported")}},83101:(t,e,i)=>{"use strict";i.d(e,{F:()=>o});var r=i(2821);let n=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,s=r.$,o=(t,e)=>i=>{var r;if((null==e?void 0:e.variants)==null)return s(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==i?void 0:i[t],r=null==a?void 0:a[t];if(null===e)return null;let s=n(e)||n(r);return o[t][s]}),u=i&&Object.entries(i).reduce((t,e)=>{let[i,r]=e;return void 0===r||(t[i]=r),t},{});return s(t,l,null==e||null==(r=e.compoundVariants)?void 0:r.reduce((t,e)=>{let{class:i,className:r,...n}=e;return Object.entries(n).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...a,...u}[e]):({...a,...u})[e]===i})?[...t,i,r]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},86553:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var r=i(12115);let n=i(33577).B?r.useLayoutEffect:r.useEffect},89442:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},89715:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},90799:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(71847).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},92556:(t,e,i)=>{"use strict";function r(t,e,{checkForDefaultPrevented:i=!0}={}){return function(r){if(t?.(r),!1===i||!r.defaultPrevented)return e?.(r)}}i.d(e,{m:()=>r})},94416:(t,e,i)=>{"use strict";i.d(e,{M:()=>n});var r=i(12115);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},94446:(t,e,i)=>{"use strict";i.d(e,{s:()=>o,t:()=>s});var r=i(12115);function n(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function s(...t){return e=>{let i=!1,r=t.map(t=>{let r=n(t,e);return i||"function"!=typeof r||(i=!0),r});if(i)return()=>{for(let e=0;e<r.length;e++){let i=r[e];"function"==typeof i?i():n(t[e],null)}}}}function o(...t){return r.useCallback(s(...t),t)}},97602:(t,e,i)=>{"use strict";i.d(e,{hO:()=>l,sG:()=>a});var r=i(12115),n=i(47650),s=i(32467),o=i(95155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let i=(0,s.TL)(`Primitive.${e}`),n=r.forwardRef((t,r)=>{let{asChild:n,...s}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?i:e,{...s,ref:r})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function l(t,e){t&&n.flushSync(()=>t.dispatchEvent(e))}}}]);