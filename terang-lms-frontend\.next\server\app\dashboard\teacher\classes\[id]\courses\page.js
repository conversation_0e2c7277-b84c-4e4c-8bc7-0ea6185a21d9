try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c41bd25e-55cb-4ae1-9d49-9f421b81bf00",e._sentryDebugIdIdentifier="sentry-dbid-c41bd25e-55cb-4ae1-9d49-9f421b81bf00")}catch(e){}(()=>{var e={};e.id=6158,e.ids=[6158],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4978:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>c});var a=s(91754);s(93491);var r=s(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,s)=>{Promise.resolve().then(s.bind(s,7346)),Promise.resolve().then(s.bind(s,21444)),Promise.resolve().then(s.bind(s,3033)),Promise.resolve().then(s.bind(s,84436))},14621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(91754);function r({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}s(93491),s(76328)},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26711:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40636:(e,t,s)=>{"use strict";s.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>i});var a=s(91754);s(93491);var r=s(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...t,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function l({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...t,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},51099:(e,t,s)=>{Promise.resolve().then(s.bind(s,51876))},51876:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var r=s(63033),n=s(1472),o=s(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\[id]\\\\courses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\courses\\page.tsx","default");let i={...r},d="workUnitAsyncStorage"in i?i.workUnitAsyncStorage:"requestAsyncStorage"in i?i.requestAsyncStorage:void 0;a="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let a,r,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher/classes/[id]/courses",componentType:"Page",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}}):l;let c=void 0,u=void 0,m=void 0,x=a},52377:(e,t,s)=>{Promise.resolve().then(s.bind(s,14621))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>v,generateImageMetadata:()=>g,generateMetadata:()=>f,generateViewport:()=>y,metadata:()=>m});var r=s(63033),n=s(18188),o=s(5434),l=s(45188),i=s(67999),d=s(4590),c=s(23064),u=s(7688);let m={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function x({children:e}){let t=await (0,c.UL)(),s=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(o.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(d.SidebarProvider,{defaultOpen:s,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(l.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(i.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let p={...r},h="workUnitAsyncStorage"in p?p.workUnitAsyncStorage:"requestAsyncStorage"in p?p.requestAsyncStorage:void 0;a=new Proxy(x,{apply:(e,t,s)=>{let a,r,n;try{let e=h?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}});let f=void 0,g=void 0,y=void 0,v=a},61715:(e,t,s)=>{Promise.resolve().then(s.bind(s,91054))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64755:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var r=s(63033),n=s(1472),o=s(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx","default");let i={...r},d="workUnitAsyncStorage"in i?i.workUnitAsyncStorage:"requestAsyncStorage"in i?i.requestAsyncStorage:void 0;a="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let a,r,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}}):l;let c=void 0,u=void 0,m=void 0,x=a},65557:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var a=s(95500),r=s(56947),n=s(26052),o=s(13636),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let i={children:["",{children:["dashboard",{children:["teacher",{children:["classes",{children:["[id]",{children:["courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,51876)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\courses\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64755)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\courses\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/teacher/classes/[id]/courses/page",pathname:"/dashboard/teacher/classes/[id]/courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},69122:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>d,yv:()=>c});var a=s(91754);s(93491);var r=s(97543),n=s(33093),o=s(87435),l=s(20388),i=s(82233);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u({className:e,size:t="default",children:s,...o}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[s,(0,a.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)(p,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,a.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:t}),(0,a.jsx)(h,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function x({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(o.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,a.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:t})]})}function p({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,s)=>{Promise.resolve().then(s.bind(s,5434)),Promise.resolve().then(s.bind(s,45188)),Promise.resolve().then(s.bind(s,67999)),Promise.resolve().then(s.bind(s,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80506:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},80601:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(91754);s(93491);var r=s(16435),n=s(25758),o=s(82233);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,asChild:s=!1,...n}){let i=s?r.DX:"span";return(0,a.jsx)(i,{"data-slot":"badge",className:(0,o.cn)(l({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},81753:(e,t,s)=>{Promise.resolve().then(s.bind(s,64755))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91054:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D});var a=s(91754),r=s(93491),n=s(21372),o=s(9260),l=s(56682),i=s(59672),d=s(40636),c=s(92681),u=s(93438),m=s(69122),x=s(80601),p=s(70067),h=s(4978),f=s(41867),g=s(41939),y=s(26711),v=s(84795),b=s(79233),j=s(57e3),w=s(80506),N=s(93626),C=s(16041),A=s.n(C),S=s(76328),P=s(81012);function D(){let e=(0,n.useRouter)(),t=(0,n.useParams)().id,[s,C]=(0,r.useState)(!0),[D,T]=(0,r.useState)(null),[k,_]=(0,r.useState)([]),[q,E]=(0,r.useState)([]),[R,I]=(0,r.useState)(""),[L,G]=(0,r.useState)(null),[M,z]=(0,r.useState)(!1),[U,B]=(0,r.useState)(""),[F,W]=(0,r.useState)(!1),H=async()=>{try{let s=S.qs.getUser();if(!s){P.oR.error("Please log in to view class courses"),e.push("/auth/sign-in");return}let a=await fetch(`/api/classes/${t}?teacherId=${s.id}`),r=await a.json();if(r.success&&r.class)T(r.class);else{P.oR.error(r.error||"Failed to fetch class data"),e.push("/dashboard/teacher/classes");return}let n=await fetch(`/api/enrollments?type=course&classId=${t}&teacherId=${s.id}`);if(n.ok){let e=await n.json();_(e.enrollments||[])}let o=await fetch(`/api/courses?teacherId=${s.id}`);if(o.ok){let e=await o.json();E(e.courses||[])}}catch(e){console.error("Error fetching data:",e),P.oR.error("Failed to fetch class courses")}finally{C(!1)}},$=async e=>{try{let t=S.qs.getUser();if(!t)return void P.oR.error("Please log in to remove course assignments");G(e);let s=await fetch(`/api/enrollments/${e}?teacherId=${t.id}&type=course`,{method:"DELETE",headers:{"Content-Type":"application/json"}}),a=await s.json();a.success?(P.oR.success("Course assignment removed successfully!"),_(t=>t.filter(t=>t.id!==e))):P.oR.error(a.error||"Failed to remove course assignment")}catch(e){console.error("Error removing assignment:",e),P.oR.error("Failed to remove course assignment")}finally{G(null)}},K=async()=>{if(!U)return void P.oR.error("Please select a course");let e=S.qs.getUser();if(!e||"teacher"!==e.role)return void P.oR.error("Access denied");W(!0);try{let s=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({type:"course",courseId:parseInt(U),classId:parseInt(t),teacherId:e.id})});if(s.ok)P.oR.success("Course assigned to class successfully"),z(!1),B(""),H();else{let e=await s.json();P.oR.error(e.error||"Failed to assign course")}}catch(e){console.error("Error assigning course:",e),P.oR.error("An error occurred while assigning course")}finally{W(!1)}},V=k.filter(e=>e.courseName.toLowerCase().includes(R.toLowerCase())||e.courseCode.toLowerCase().includes(R.toLowerCase())||e.courseDescription.toLowerCase().includes(R.toLowerCase()));return s?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(p.E,{className:"h-10 w-20"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.E,{className:"h-8 w-64"}),(0,a.jsx)(p.E,{className:"h-4 w-96"})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(p.E,{className:"h-6 w-48"}),(0,a.jsx)(p.E,{className:"h-4 w-64"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(p.E,{className:"h-10 w-full"}),(0,a.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)(p.E,{className:"h-16 w-full"},t))})]})})]})]}):D?(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ClassCoursesPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(A(),{href:`/dashboard/teacher/classes/${t}`,"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:[D.name," - Assigned Courses"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage courses assigned to this class"})]})]}),(0,a.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(f.A,{className:"h-5 w-5","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"}),"Class Information"]})}),(0,a.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:D.studentCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Students"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:k.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Assigned Courses"})]}),(0,a.jsxs)("div",{className:"text-center p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[k.length>0?Math.round(k.reduce((e,t)=>e+t.completionRate,0)/k.length):0,"%"]}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Avg. Completion"})]})]})})]}),(0,a.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Assigned Courses"}),(0,a.jsxs)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:["Courses currently assigned to ",D.name]})]}),(0,a.jsxs)(l.$,{onClick:()=>z(!0),"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Assign Course"]})]})}),(0,a.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(i.p,{placeholder:"Search assigned courses...",value:R,onChange:e=>I(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),V.length>0?(0,a.jsxs)(d.Table,{children:[(0,a.jsx)(d.TableHeader,{children:(0,a.jsxs)(d.TableRow,{children:[(0,a.jsx)(d.TableHead,{children:"Course"}),(0,a.jsx)(d.TableHead,{children:"Code"}),(0,a.jsx)(d.TableHead,{children:"Students"}),(0,a.jsx)(d.TableHead,{children:"Completion"}),(0,a.jsx)(d.TableHead,{children:"Assigned Date"}),(0,a.jsx)(d.TableHead,{className:"text-right",children:"Actions"})]})}),(0,a.jsx)(d.TableBody,{children:V.map(e=>(0,a.jsxs)(d.TableRow,{children:[(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.courseName}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground line-clamp-1",children:e.courseDescription})]})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsx)(x.E,{variant:"outline",children:e.courseCode})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"}),e.studentCount]})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${e.completionRate}%`}})}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.completionRate,"%"]})]})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),new Date(e.enrolledAt).toLocaleDateString()]})}),(0,a.jsx)(d.TableCell,{className:"text-right",children:(0,a.jsxs)(c.rI,{children:[(0,a.jsx)(c.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(c.SQ,{align:"end",children:[(0,a.jsx)(c._2,{asChild:!0,children:(0,a.jsxs)(A(),{href:`/dashboard/teacher/courses/${e.courseId}`,children:[(0,a.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"View Course"]})}),(0,a.jsxs)(c._2,{className:"text-red-600",onClick:()=>$(e.id),disabled:L===e.id,children:[L===e.id?(0,a.jsx)("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent"}):(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Remove Assignment"]})]})]})})]},e.id))})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(f.A,{className:"mx-auto h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No courses assigned"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:R?"No courses match your search criteria.":"This class doesn't have any assigned courses yet."}),!R&&(0,a.jsxs)(l.$,{onClick:()=>z(!0),children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Assign First Course"]})]})]})})]}),(0,a.jsx)(u.lG,{open:M,onOpenChange:z,"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(u.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(u.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(u.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:["Assign Course to ",D?.name]}),(0,a.jsx)(u.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"page.tsx",children:"Select a course to assign to this class."})]}),(0,a.jsx)("div",{className:"grid gap-4 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Course"}),(0,a.jsxs)(m.l6,{value:U,onValueChange:B,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(m.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(m.yv,{placeholder:"Select a course","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(m.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:q.filter(e=>!k.some(t=>t.courseId===e.id)).map(e=>(0,a.jsx)(m.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:e.courseCode})]})},e.id))})]})]})}),(0,a.jsxs)(u.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>{z(!1),B("")},disabled:F,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"}),(0,a.jsx)(l.$,{onClick:K,disabled:F,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:F?"Assigning...":"Assign Course"})]})]})})]}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Class not found"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"The class you're looking for doesn't exist."}),(0,a.jsx)(A(),{href:"/dashboard/teacher/classes",children:(0,a.jsxs)(l.$,{className:"mt-4",children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Back to Classes"]})})]})})}},93438:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>x,L3:()=>p,c7:()=>m,lG:()=>l,rr:()=>h,zM:()=>i});var a=s(91754);s(93491);var r=s(18227),n=s(31619),o=s(82233);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function i({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u({className:e,children:t,...s}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8134,8634],()=>s(65557));module.exports=a})();
//# sourceMappingURL=page.js.map