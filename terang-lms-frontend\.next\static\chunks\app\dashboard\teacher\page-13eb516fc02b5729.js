try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="46342291-ec00-4fdb-a5b8-fb3bb00b8ae0",e._sentryDebugIdIdentifier="sentry-dbid-46342291-ec00-4fdb-a5b8-fb3bb00b8ae0")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7e3],{1524:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3468:(e,t,s)=>{"use strict";s.d(t,{A:()=>d,q:()=>r});var a=s(12115),n=s(95155);function r(e,t){let s=a.createContext(t),r=e=>{let{children:t,...r}=e,d=a.useMemo(()=>r,Object.values(r));return(0,n.jsx)(s.Provider,{value:d,children:t})};return r.displayName=e+"Provider",[r,function(n){let r=a.useContext(s);if(r)return r;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function d(e,t=[]){let s=[],r=()=>{let t=s.map(e=>a.createContext(e));return function(s){let n=s?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...s,[e]:n}}),[s,n])}};return r.scopeName=e,[function(t,r){let d=a.createContext(r),l=s.length;s=[...s,r];let i=t=>{let{scope:s,children:r,...i}=t,c=s?.[e]?.[l]||d,o=a.useMemo(()=>i,Object.values(i));return(0,n.jsx)(c.Provider,{value:o,children:r})};return i.displayName=t+"Provider",[i,function(s,n){let i=n?.[e]?.[l]||d,c=a.useContext(i);if(c)return c;if(void 0!==r)return r;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=s.reduce((t,{useScope:s,scopeName:a})=>{let n=s(e)[`__scope${a}`];return{...t,...n}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return s.scopeName=t.scopeName,s}(r,...t)]}},4129:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var a=s(12115),n=globalThis?.document?a.useLayoutEffect:()=>{}},4234:(e,t,s)=>{Promise.resolve().then(s.bind(s,93791))},6132:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6191:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},19408:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},23664:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},34212:(e,t,s)=>{"use strict";function a(e,[t,s]){return Math.min(s,Math.max(t,e))}s.d(t,{q:()=>a})},47937:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},66218:(e,t,s)=>{"use strict";s.d(t,{jH:()=>r});var a=s(12115);s(95155);var n=a.createContext(void 0);function r(e){let t=a.useContext(n);return e||t||"ltr"}},70222:(e,t,s)=>{"use strict";s.d(t,{c:()=>n});var a=s(12115);function n(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}},76842:(e,t,s)=>{"use strict";s.d(t,{C:()=>d});var a=s(12115),n=s(94446),r=s(4129),d=e=>{let{present:t,children:s}=e,d=function(e){var t,s;let[n,d]=a.useState(),i=a.useRef(null),c=a.useRef(e),o=a.useRef("none"),[u,m]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let a=s[e][t];return null!=a?a:e},t));return a.useEffect(()=>{let e=l(i.current);o.current="mounted"===u?e:"none"},[u]),(0,r.N)(()=>{let t=i.current,s=c.current;if(s!==e){let a=o.current,n=l(t);e?m("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):s&&a!==n?m("ANIMATION_OUT"):m("UNMOUNT"),c.current=e}},[e,m]),(0,r.N)(()=>{if(n){var e;let t,s=null!=(e=n.ownerDocument.defaultView)?e:window,a=e=>{let a=l(i.current).includes(e.animationName);if(e.target===n&&a&&(m("ANIMATION_END"),!c.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=s.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},r=e=>{e.target===n&&(o.current=l(i.current))};return n.addEventListener("animationstart",r),n.addEventListener("animationcancel",a),n.addEventListener("animationend",a),()=>{s.clearTimeout(t),n.removeEventListener("animationstart",r),n.removeEventListener("animationcancel",a),n.removeEventListener("animationend",a)}}m("ANIMATION_END")},[n,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{i.current=e?getComputedStyle(e):null,d(e)},[])}}(t),i="function"==typeof s?s({present:d.isPresent}):a.Children.only(s),c=(0,n.s)(d.ref,function(e){var t,s;let a=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,n=a&&"isReactWarning"in a&&a.isReactWarning;return n?e.ref:(n=(a=null==(s=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:s.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof s||d.isPresent?a.cloneElement(i,{ref:c}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}d.displayName="Presence"},78192:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},91169:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},92556:(e,t,s)=>{"use strict";function a(e,t,{checkForDefaultPrevented:s=!0}={}){return function(a){if(e?.(a),!1===s||!a.defaultPrevented)return t?.(a)}}s.d(t,{m:()=>a})},93791:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(95155),n=s(66094),r=s(20764),d=s(88021),l=s(78192),i=s(6191),c=s(91169),o=s(47937),u=s(1524),m=s(52619),x=s.n(m),f=s(47886),y=s(15868),p=s(12115);function h(){let[e,t]=(0,p.useState)(f.qs.getUser()),[s,m]=(0,p.useState)(!0);if((0,p.useEffect)(()=>{t(f.qs.getUser()),m(!1)},[]),s)return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading..."});if(!(null==e?void 0:e.institutionId))return(0,a.jsx)(y.A,{userRole:"teacher"});let h={totalClasses:5,totalCourses:12,totalStudents:150,completionRate:78};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"TeacherDashboard","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Teacher Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your classes, courses, and track student progress"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x(),{href:"/dashboard/teacher/courses/generate","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(r.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Bot","data-sentry-source-file":"page.tsx"}),"AI Generator"]})}),(0,a.jsx)(x(),{href:"/dashboard/teacher/courses/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(r.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Create Course"]})})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Classes"}),(0,a.jsx)(c.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Users","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.totalClasses}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Active classes"})]})]}),(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Courses"}),(0,a.jsx)(o.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.totalCourses}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Published courses"})]})]}),(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Students"}),(0,a.jsx)(c.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Users","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.totalStudents}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Enrolled students"})]})]}),(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Completion Rate"}),(0,a.jsx)(u.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[h.completionRate,"%"]}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Average completion"})]})]})]}),(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Recent Courses"}),(0,a.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your latest course activities"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"space-y-4",children:[{id:1,name:"Introduction to Mathematics",type:"self_paced",students:45,completion:85,status:"active"},{id:2,name:"Basic Physics",type:"verified",students:32,completion:72,status:"active"},{id:3,name:"Chemistry Fundamentals",type:"self_paced",students:28,completion:90,status:"completed"}].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.E,{variant:"verified"===e.type?"default":"secondary",children:e.type}),(0,a.jsx)(d.E,{variant:"active"===e.status?"default":"outline",children:e.status}),(0,a.jsxs)("span",{className:"text-muted-foreground text-sm",children:[e.students," students"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.completion,"%"]}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"completion"})]}),(0,a.jsx)(x(),{href:"/dashboard/teacher/courses/".concat(e.id),children:(0,a.jsx)(r.$,{variant:"outline",size:"sm",children:"View"})})]})]},e.id))}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(x(),{href:"/dashboard/teacher/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(r.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"View All Courses"})})})]})]})]})}},97602:(e,t,s)=>{"use strict";s.d(t,{hO:()=>i,sG:()=>l});var a=s(12115),n=s(47650),r=s(32467),d=s(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,r.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...r}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(n?s:t,{...r,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function i(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,9034,3984,4850,8441,3840,7358],()=>t(4234)),_N_E=e.O()}]);