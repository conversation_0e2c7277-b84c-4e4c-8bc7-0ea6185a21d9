try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="920f705c-4ac2-44f0-92b4-12f218c09bc8",e._sentryDebugIdIdentifier="sentry-dbid-920f705c-4ac2-44f0-92b4-12f218c09bc8")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1833],{1524:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},6191:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},19408:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>i});var s=a(95155);a(12115);var r=a(32467),n=a(83101),d=a(64269);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,d.cn)(i({variant:a,size:n,className:t})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},35299:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},38270:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(95155),r=a(12115),n=a(66094),d=a(20764),i=a(88021),l=a(6191),o=a(19408),c=a(91169),u=a(78519),x=a(1524),m=a(35299),f=a(52619),y=a.n(f);function p(){let[e,t]=(0,r.useState)([]),[a,f]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/institutions?limit=3"),a=await e.json();a.success&&t(a.data.institutions)}catch(e){console.error("Failed to fetch recent institutions",e)}finally{f(!1)}})()},[]),(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"AdminDashboard","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Super Admin Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage institutions, subscriptions, and system-wide analytics"})]}),(0,s.jsx)(y(),{href:"/dashboard/admin/institutions/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(d.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Add Institution"]})})]}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Institutions"}),(0,s.jsx)(o.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Building2","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:24}),(0,s.jsx)("p",{className:"text-muted-foreground text-xs",children:"+2 from last month"})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Users"}),(0,s.jsx)(c.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Users","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:1250..toLocaleString()}),(0,s.jsx)("p",{className:"text-muted-foreground text-xs",children:"+180 from last month"})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Revenue"}),(0,s.jsx)(u.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"CreditCard","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold",children:["Rp ","125.0","M"]}),(0,s.jsx)("p",{className:"text-muted-foreground text-xs",children:"+12% from last month"})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Active Subscriptions"}),(0,s.jsx)(x.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:22}),(0,s.jsx)("p",{className:"text-muted-foreground text-xs",children:"+1 from last month"})]})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Recent Institutions"}),(0,s.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Latest institutions added to the platform"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"space-y-4",children:a?(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,s.jsx)(m.A,{className:"mr-2 h-6 w-6 animate-spin"}),(0,s.jsx)("span",{children:"Loading recent institutions..."})]}):e.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.E,{variant:"outline",children:e.type}),(0,s.jsx)(i.E,{variant:"enterprise"===e.subscription_plan?"default":"secondary",children:e.subscription_plan}),(0,s.jsxs)("span",{className:"text-muted-foreground text-sm",children:[e.student_count," students"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.E,{variant:"paid"===e.payment_status?"default":"destructive",children:e.payment_status}),(0,s.jsx)(y(),{href:"/dashboard/admin/institutions/".concat(e.id),children:(0,s.jsx)(d.$,{variant:"outline",size:"sm",children:"View"})})]})]},e.id))}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(y(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(d.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"View All Institutions"})})})]})]})]})}},43043:(e,t,a)=>{Promise.resolve().then(a.bind(a,38270))},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>d});var s=a(2821),r=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function d(e){var t,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=s;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][d])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d,wL:()=>c});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},78519:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},88021:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(95155);a(12115);var r=a(32467),n=a(83101),d=a(64269);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:n=!1,...l}=e,o=n?r.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,d.cn)(i({variant:a}),t),...l,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},91169:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4850,8441,3840,7358],()=>t(43043)),_N_E=e.O()}]);