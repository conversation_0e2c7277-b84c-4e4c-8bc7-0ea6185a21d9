try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="6d6f4b47-5beb-489e-82ce-eba45633d063",e._sentryDebugIdIdentifier="sentry-dbid-6d6f4b47-5beb-489e-82ce-eba45633d063")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6758],{6758:(e,t,r)=>{function n(e){var t=e.match(/^\s*\S/);return e.skipToEnd(),t?"error":null}r.r(t),r.d(t,{asciiArmor:()=>s});let s={name:"asciiarmor",token:function(e,t){var r;if("top"==t.state)return e.sol()&&(r=e.match(/^-----BEGIN (.*)?-----\s*$/))?(t.state="headers",t.type=r[1],"tag"):n(e);if("headers"==t.state)if(e.sol()&&e.match(/^\w+:/))return t.state="header","atom";else{var s=n(e);return s&&(t.state="body"),s}if("header"==t.state)return e.skipToEnd(),t.state="headers","string";if("body"==t.state)if(e.sol()&&(r=e.match(/^-----END (.*)?-----\s*$/)))return r[1]!=t.type?"error":(t.state="end","tag");else return e.eatWhile(/[A-Za-z0-9+\/=]/)?null:(e.next(),"error");if("end"==t.state)return n(e)},blankLine:function(e){"headers"==e.state&&(e.state="body")},startState:function(){return{state:"top",type:null}}}}}]);