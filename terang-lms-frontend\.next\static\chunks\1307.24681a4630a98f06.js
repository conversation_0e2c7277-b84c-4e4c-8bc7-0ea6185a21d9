try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f306a539-31e7-407c-b707-1d5bd6b049e3",e._sentryDebugIdIdentifier="sentry-dbid-f306a539-31e7-407c-b707-1d5bd6b049e3")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1307],{31307:(e,t,r)=>{function n(e){for(var t={},r=0,n=e.length;r<n;++r)t[e[r]]=!0;return t}r.r(t),r.d(t,{eiffel:()=>s});var a=n(["note","across","when","variant","until","unique","undefine","then","strip","select","retry","rescue","require","rename","reference","redefine","prefix","once","old","obsolete","loop","local","like","is","inspect","infix","include","if","frozen","from","external","export","ensure","end","elseif","else","do","creation","create","check","alias","agent","separate","invariant","inherit","indexing","feature","expanded","deferred","class","Void","True","Result","Precursor","False","Current","create","attached","detachable","as","and","implies","not","or"]),i=n([":=","and then","and","or","<<",">>"]);function o(e,t){if(e.eatSpace())return null;var r,n,a,i=e.next();if('"'==i||"'"==i){return r=i,a=function(e,t){for(var n,a=!1;null!=(n=e.next());){if(n==r&&!a){t.tokenize.pop();break}a=!a&&"%"==n}return"string"},t.tokenize.push(a),a(e,t)}if("-"==i&&e.eat("-"))return e.skipToEnd(),"comment";if(":"==i&&e.eat("="))return"operator";if(/[0-9]/.test(i))return e.eatWhile(/[xXbBCc0-9\.]/),e.eat(/[\?\!]/),"variable";if(/[a-zA-Z_0-9]/.test(i))return e.eatWhile(/[a-zA-Z_0-9]/),e.eat(/[\?\!]/),"variable";else if(/[=+\-\/*^%<>~]/.test(i))return e.eatWhile(/[=+\-\/*^%<>~]/),"operator";else return null}let s={name:"eiffel",startState:function(){return{tokenize:[o]}},token:function(e,t){var r=t.tokenize[t.tokenize.length-1](e,t);if("variable"==r){var n=e.current();r=a.propertyIsEnumerable(e.current())?"keyword":i.propertyIsEnumerable(e.current())?"operator":/^[A-Z][A-Z_0-9]*$/g.test(n)?"tag":/^0[bB][0-1]+$/g.test(n)||/^0[cC][0-7]+$/g.test(n)||/^0[xX][a-fA-F0-9]+$/g.test(n)||/^([0-9]+\.[0-9]*)|([0-9]*\.[0-9]+)$/g.test(n)||/^[0-9]+$/g.test(n)?"number":"variable"}return r},languageData:{commentTokens:{line:"--"}}}}}]);