try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="aff38a4d-453a-412f-aaab-729a9c51bb26",e._sentryDebugIdIdentifier="sentry-dbid-aff38a4d-453a-412f-aaab-729a9c51bb26")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2191],{62191:(e,t,r)=>{r.r(t),r.d(t,{erlang:()=>I});var n=["-type","-spec","-export_type","-opaque"],i=["after","begin","catch","case","cond","end","fun","if","let","of","query","receive","try","when"],a=/[\->,;]/,o=["->",";",","],s=["and","andalso","band","bnot","bor","bsl","bsr","bxor","div","not","or","orelse","rem","xor"],u=/[\+\-\*\/<>=\|:!]/,c=["=","+","-","*","/",">",">=","<","=<","=:=","==","=/=","/=","||","<-","!"],l=/[<\(\[\{]/,f=["<<","(","[","{"],_=/[>\)\]\}]/,p=["}","]",")",">>"],d=["is_atom","is_binary","is_bitstring","is_boolean","is_float","is_function","is_integer","is_list","is_number","is_pid","is_port","is_record","is_reference","is_tuple","atom","binary","bitstring","boolean","function","integer","list","number","pid","port","record","reference","tuple"],m=["abs","adler32","adler32_combine","alive","apply","atom_to_binary","atom_to_list","binary_to_atom","binary_to_existing_atom","binary_to_list","binary_to_term","bit_size","bitstring_to_list","byte_size","check_process_code","contact_binary","crc32","crc32_combine","date","decode_packet","delete_module","disconnect_node","element","erase","exit","float","float_to_list","garbage_collect","get","get_keys","group_leader","halt","hd","integer_to_list","internal_bif","iolist_size","iolist_to_binary","is_alive","is_atom","is_binary","is_bitstring","is_boolean","is_float","is_function","is_integer","is_list","is_number","is_pid","is_port","is_process_alive","is_record","is_reference","is_tuple","length","link","list_to_atom","list_to_binary","list_to_bitstring","list_to_existing_atom","list_to_float","list_to_integer","list_to_pid","list_to_tuple","load_module","make_ref","module_loaded","monitor_node","node","node_link","node_unlink","nodes","notalive","now","open_port","pid_to_list","port_close","port_command","port_connect","port_control","pre_loaded","process_flag","process_info","processes","purge_module","put","register","registered","round","self","setelement","size","spawn","spawn_link","spawn_monitor","spawn_opt","split_binary","statistics","term_to_binary","time","throw","tl","trunc","tuple_size","tuple_to_list","unlink","unregister","whereis"],b=/[\w@Ø-ÞÀ-Öß-öø-ÿ]/,g=/[0-7]{1,3}|[bdefnrstv\\"']|\^[a-zA-Z]|x[0-9a-zA-Z]{2}|x{[0-9a-zA-Z]+}/;function k(e,t,r){if(1==e.current().length&&t.test(e.current())){for(e.backUp(1);t.test(e.peek());)if(e.next(),x(e.current(),r))return!0;e.backUp(e.current().length-1)}return!1}function h(e,t,r){if(1==e.current().length&&t.test(e.current())){for(;t.test(e.peek());)e.next();for(;0<e.current().length;)if(x(e.current(),r))return!0;else e.backUp(1);e.next()}return!1}function y(e){return v(e,'"',"\\")}function w(e){return v(e,"'","\\")}function v(e,t,r){for(;!e.eol();){var n=e.next();if(n==t)return!0;n==r&&e.next()}return!1}function x(e,t){return -1<t.indexOf(e)}function S(e,t,r){var n,i,a,o,s,u,c;switch(a=e,"comment"!=(o=(n=r,z((i=t).current(),i.column(),i.indentation(),n))).type&&"whitespace"!=o.type&&(s=a.tokenStack,u=o,0<(c=s.length-1)&&"record"===s[c].type&&"dot"===u.type?s.pop():(0<c&&"group"===s[c].type&&s.pop(),s.push(u)),a.tokenStack=s,a.tokenStack=function(e){if(!e.length)return e;var t=e.length-1;if("dot"===e[t].type)return[];if(t>1&&"fun"===e[t].type&&"fun"===e[t-1].token)return e.slice(0,t-1);switch(e[t].token){case"}":return E(e,{g:["{"]});case"]":return E(e,{i:["["]});case")":return E(e,{i:["("]});case">>":return E(e,{i:["<<"]});case"end":return E(e,{i:["begin","case","fun","if","receive","try"]});case",":return E(e,{e:["begin","try","when","->",",","(","[","{","<<"]});case"->":return E(e,{r:["when"],m:["try","if","case","receive"]});case";":return E(e,{E:["case","fun","if","receive","try","when"]});case"catch":return E(e,{e:["try"]});case"of":return E(e,{e:["case"]});case"after":return E(e,{e:["receive","try"]});default:return e}}(a.tokenStack)),r){case"atom":case"boolean":return"atom";case"attribute":return"attribute";case"builtin":return"builtin";case"close_paren":case"colon":case"dot":case"open_paren":case"separator":default:return null;case"comment":return"comment";case"error":return"error";case"fun":return"meta";case"function":return"tag";case"guard":return"property";case"keyword":return"keyword";case"macro":return"macroName";case"number":return"number";case"operator":return"operator";case"record":return"bracket";case"string":return"string";case"type":return"def";case"variable":return"variable"}}function z(e,t,r,n){return{token:e,column:t,indent:r,type:n}}function W(e,t){var r=e.tokenStack.length,n=t||1;return!(r<n)&&e.tokenStack[r-n]}function E(e,t){for(var r in t)for(var n=e.length-1,i=t[r],a=n-1;-1<a;a--)if(x(e[a].token,i)){var o,s=e.slice(0,a);switch(r){case"m":return s.concat(e[a]).concat(e[n]);case"r":return s.concat(e[n]);case"i":return s;case"g":return s.concat(z(o="group",0,0,o));case"E":case"e":return s.concat(e[a])}}return"E"==r?[]:e}function U(e,t){var r=e.tokenStack,n=A(r,"token",t);return!!D(r[n])&&r[n]}function A(e,t,r){for(var n=e.length-1;-1<n;n--)if(x(e[n][t],r))return n;return!1}function D(e){return!1!==e&&null!=e}let I={name:"erlang",startState:()=>({tokenStack:[],in_string:!1,in_atom:!1}),token:function(e,t){if(t.in_string)return t.in_string=!y(e),S(t,e,"string");if(t.in_atom)return t.in_atom=!w(e),S(t,e,"atom");if(e.eatSpace())return S(t,e,"whitespace");if(!W(t)&&e.match(/-\s*[a-zß-öø-ÿ][\wØ-ÞÀ-Öß-öø-ÿ]*/))if(x(e.current(),n))return S(t,e,"type");else return S(t,e,"attribute");var r=e.next();if("%"==r)return e.skipToEnd(),S(t,e,"comment");if(":"==r)return S(t,e,"colon");if("?"==r)return e.eatSpace(),e.eatWhile(b),S(t,e,"macro");if("#"==r)return e.eatSpace(),e.eatWhile(b),S(t,e,"record");if("$"==r)return"\\"!=e.next()||e.match(g)?S(t,e,"number"):S(t,e,"error");if("."==r)return S(t,e,"dot");if("'"==r){if(!(t.in_atom=!w(e))){if(e.match(/\s*\/\s*[0-9]/,!1))return e.match(/\s*\/\s*[0-9]/,!0),S(t,e,"fun");if(e.match(/\s*\(/,!1)||e.match(/\s*:/,!1))return S(t,e,"function")}return S(t,e,"atom")}if('"'==r)return t.in_string=!y(e),S(t,e,"string");if(/[A-Z_Ø-ÞÀ-Ö]/.test(r))return e.eatWhile(b),S(t,e,"variable");if(/[a-z_ß-öø-ÿ]/.test(r)){if(e.eatWhile(b),e.match(/\s*\/\s*[0-9]/,!1))return e.match(/\s*\/\s*[0-9]/,!0),S(t,e,"fun");var v,z=e.current();if(x(z,i))return S(t,e,"keyword");if(x(z,s))return S(t,e,"operator");if(e.match(/\s*\(/,!1))if(x(z,m)&&(":"!=W(t).token||"erlang"==W(t,2).token))return S(t,e,"builtin");else if(x(z,d))return S(t,e,"guard");else return S(t,e,"function");else{if(":"==((v=e.match(/^\s*([^\s%])/,!1))?v[1]:""))if("erlang"==z)return S(t,e,"builtin");else return S(t,e,"function");else if(x(z,["true","false"]))return S(t,e,"boolean");else return S(t,e,"atom")}}var E=/[0-9]/;return E.test(r)?(e.eatWhile(E),e.eat("#")?e.eatWhile(/[0-9a-zA-Z]/)||e.backUp(1):e.eat(".")&&(e.eatWhile(E)?e.eat(/[eE]/)&&(e.eat(/[-+]/)?e.eatWhile(E)||e.backUp(2):e.eatWhile(E)||e.backUp(1)):e.backUp(1)),S(t,e,"number")):k(e,l,f)?S(t,e,"open_paren"):k(e,_,p)?S(t,e,"close_paren"):h(e,a,o)?S(t,e,"separator"):h(e,u,c)?S(t,e,"operator"):S(t,e,null)},indent:function(e,t,r){var n,i,a,o,s,u,c,l=D(u=t.match(/,|[a-z]+|\}|\]|\)|>>|\|+|\(/))&&0===u.index?u[0]:"",_=W(e,1),d=W(e,2);if(e.in_string||e.in_atom)return null;if(!d)return 0;if("when"==_.token)return _.column+r.unit;if("when"===l&&"function"===d.type)return d.indent+r.unit;if("("===l&&"fun"===_.token)return _.column+3;else if("catch"===l&&(c=U(e,["try"])))return c.column;else if(x(l,["end","after","of"]))return(c=U(e,["begin","case","fun","if","receive","try"]))?c.column:null;else if(x(l,p))return(c=U(e,f))?c.column:null;else if(x(_.token,[",","|","||"])||x(l,[",","|","||"])){return(i=A(n=e.tokenStack.slice(0,-1),"type",["open_paren"]),c=!!D(n[i])&&n[i])?c.column+c.token.length:r.unit}else if("->"==_.token)if(x(d.token,["receive","case","if","try"]))return d.column+r.unit+r.unit;else return d.column+r.unit;else{return x(_.token,f)?_.column+_.token.length:D((o=A(a=e.tokenStack,"type",["open_paren","separator","keyword"]),s=A(a,"type",["operator"]),c=D(o)&&D(s)&&o<s?a[o+1]:!!D(o)&&a[o]))?c.column+r.unit:0}},languageData:{commentTokens:{line:"%"}}}}}]);