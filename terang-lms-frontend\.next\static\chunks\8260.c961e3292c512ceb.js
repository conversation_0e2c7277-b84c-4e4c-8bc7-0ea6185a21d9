try{let O="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new <PERSON><PERSON>rror).stack;r&&(O._sentryDebugIds=O._sentryDebugIds||{},O._sentryDebugIds[r]="800f71b3-caa4-4d88-a00e-3be4a6927a24",O._sentryDebugIdIdentifier="sentry-dbid-800f71b3-caa4-4d88-a00e-3be4a6927a24")}catch(O){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8260],{98260:(O,r,e)=>{e.r(r),e.d(r,{angular:()=>C,angularLanguage:()=>A});var t=e(14563),a=e(32158),n=e(31224),l=e(21769),i=e(38348),o=e(46485);let p=new o.Lu(O=>{let r=O.pos;for(;;){if(10==O.next){O.advance();break}if(123==O.next&&123==O.peek(1)||O.next<0)break;O.advance()}O.pos>r&&O.acceptToken(1)});function u(O,r,e){return new o.Lu(t=>{let a=t.pos;for(;t.next!=O&&t.next>=0&&(e||38!=t.next&&(123!=t.next||123!=t.peek(1)));)t.advance();t.pos>a&&t.acceptToken(r)})}let Q=u(39,33,!1),s=u(34,34,!1),S=u(39,35,!0),g=u(34,36,!0),q=o.U1.deserialize({version:14,states:"(jOVOqOOOeQpOOOvO!bO'#CaOOOP'#Cx'#CxQVOqOOO!OQpO'#CfO!WQpO'#ClO!]QpO'#CrO!bQpO'#CsOOQO'#Cv'#CvQ!gQpOOQ!lQpOOQ!qQpOOOOOV,58{,58{O!vOpO,58{OOOP-E6v-E6vO!{QpO,59QO#TQpO,59QOOQO,59W,59WO#YQpO,59^OOQO,59_,59_O#_QpOOO#_QpOOO#gQpOOOOOV1G.g1G.gO#oQpO'#CyO#tQpO1G.lOOQO1G.l1G.lO#|QpO1G.lOOQO1G.x1G.xO$UO`O'#DUO$ZOWO'#DUOOQO'#Co'#CoQOQpOOOOQO'#Cu'#CuO$`OtO'#CwO$qOrO'#CwOOQO,59e,59eOOQO-E6w-E6wOOQO7+$W7+$WO%SQpO7+$WO%[QpO7+$WOOOO'#Cp'#CpO%aOpO,59pOOOO'#Cq'#CqO%fOpO,59pOOOS'#Cz'#CzO%kOtO,59cOOQO,59c,59cOOOQ'#C{'#C{O%|OrO,59cO&_QpO<<GrOOQO<<Gr<<GrOOQO1G/[1G/[OOOS-E6x-E6xOOQO1G.}1G.}OOOQ-E6y-E6yOOQOAN=^AN=^",stateData:"&d~OvOS~OPROSQOVROWRO~OZTO[XO^VOaUOhWO~OR]OU^O~O[`O^aO~O[bO~O[cO~O[dO~ObeO~ObfO~ObgO~ORhO~O]kOwiO~O[lO~O_mO~OynOzoO~OysOztO~O[uO~O]wOwiO~O_yOwiO~OtzO~Os|O~OSQOV!OOW!OOr!OOy!QO~OSQOV!ROW!ROq!ROz!QO~O_!TOwiO~O]!UO~Oy!VO~Oz!VO~OSQOV!OOW!OOr!OOy!XO~OSQOV!ROW!ROq!ROz!XO~O]!ZO~O",goto:"#dyPPPPPzPPPP!WPPPPP!WPP!Z!^!a!d!dP!g!j!m!p!v#Q#WPPPPPPPP#^SROSS!Os!PT!Rt!SRYPRqeR{nR}oRZPRqfR[PRqgQSOR_SQj`SvjxRxlQ!PsR!W!PQ!StR!Y!SQpeRrf",nodeNames:"⚠ Text Content }} {{ Interpolation InterpolationContent Entity InvalidEntity Attribute BoundAttributeName [ Identifier ] ( ) ReferenceName # Is ExpressionAttributeValue AttributeInterpolation AttributeInterpolation EventName DirectiveName * StatementAttributeValue AttributeName AttributeValue",maxTerm:42,nodeProps:[["openedBy",3,"{{",15,"("],["closedBy",4,"}}",14,")"],["isolate",-4,5,19,25,27,""]],skippedNodes:[0],repeatNodeCount:4,tokenData:"0r~RyOX#rXY$mYZ$mZ]#r]^$m^p#rpq$mqr#rrs%jst&Qtv#rvw&hwx)zxy*byz*xz{+`{}#r}!O+v!O!P-]!P!Q#r!Q![+v![!]+v!]!_#r!_!`-s!`!c#r!c!}+v!}#O.Z#O#P#r#P#Q.q#Q#R#r#R#S+v#S#T#r#T#o+v#o#p/X#p#q#r#q#r0Z#r%W#r%W;'S+v;'S;:j-V;:j;=`$g<%lO+vQ#wTUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rQ$ZSO#q#r#r;'S#r;'S;=`$g<%lO#rQ$jP;=`<%l#rR$t[UQvPOX#rXY$mYZ$mZ]#r]^$m^p#rpq$mq#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR%qTyPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR&XTaPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR&oXUQWPOp'[pq#rq!]'[!]!^#r!^#q'[#q#r(d#r;'S'[;'S;=`)t<%lO'[R'aXUQOp'[pq#rq!]'[!]!^'|!^#q'[#q#r(d#r;'S'[;'S;=`)t<%lO'[R(TTVPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR(gXOp'[pq#rq!]'[!]!^'|!^#q'[#q#r)S#r;'S'[;'S;=`)t<%lO'[P)VUOp)Sq!])S!]!^)i!^;'S)S;'S;=`)n<%lO)SP)nOVPP)qP;=`<%l)SR)wP;=`<%l'[R*RTzPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR*iT^PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+PT_PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+gThPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+}b[PUQO}#r}!O+v!O!Q#r!Q![+v![!]+v!]!c#r!c!}+v!}#R#r#R#S+v#S#T#r#T#o+v#o#q#r#q#r$W#r%W#r%W;'S+v;'S;:j-V;:j;=`$g<%lO+vR-YP;=`<%l+vR-dTwPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR-zTUQbPO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR.bTZPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR.xT]PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR/^VUQO#o#r#o#p/s#p#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR/zTSPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#r~0^TO#q#r#q#r0m#r;'S#r;'S;=`$g<%lO#r~0rOR~",tokenizers:[p,Q,s,S,g,0,1],topRules:{Content:[0,2],Attribute:[1,9]},tokenPrec:0}),P=n.javascriptLanguage.parser.configure({top:"SingleExpression"}),b=q.configure({props:[(0,l.pn)({Text:l._A.content,Is:l._A.definitionOperator,AttributeName:l._A.attributeName,"AttributeValue ExpressionAttributeValue StatementAttributeValue":l._A.attributeValue,Entity:l._A.character,InvalidEntity:l._A.invalid,"BoundAttributeName/Identifier":l._A.attributeName,"EventName/Identifier":l._A.special(l._A.attributeName),"ReferenceName/Identifier":l._A.variableName,"DirectiveName/Identifier":l._A.keyword,"{{ }}":l._A.brace,"( )":l._A.paren,"[ ]":l._A.bracket,"# '*'":l._A.punctuation})]}),$={parser:P},R={parser:n.javascriptLanguage.parser},c=b.configure({wrap:(0,i.$g)((O,r)=>"InterpolationContent"==O.name?$:null)}),d=b.configure({wrap:(0,i.$g)((O,r)=>{var e;return"InterpolationContent"==O.name?$:"AttributeInterpolation"!=O.name?null:(null==(e=O.node.parent)?void 0:e.name)=="StatementAttributeValue"?R:$}),top:"Attribute"}),f={parser:c},v={parser:d},m=(0,a.html)({selfClosingTags:!0});function W(O){return O.configure({wrap:(0,i.$g)(_)},"angular")}let A=W(m.language);function _(O,r){switch(O.name){case"Attribute":return/^[*#(\[]|\{\{/.test(r.read(O.from,O.to))?v:null;case"Text":return f}return null}function C(O={}){let r=m;if(O.base){if("html"!=O.base.language.name||!(O.base.language instanceof t.bj))throw RangeError("The base option must be the result of calling html(...)");r=O.base}return new t.Yy(r.language==m.language?A:W(r.language),[r.support,r.language.data.of({closeBrackets:{brackets:["[","{",'"']},indentOnInput:/^\s*[\}\]]$/})])}}}]);