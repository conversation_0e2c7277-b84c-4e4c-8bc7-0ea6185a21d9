try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="df4dd7fe-128a-48d2-a900-e963c512031b",e._sentryDebugIdIdentifier="sentry-dbid-df4dd7fe-128a-48d2-a900-e963c512031b")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9867],{45074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(95155),n=r(12115),a=r(47886);function l(){return(0,n.useEffect)(()=>{let e=a.qs.getUser();if(e){let t=(0,a.WG)(e);window.location.href=t}else window.location.href="/auth/sign-in"},[]),(0,s.jsx)("div",{className:"flex min-h-screen items-center justify-center","data-sentry-component":"Dashboard","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold",children:"Redirecting..."}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Please wait while we redirect you to your dashboard."})]})})}},47886:(e,t,r)=>{"use strict";r.d(t,{WG:()=>n,cl:()=>l,qs:()=>s});let s={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==s.getUser(),hasRole:e=>{let t=s.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>s.hasRole("super_admin"),isTeacher:()=>s.hasRole("teacher"),isStudent:()=>s.hasRole("student")},n=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},a=()=>{let e=s.getUser();return e||(window.location.href="/auth/sign-in",null)},l=e=>{let t=a();return t?t.role!==e?(window.location.href=n(t),null):t:null}},50848:(e,t,r)=>{Promise.resolve().then(r.bind(r,45074))}},e=>{var t=t=>e(e.s=t);e.O(0,[4850,8441,3840,7358],()=>t(50848)),_N_E=e.O()}]);