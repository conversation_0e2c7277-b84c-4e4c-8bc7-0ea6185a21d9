try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ff0e0081-7d4e-49bd-93b1-05c802d825d3",e._sentryDebugIdIdentifier="sentry-dbid-ff0e0081-7d4e-49bd-93b1-05c802d825d3")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4610],{16485:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(71847).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},20063:(e,t,r)=>{"use strict";var s=r(47260);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},20764:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var s=r(95155);r(12115);var a=r(32467),n=r(83101),i=r(64269);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:t})),...c,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},32467:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>i});var s=r(12115),a=r(94446),n=r(95155);function i(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var i;let e,l,d=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(c.ref=t?(0,a.t)(t,d):d),s.cloneElement(r,c)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,l=s.Children.toArray(a),d=l.find(o);if(d){let e=d.props.children,a=l.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),d=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=d,t}function o(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}},42529:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(71847).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},52472:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(71847).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},57828:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(71847).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},61289:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(12115),a=r(47886),n=r(20063);function i(){let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0),l=(0,n.useRouter)();return(0,s.useEffect)(()=>{let e=a.qs.getUser();e&&t(e),i(!1)},[]),{user:e,loading:r,signOut:()=>{a.qs.removeUser(),t(null),l.push("/auth/sign-in")}}}},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n,z:()=>i});var s=r(2821),a=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}function i(e){var t,r;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:n="normal"}=s;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(a)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][i])?r:"Bytes")}},66094:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>o});var s=r(95155);r(12115);var a=r(64269);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},66325:(e,t,r)=>{Promise.resolve().then(r.bind(r,79856))},71847:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:o,iconNode:u,...m}=e;return(0,s.createElement)("svg",{ref:t,...i,width:a,height:a,stroke:r,strokeWidth:d?24*Number(l)/Number(a):l,className:n("lucide",c),...m},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(o)?o:[o]])}),d=(e,t)=>{let r=(0,s.forwardRef)((r,i)=>{let{className:d,...c}=r;return(0,s.createElement)(l,{ref:i,iconNode:t,className:n("lucide-".concat(a(e)),d),...c})});return r.displayName="".concat(e),r}},79856:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(95155),a=r(66094),n=r(20764),i=r(88021),l=r(52472),d=r(42529),c=r(39867),o=r(57828);let u=(0,r(71847).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var m=r(16485),x=r(99223),f=r(61289);function h(){let{user:e}=(0,f.A)(),t=[{id:1,courseName:"Chemistry Basics",courseCode:"CHEM101",certificateId:"CERT-2024-001",completedAt:"2024-07-30",finalScore:95,instructor:"Prof. Johnson",status:"issued",downloadUrl:"#"}],r=[{id:2,courseName:"Introduction to Algebra",courseCode:"MATH101",progress:85,estimatedCompletion:"2024-08-15",status:"in_progress"}],h=r=>{let s=t.find(e=>e.certificateId===r);if(s&&e){var a;return{studentName:null!=(a=e.name)?a:"Student",institutionName:"Terang University",courseName:s.courseName,courseCode:s.courseCode,completionDate:new Date(s.completedAt).toLocaleDateString(),finalScore:s.finalScore,instructorName:s.instructor,certificateId:s.certificateId}}},p=e=>{let t=h(e);t&&(0,x.Ct)(t)},g=e=>{let t=h(e);t&&(0,x.hr)(t)},y=e=>{let t=h(e);t&&(0,x.a7)(t)};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentCertificatesPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Certificates"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"View and download your course completion certificates"})]})}),(0,s.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(a.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(a.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(l.A,{className:"h-5 w-5","data-sentry-element":"Award","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("span",{children:"Earned Certificates"})]}),(0,s.jsx)(a.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Certificates you have earned by completing courses"})]}),(0,s.jsx)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:t.length>0?(0,s.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,s.jsx)("div",{className:"rounded-lg border p-6",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-yellow-400 to-yellow-600",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:e.courseName}),(0,s.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Course Code: ",e.courseCode]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Certificate ID"}),(0,s.jsx)("p",{className:"font-mono",children:e.certificateId})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Completed"}),(0,s.jsx)("p",{children:new Date(e.completedAt).toLocaleDateString()})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Final Score"}),(0,s.jsxs)("p",{className:"font-semibold",children:[e.finalScore,"%"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Instructor"}),(0,s.jsx)("p",{children:e.instructor})]})]}),(0,s.jsxs)(i.E,{variant:"default",className:"w-fit",children:[(0,s.jsx)(d.A,{className:"mr-1 h-3 w-3"}),"Verified Certificate"]})]}),(0,s.jsxs)("div",{className:"ml-4 flex flex-col space-y-2",children:[(0,s.jsxs)(n.$,{size:"sm",onClick:()=>p(e.certificateId),children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Download"]}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>y(e.certificateId),children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Preview"]}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>g(e.certificateId),children:[(0,s.jsx)(u,{className:"mr-2 h-4 w-4"}),"Share"]})]})]})},e.id))}):(0,s.jsxs)("div",{className:"py-8 text-center",children:[(0,s.jsx)(l.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No certificates yet"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:"Complete a course to earn your first certificate."})]})})]}),(0,s.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(a.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(a.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(m.A,{className:"h-5 w-5","data-sentry-element":"Calendar","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("span",{children:"Certificates in Progress"})]}),(0,s.jsx)(a.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Courses you're working on that will earn certificates"})]}),(0,s.jsx)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:r.length>0?(0,s.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,s.jsx)("div",{className:"rounded-lg border p-6",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-gray-400 to-gray-600",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:e.courseName}),(0,s.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Course Code: ",e.courseCode]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[e.progress,"%"]})]}),(0,s.jsx)("div",{className:"bg-muted h-2 w-full rounded-full",children:(0,s.jsx)("div",{className:"bg-primary h-2 rounded-full",style:{width:"".concat(e.progress,"%")}})}),(0,s.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Estimated completion:"," ",new Date(e.estimatedCompletion).toLocaleDateString()]})]}),(0,s.jsx)(i.E,{variant:"outline",className:"w-fit",children:"In Progress"})]})})},e.id))}):(0,s.jsxs)("div",{className:"py-8 text-center",children:[(0,s.jsx)(m.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,s.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No courses in progress"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:"Enroll in a course to start working towards a certificate."})]})})]}),(0,s.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(a.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(a.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"About Certificates"})}),(0,s.jsxs)(a.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"mb-2 font-semibold",children:"Self-paced Courses"}),(0,s.jsxs)("ul",{className:"text-muted-foreground space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"• Certificates are automatically generated upon completion"}),(0,s.jsx)("li",{children:"• Must achieve minimum score on all quizzes"}),(0,s.jsx)("li",{children:"• Complete all modules and chapters"}),(0,s.jsx)("li",{children:"• Available for immediate download"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"mb-2 font-semibold",children:"Verified Courses"}),(0,s.jsxs)("ul",{className:"text-muted-foreground space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"• Requires manual verification by instructor"}),(0,s.jsx)("li",{children:"• May include additional assessments"}),(0,s.jsx)("li",{children:"• Higher credibility and recognition"}),(0,s.jsx)("li",{children:"• Processing time: 1-3 business days"})]})]})]}),(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsx)("h4",{className:"mb-2 font-semibold",children:"Certificate Features"}),(0,s.jsxs)("ul",{className:"text-muted-foreground space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"• Unique certificate ID for verification"}),(0,s.jsx)("li",{children:"• Digital signature and timestamp"}),(0,s.jsx)("li",{children:"• Shareable on professional networks"}),(0,s.jsx)("li",{children:"• PDF format for easy printing"}),(0,s.jsx)("li",{children:"• Permanent record in your profile"})]})]})]})]})]})}},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(2821);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,d=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let n=a(t)||a(s);return i[e][n]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,d,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},88021:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(95155);r(12115);var a=r(32467),n=r(83101),i=r(64269);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,asChild:n=!1,...d}=e,c=n?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...d,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},94446:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>n});var s=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return s.useCallback(n(...e),e)}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,2562,4850,8441,3840,7358],()=>t(66325)),_N_E=e.O()}]);