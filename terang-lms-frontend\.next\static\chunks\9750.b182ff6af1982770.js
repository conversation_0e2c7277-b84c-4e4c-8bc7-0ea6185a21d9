try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="6d87e39c-3474-4548-a22b-f3a124570cc5",e._sentryDebugIdIdentifier="sentry-dbid-6d87e39c-3474-4548-a22b-f3a124570cc5")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9750],{39750:(e,n,t)=>{t.r(n),t.d(n,{dockerFile:()=>k});var r=t(52582),a="from",s=RegExp("^(\\s*)\\b("+a+")\\b","i"),o=["run","cmd","entrypoint","shell"],l=RegExp("^(\\s*)("+o.join("|")+")(\\s+\\[)","i"),i="expose",g=RegExp("^(\\s*)("+i+")(\\s+)","i"),u="("+[a,i].concat(o).concat(["arg","from","maintainer","label","env","add","copy","volume","user","workdir","onbuild","stopsignal","healthcheck","shell"]).join("|")+")",d=RegExp("^(\\s*)"+u+"(\\s*)(#.*)?$","i"),x=RegExp("^(\\s*)"+u+"(\\s+)","i");let k=(0,r.I)({start:[{regex:/^\s*#.*$/,sol:!0,token:"comment"},{regex:s,token:[null,"keyword"],sol:!0,next:"from"},{regex:d,token:[null,"keyword",null,"error"],sol:!0},{regex:l,token:[null,"keyword",null],sol:!0,next:"array"},{regex:g,token:[null,"keyword",null],sol:!0,next:"expose"},{regex:x,token:[null,"keyword",null],sol:!0,next:"arguments"},{regex:/./,token:null}],from:[{regex:/\s*$/,token:null,next:"start"},{regex:/(\s*)(#.*)$/,token:[null,"error"],next:"start"},{regex:/(\s*\S+\s+)(as)/i,token:[null,"keyword"],next:"start"},{token:null,next:"start"}],single:[{regex:/(?:[^\\']|\\.)/,token:"string"},{regex:/'/,token:"string",pop:!0}],double:[{regex:/(?:[^\\"]|\\.)/,token:"string"},{regex:/"/,token:"string",pop:!0}],array:[{regex:/\]/,token:null,next:"start"},{regex:/"(?:[^\\"]|\\.)*"?/,token:"string"}],expose:[{regex:/\d+$/,token:"number",next:"start"},{regex:/[^\d]+$/,token:null,next:"start"},{regex:/\d+/,token:"number"},{regex:/[^\d]+/,token:null},{token:null,next:"start"}],arguments:[{regex:/^\s*#.*$/,sol:!0,token:"comment"},{regex:/"(?:[^\\"]|\\.)*"?$/,token:"string",next:"start"},{regex:/"/,token:"string",push:"double"},{regex:/'(?:[^\\']|\\.)*'?$/,token:"string",next:"start"},{regex:/'/,token:"string",push:"single"},{regex:/[^#"']+[\\`]$/,token:null},{regex:/[^#"']+$/,token:null,next:"start"},{regex:/[^#"']+/,token:null},{token:null,next:"start"}],languageData:{commentTokens:{line:"#"}}})},52582:(e,n,t)=>{function r(e){a(e,"start");var n,t,r,o={},l=e.languageData||{},i=!1;for(var g in e)if(g!=l&&e.hasOwnProperty(g))for(var u=o[g]=[],d=e[g],x=0;x<d.length;x++){var k=d[x];u.push(new s(k,e)),(k.indent||k.dedent)&&(i=!0)}return{name:l.name,startState:function(){return{state:"start",pending:null,indent:i?[]:null}},copyState:function(e){var n={state:e.state,pending:e.pending,indent:e.indent&&e.indent.slice(0)};return e.stack&&(n.stack=e.stack.slice(0)),n},token:(n=o,function(e,t){if(t.pending){var r=t.pending.shift();return 0==t.pending.length&&(t.pending=null),e.pos+=r.text.length,r.token}for(var a=n[t.state],s=0;s<a.length;s++){var o=a[s],l=(!o.data.sol||e.sol())&&e.match(o.regex);if(l){o.data.next?t.state=o.data.next:o.data.push?((t.stack||(t.stack=[])).push(t.state),t.state=o.data.push):o.data.pop&&t.stack&&t.stack.length&&(t.state=t.stack.pop()),o.data.indent&&t.indent.push(e.indentation()+e.indentUnit),o.data.dedent&&t.indent.pop();var i=o.token;if(i&&i.apply&&(i=i(l)),l.length>2&&o.token&&"string"!=typeof o.token){t.pending=[];for(var g=2;g<l.length;g++)l[g]&&t.pending.push({text:l[g],token:o.token[g-1]});return e.backUp(l[0].length-(l[1]?l[1].length:0)),i[0]}if(i&&i.join)return i[0];return i}}return e.next(),null}),indent:(t=o,r=l,function(e,n){if(null==e.indent||r.dontIndentStates&&r.dontIndentStates.indexOf(e.state)>-1)return null;var a=e.indent.length-1,s=t[e.state];e:for(;;){for(var o=0;o<s.length;o++){var l=s[o];if(l.data.dedent&&!1!==l.data.dedentIfLineStart){var i=l.regex.exec(n);if(i&&i[0]){a--,(l.next||l.push)&&(s=t[l.next||l.push]),n=n.slice(i[0].length);continue e}}}break}return a<0?0:e.indent[a]}),mergeTokens:l.mergeTokens,languageData:l}}function a(e,n){if(!e.hasOwnProperty(n))throw Error("Undefined state "+n+" in simple mode")}function s(e,n){(e.next||e.push)&&a(n,e.next||e.push),this.regex=function(e,n){if(!e)return/(?:)/;var t="";return e instanceof RegExp?(e.ignoreCase&&(t="i"),e=e.source):e=String(e),RegExp("^(?:"+e+")",t)}(e.regex),this.token=function(e){if(!e)return null;if(e.apply)return e;if("string"==typeof e)return e.replace(/\./g," ");for(var n=[],t=0;t<e.length;t++)n.push(e[t]&&e[t].replace(/\./g," "));return n}(e.token),this.data=e}t.d(n,{I:()=>r})}}]);