try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="74c17834-2df1-4234-9c81-b64e9159a36f",e._sentryDebugIdIdentifier="sentry-dbid-74c17834-2df1-4234-9c81-b64e9159a36f")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7520],{47520:(e,t,n)=>{n.d(t,{Kq:()=>Y,UC:()=>K,ZL:()=>G,bL:()=>Z,i3:()=>W,l9:()=>z});var r=n(12115),o=n(92556),l=n(94446),i=n(3468),a=n(44831),s=n(68946),u=n(66093),c=n(75433),d=n(76842),p=n(97602),f=n(32467),h=n(23558),x=n(861),g=n(95155),[y,v]=(0,i.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),w="TooltipProvider",m="tooltip.open",[C,T]=y(w),E=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=r.useRef(!0),s=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,g.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};E.displayName=w;var k="Tooltip",[L,R]=y(k),j=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,d=T(k,e.__scopeTooltip),p=b(t),[f,x]=r.useState(null),y=(0,s.B)(),v=r.useRef(0),w=null!=a?a:d.disableHoverableContent,C=null!=c?c:d.delayDuration,E=r.useRef(!1),[R,j]=(0,h.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(m))):d.onClose(),null==i||i(e)},caller:k}),_=r.useMemo(()=>R?E.current?"delayed-open":"instant-open":"closed",[R]),D=r.useCallback(()=>{window.clearTimeout(v.current),v.current=0,E.current=!1,j(!0)},[j]),I=r.useCallback(()=>{window.clearTimeout(v.current),v.current=0,j(!1)},[j]),P=r.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{E.current=!0,j(!0),v.current=0},C)},[C,j]);return r.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,g.jsx)(u.bL,{...p,children:(0,g.jsx)(L,{scope:t,contentId:y,open:R,stateAttribute:_,trigger:f,onTriggerChange:x,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?P():D()},[d.isOpenDelayedRef,P,D]),onTriggerLeave:r.useCallback(()=>{w?I():(window.clearTimeout(v.current),v.current=0)},[I,w]),onOpen:D,onClose:I,disableHoverableContent:w,children:n})})};j.displayName=k;var _="TooltipTrigger",D=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...i}=e,a=R(_,n),s=T(_,n),c=b(n),d=r.useRef(null),f=(0,l.s)(t,d,a.onTriggerChange),h=r.useRef(!1),x=r.useRef(!1),y=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,g.jsx)(u.Mz,{asChild:!0,...c,children:(0,g.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(x.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),x.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),x.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});D.displayName=_;var I="TooltipPortal",[P,M]=y(I,{forceMount:void 0}),N=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,l=R(I,t);return(0,g.jsx)(P,{scope:t,forceMount:n,children:(0,g.jsx)(d.C,{present:n||l.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};N.displayName=I;var O="TooltipContent",B=r.forwardRef((e,t)=>{let n=M(O,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...l}=e,i=R(O,e.__scopeTooltip);return(0,g.jsx)(d.C,{present:r||i.open,children:i.disableHoverableContent?(0,g.jsx)(S,{side:o,...l,ref:t}):(0,g.jsx)(A,{side:o,...l,ref:t})})}),A=r.forwardRef((e,t)=>{let n=R(O,e.__scopeTooltip),o=T(O,e.__scopeTooltip),i=r.useRef(null),a=(0,l.s)(t,i),[s,u]=r.useState(null),{trigger:c,onClose:d}=n,p=i.current,{onPointerInTransitChange:f}=o,h=r.useCallback(()=>{u(null),f(!1)},[f]),x=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(n,r,o,l)){case l:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),f(!0)},[f]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&p){let e=e=>x(e,p),t=e=>x(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,x,h]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-s)*(r-u)/(d-u)+s&&(o=!o)}return o}(n,s);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,h]),(0,g.jsx)(S,{...e,ref:a})}),[F,H]=y(k,{isInside:!1}),q=(0,f.Dc)("TooltipContent"),S=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=R(O,n),p=b(n),{onClose:f}=d;return r.useEffect(()=>(document.addEventListener(m,f),()=>document.removeEventListener(m,f)),[f]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,g.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,g.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,g.jsx)(q,{children:o}),(0,g.jsx)(F,{scope:n,isInside:!0,children:(0,g.jsx)(x.bL,{id:d.contentId,role:"tooltip",children:l||o})})]})})});B.displayName=O;var U="TooltipArrow",X=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=b(n);return H(U,n).isInside?null:(0,g.jsx)(u.i3,{...o,...r,ref:t})});X.displayName=U;var Y=E,Z=j,z=D,G=N,K=B,W=X}}]);