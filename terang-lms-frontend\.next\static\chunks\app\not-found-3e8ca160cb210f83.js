try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f71b55db-9c68-427e-b982-a789ab0e5bc5",e._sentryDebugIdIdentifier="sentry-dbid-f71b55db-9c68-427e-b982-a789ab0e5bc5")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4345],{20063:(e,t,r)=>{"use strict";var n=r(47260);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},20764:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>o});var n=r(95155);r(12115);var a=r(32467),i=r(83101),s=r(64269);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...u}=e,d=l?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,s.cn)(o({variant:r,size:i,className:t})),...u,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},32467:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,Dc:()=>u,TL:()=>s});var n=r(12115),a=r(94446),i=r(95155);function s(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var s;let e,o,l=(s=r,(o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(o=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(u.ref=t?(0,a.t)(t,l):l),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...s}=e,o=n.Children.toArray(a),l=o.find(d);if(l){let e=l.props.children,a=o.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...s,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...s,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var o=s("Slot"),l=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},46380:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(95155),a=r(20063),i=r(20764);function s(){let e=(0,a.useRouter)();return(0,n.jsxs)("div",{className:"absolute top-1/2 left-1/2 mb-16 -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center","data-sentry-component":"NotFound","data-sentry-source-file":"not-found.tsx",children:[(0,n.jsx)("span",{className:"from-foreground bg-linear-to-b to-transparent bg-clip-text text-[10rem] leading-none font-extrabold text-transparent",children:"404"}),(0,n.jsx)("h2",{className:"font-heading my-2 text-2xl font-bold",children:"Something's missing"}),(0,n.jsx)("p",{children:"Sorry, the page you are looking for doesn't exist or has been moved."}),(0,n.jsxs)("div",{className:"mt-8 flex justify-center gap-2",children:[(0,n.jsx)(i.$,{onClick:()=>e.back(),variant:"default",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"not-found.tsx",children:"Go back"}),(0,n.jsx)(i.$,{onClick:()=>e.push("/dashboard"),variant:"ghost",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"not-found.tsx",children:"Back to Home"})]})]})}},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i,z:()=>s});var n=r(2821),a=r(75889);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function s(e){var t,r;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:i="normal"}=n;if(0===e)return"0 Byte";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,s)).toFixed(a)," ").concat("accurate"===i?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][s])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][s])?r:"Bytes")}},82105:(e,t,r)=>{Promise.resolve().then(r.bind(r,46380))},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(2821);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,s=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:o}=t,l=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==o?void 0:o[e];if(null===t)return null;let i=a(t)||a(n);return s[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,l,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...u}[t]):({...o,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},94446:(e,t,r)=>{"use strict";r.d(t,{s:()=>s,t:()=>i});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function s(...e){return n.useCallback(i(...e),e)}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,4850,8441,3840,7358],()=>t(82105)),_N_E=e.O()}]);