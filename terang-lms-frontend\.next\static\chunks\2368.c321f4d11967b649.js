try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f3c11319-941d-4401-9272-6fc126fee3cd",e._sentryDebugIdIdentifier="sentry-dbid-f3c11319-941d-4401-9272-6fc126fee3cd")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2368],{32368:(e,t,n)=>{function r(e){return{type:e,style:"keyword"}}n.r(t),n.d(t,{haxe:()=>et,hxml:()=>en});var i,a,o=r("keyword a"),u=r("keyword b"),l=r("keyword c"),c=r("operator"),f={type:"atom",style:"atom"},s={type:"attribute",style:"attribute"},i=r("typedef"),d={if:o,while:o,else:u,do:u,try:u,return:l,break:l,continue:l,new:l,throw:l,var:r("var"),inline:s,static:s,using:r("import"),public:s,private:s,cast:r("cast"),import:r("import"),macro:r("macro"),function:r("function"),catch:r("catch"),untyped:r("untyped"),callback:r("cb"),for:r("for"),switch:r("switch"),case:r("case"),default:r("default"),in:c,never:r("property_access"),trace:r("trace"),class:i,abstract:i,enum:i,interface:i,typedef:i,extends:i,implements:i,dynamic:i,true:f,false:f,null:f},p=/[+\-*&%=<>!?|]/;function m(e,t,n){return t.tokenize=n,n(e,t)}function v(e,t){for(var n,r=!1;null!=(n=e.next());){if(n==t&&!r)return!0;r=!r&&"\\"==n}}function y(e,t){var n,r=e.next();if('"'==r||"'"==r){return m(e,t,(o=r,function(e,t){return v(e,o)&&(t.tokenize=y),i="string",a=void 0,"string"}))}if(/[\[\]{}\(\),;\:\.]/.test(r))return i=r,void(a=void 0);if("0"==r&&e.eat(/x/i))return e.eatWhile(/[\da-f]/i),i="number",a=void 0,"number";if(/\d/.test(r)||"-"==r&&e.eat(/\d/))return e.match(/^\d*(?:\.\d*(?!\.))?(?:[eE][+\-]?\d+)?/),i="number",a=void 0,"number";if(t.reAllowed&&"~"==r&&e.eat(/\//))return v(e,"/"),e.eatWhile(/[gimsu]/),i="regexp",a=void 0,"string.special";else if("/"==r)if(e.eat("*"))return m(e,t,b);else if(e.eat("/"))return e.skipToEnd(),i="comment",a=void 0,"comment";else return e.eatWhile(p),u=e.current(),i="operator",a=u,null;else{if("#"==r)return e.skipToEnd(),i="conditional",a=void 0,"meta";if("@"==r)return e.eat(/:/),e.eatWhile(/[\w_]/),i="metadata",a=void 0,"meta";if(p.test(r))return e.eatWhile(p),l=e.current(),i="operator",a=l,null;if(/[A-Z]/.test(r))return e.eatWhile(/[\w_<>]/),c=n=e.current(),i="type",a=c,"type";e.eatWhile(/[\w_]/);var o,u,l,c,f,s,h,n=e.current(),k=d.propertyIsEnumerable(n)&&d[n];return k&&t.kwAllowed?(f=k.type,s=k.style,h=n,i=f,a=h,s):(i="variable",a=n,"variable")}}function b(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=y;break}r="*"==n}return i="comment",a=void 0,"comment"}var h={atom:!0,number:!0,variable:!0,string:!0,regexp:!0};function k(e,t,n,r,i,a){this.indented=e,this.column=t,this.type=n,this.prev=i,this.info=a,null!=r&&(this.align=r)}function x(e){for(var t=w.state,n=t.importedtypes;n;n=n.next)if(n.name==e)return;t.importedtypes={name:e,next:t.importedtypes}}var w={state:null,column:null,marked:null,cc:null};function g(){for(var e=arguments.length-1;e>=0;e--)w.cc.push(arguments[e])}function A(){return g.apply(null,arguments),!0}function V(e,t){for(var n=t;n;n=n.next)if(n.name==e)return!0;return!1}function _(e){var t=w.state;if(t.context){if(w.marked="def",V(e,t.localVars))return;t.localVars={name:e,next:t.localVars}}else if(t.globalVars){if(V(e,t.globalVars))return;t.globalVars={name:e,next:t.globalVars}}}var S={name:"this",next:null};function D(){w.state.context||(w.state.localVars=S),w.state.context={prev:w.state.context,vars:w.state.localVars}}function E(){w.state.localVars=w.state.context.vars,w.state.context=w.state.context.prev}function T(e,t){var n=function(){var n=w.state;n.lexical=new k(n.indented,w.stream.column(),e,null,n.lexical,t)};return n.lex=!0,n}function I(){var e=w.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function W(e){return function t(n){return n==e?A():";"==e?g():A(t)}}function z(e){return"@"==e?A(C):"var"==e?A(T("vardef"),K,W(";"),I):"keyword a"==e?A(T("form"),O,z,I):"keyword b"==e?A(T("form"),z,I):"{"==e?A(T("}"),D,J,I,E):";"==e?A():"attribute"==e?A(P):"function"==e?A(R):"for"==e?A(T("form"),W("("),T(")"),M,W(")"),I,z,I):"variable"==e?A(T("stat"),j):"switch"==e?A(T("form"),O,T("}","switch"),W("{"),J,I,I):"case"==e?A(O,W(":")):"default"==e?A(W(":")):"catch"==e?A(T("form"),D,W("("),ee,W(")"),z,I,E):"import"==e?A(B,W(";")):"typedef"==e?A(F):g(T("stat"),O,W(";"),I)}function O(e){return h.hasOwnProperty(e)||"type"==e?A(N):"function"==e?A(R):"keyword c"==e?A(Z):"("==e?A(T(")"),Z,W(")"),I,N):"operator"==e?A(O):"["==e?A(T("]"),H(Z,"]"),I,N):"{"==e?A(T("}"),H(G,"}"),I,N):A()}function Z(e){return e.match(/[;\}\)\],]/)?g():g(O)}function N(e,t){if("operator"==e&&/\+\+|--/.test(t))return A(N);if("operator"==e||":"==e)return A(O);if(";"!=e){if("("==e)return A(T(")"),H(O,")"),I,N);if("."==e)return A(q,N);if("["==e)return A(T("]"),O,W("]"),I,N)}}function P(e){return"attribute"==e?A(P):"function"==e?A(R):"var"==e?A(K):void 0}function C(e){return":"==e||"variable"==e?A(C):"("==e?A(T(")"),H($,")"),I,z):void 0}function $(e){if("variable"==e)return A()}function B(e,t){return"variable"==e&&/[A-Z]/.test(t.charAt(0))?(x(t),A()):"variable"==e||"property"==e||"."==e||"*"==t?A(B):void 0}function F(e,t){return"variable"==e&&/[A-Z]/.test(t.charAt(0))?(x(t),A()):"type"==e&&/[A-Z]/.test(t.charAt(0))?A():void 0}function j(e){return":"==e?A(I,z):g(N,W(";"),I)}function q(e){if("variable"==e)return w.marked="property",A()}function G(e){if("variable"==e&&(w.marked="property"),h.hasOwnProperty(e))return A(W(":"),O)}function H(e,t){function n(r){return","==r?A(e,n):r==t?A():A(W(t))}return function(r){return r==t?A():g(e,n)}}function J(e){return"}"==e?A():g(z,J)}function K(e,t){return"variable"==e?(_(t),A(U,L)):A()}function L(e,t){return"="==t?A(O,L):","==e?A(K):void 0}function M(e,t){return"variable"==e?(_(t),A(Q,O)):g()}function Q(e,t){if("in"==t)return A()}function R(e,t){return"variable"==e||"type"==e?(_(t),A(R)):"new"==t?A(R):"("==e?A(T(")"),D,H(ee,")"),I,U,z,E):void 0}function U(e){if(":"==e)return A(X)}function X(e){return"type"==e||"variable"==e?A():"{"==e?A(T("}"),H(Y,"}"),I):void 0}function Y(e){if("variable"==e)return A(U)}function ee(e,t){if("variable"==e)return _(t),A(U)}E.lex=!0,I.lex=!0;let et={name:"haxe",startState:function(e){return{tokenize:y,reAllowed:!0,kwAllowed:!0,cc:[],lexical:new k(-e,0,"block",!1),importedtypes:["Int","Float","String","Void","Std","Bool","Dynamic","Array"],context:null,indented:0}},token:function(e,t){if(e.sol()&&(t.lexical.hasOwnProperty("align")||(t.lexical.align=!1),t.indented=e.indentation()),e.eatSpace())return null;var n=t.tokenize(e,t);if("comment"==i)return n;t.reAllowed=!!("operator"==i||"keyword c"==i||i.match(/^[\[{}\(,;:]$/)),t.kwAllowed="."!=i;var r=i,o=a,u=t.cc;for(w.state=t,w.stream=e,w.marked=null,w.cc=u,t.lexical.hasOwnProperty("align")||(t.lexical.align=!0);;)if((u.length?u.pop():z)(r,o)){for(;u.length&&u[u.length-1].lex;)u.pop()();if(w.marked)return w.marked;if("variable"==r&&function(e,t){for(var n=e.localVars;n;n=n.next)if(n.name==t)return!0}(t,o))return"variableName.local";if("variable"==r&&function(e,t){if(/[a-z]/.test(t.charAt(0)))return!1;for(var n=e.importedtypes.length,r=0;r<n;r++)if(e.importedtypes[r]==t)return!0}(t,o))return"variableName.special";return n}},indent:function(e,t,n){if(e.tokenize!=y)return 0;var r=t&&t.charAt(0),i=e.lexical;"stat"==i.type&&"}"==r&&(i=i.prev);var a=i.type,o=r==a;if("vardef"==a)return i.indented+4;if("form"==a&&"{"==r)return i.indented;if("stat"==a||"form"==a)return i.indented+n.unit;if("switch"==i.info&&!o)return i.indented+(/^(?:case|default)\b/.test(t)?n.unit:2*n.unit);if(i.align)return i.column+ +!o;else return i.indented+(o?0:n.unit)},languageData:{indentOnInput:/^\s*[{}]$/,commentTokens:{line:"//",block:{open:"/*",close:"*/"}}}},en={name:"hxml",startState:function(){return{define:!1,inString:!1}},token:function(e,t){var n=e.peek(),r=e.sol();if("#"==n)return e.skipToEnd(),"comment";if(r&&"-"==n){var i="variable-2";return e.eat(/-/),"-"==e.peek()&&(e.eat(/-/),i="keyword a"),"D"==e.peek()&&(e.eat(/[D]/),i="keyword c",t.define=!0),e.eatWhile(/[A-Z]/i),i}var n=e.peek();return(!1==t.inString&&"'"==n&&(t.inString=!0,e.next()),!0==t.inString)?(e.skipTo("'")||e.skipToEnd(),"'"==e.peek()&&(e.next(),t.inString=!1),"string"):(e.next(),null)},languageData:{commentTokens:{line:"#"}}}}}]);