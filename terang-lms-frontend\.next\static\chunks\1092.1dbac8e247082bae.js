try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="605a9ce6-bb61-43a0-9c41-d0c75c2a4d2e",e._sentryDebugIdIdentifier="sentry-dbid-605a9ce6-bb61-43a0-9c41-d0c75c2a4d2e")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1092],{91092:(e,t,n)=>{function a(e){return RegExp("^(("+e.join(")|(")+"))\\b","i")}n.r(t),n.d(t,{mumps:()=>l});var r=RegExp("^[\\+\\-\\*/&#!_?\\\\<>=\\'\\[\\]]"),o=RegExp("^(('=)|(<=)|(>=)|('>)|('<)|([[)|(]])|(^$))"),$=RegExp("^[\\.,:]"),c=/[()]/,d=RegExp("^[%A-Za-z][A-Za-z0-9]*"),i=a(["\\$ascii","\\$char","\\$data","\\$ecode","\\$estack","\\$etrap","\\$extract","\\$find","\\$fnumber","\\$get","\\$horolog","\\$io","\\$increment","\\$job","\\$justify","\\$length","\\$name","\\$next","\\$order","\\$piece","\\$qlength","\\$qsubscript","\\$query","\\$quit","\\$random","\\$reverse","\\$select","\\$stack","\\$test","\\$text","\\$translate","\\$view","\\$x","\\$y","\\$a","\\$c","\\$d","\\$e","\\$ec","\\$es","\\$et","\\$f","\\$fn","\\$g","\\$h","\\$i","\\$j","\\$l","\\$n","\\$na","\\$o","\\$p","\\$q","\\$ql","\\$qs","\\$r","\\$re","\\$s","\\$st","\\$t","\\$tr","\\$v","\\$z"]),s=a(["break","close","do","else","for","goto","halt","hang","if","job","kill","lock","merge","new","open","quit","read","set","tcommit","trollback","tstart","use","view","write","xecute","b","c","d","e","f","g","h","i","j","k","l","m","n","o","q","r","s","tc","tro","ts","u","v","w","x"]);let l={name:"mumps",startState:function(){return{label:!1,commandMode:0}},token:function(e,t){var n=function(e,t){e.sol()&&(t.label=!0,t.commandMode=0);var n=e.peek();if(" "==n||"	"==n?(t.label=!1,0==t.commandMode?t.commandMode=1:(t.commandMode<0||2==t.commandMode)&&(t.commandMode=0)):"."!=n&&t.commandMode>0&&(":"==n?t.commandMode=-1:t.commandMode=2),("("===n||"	"===n)&&(t.label=!1),";"===n)return e.skipToEnd(),"comment";if(e.match(/^[-+]?\d+(\.\d+)?([eE][-+]?\d+)?/))return"number";if('"'==n)if(e.skipTo('"'))return e.next(),"string";else return e.skipToEnd(),"error";return e.match(o)||e.match(r)?"operator":e.match($)?null:c.test(n)?(e.next(),"bracket"):t.commandMode>0&&e.match(s)?"controlKeyword":e.match(i)?"builtin":e.match(d)?"variable":"$"===n||"^"===n?(e.next(),"builtin"):"@"===n?(e.next(),"string.special"):/[\w%]/.test(n)?(e.eatWhile(/[\w%]/),"variable"):(e.next(),"error")}(e,t);return t.label?"tag":n}}}}]);