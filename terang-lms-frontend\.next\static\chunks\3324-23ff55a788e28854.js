try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="e6ca4303-c7a3-4142-8e2a-9a7b9b6016f2",e._sentryDebugIdIdentifier="sentry-dbid-e6ca4303-c7a3-4142-8e2a-9a7b9b6016f2")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3324],{9005:(e,t,r)=>{r.d(t,{EnrollmentProvider:()=>d,q:()=>i});var a=r(95155),n=r(12115),o=r(55542);let s=(0,n.createContext)(void 0),i=()=>{let e=(0,n.useContext)(s);if(!e)throw Error("useEnrollment must be used within an EnrollmentProvider");return e},d=e=>{let{children:t}=e,[r,i]=(0,n.useState)(!1),[d,l]=(0,n.useState)(o.n4),[c,u]=(0,n.useState)([]),m="lms-enrollment-data",b="lms-multiple-enrollment-data";(0,n.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(b);if(e){let t=JSON.parse(e);Date.now()<t.expirationTime?(u(t.enrolledCourses),i(t.enrolledCourses.length>0),t.enrolledCourses.length>0&&l(t.enrolledCourses[0])):localStorage.removeItem(b);return}let t=localStorage.getItem(m);if(t){let e=JSON.parse(t);if(Date.now()<e.expirationTime){i(e.isEnrolled),l(e.courseData),u([e.courseData]);let t={enrolledCourses:[e.courseData],enrollmentTimestamp:e.enrollmentTimestamp,expirationTime:e.expirationTime};localStorage.setItem(b,JSON.stringify(t)),localStorage.removeItem(m)}else localStorage.removeItem(m)}}catch(e){console.error("Failed to load enrollment data:",e),localStorage.removeItem(m),localStorage.removeItem(b)}})()},[]);let g=e=>{let t=Date.now();try{u(r=>{let a,n={enrolledCourses:a=r.some(t=>t.id===e.id)?r.map(t=>t.id===e.id?e:t):[...r,e],enrollmentTimestamp:t,expirationTime:t+6e5};return localStorage.setItem(b,JSON.stringify(n)),a}),setTimeout(()=>{localStorage.removeItem(b),i(!1),u([]),l(o.n4)},6e5)}catch(e){console.error("Failed to persist enrollment data:",e)}};return(0,a.jsx)(s.Provider,{value:{isEnrolled:r,courseData:d,enrollInCourse:()=>{i(!0);let e={...o.n4,status:"in-progress"};l(e),g(e)},enrollInCourseWithPurchase:e=>{i(!0);let t={...e,status:"in-progress",totalProgress:0};l(t),g(t)},updateCourseProgress:e=>{d.id===e.id&&l(e),u(t=>t.map(t=>t.id===e.id?e:t)),r&&g(e)},enrolledCourses:c,isEnrolledInCourse:e=>c.some(t=>t.id===e),getCourseById:e=>c.find(t=>t.id===e)},"data-sentry-element":"EnrollmentContext.Provider","data-sentry-component":"EnrollmentProvider","data-sentry-source-file":"enrollment-context.tsx",children:t})}},20764:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>i});var a=r(95155);r(12115);var n=r(32467),o=r(83101),s=r(64269);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:o,asChild:d=!1,...l}=e,c=d?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,s.cn)(i({variant:r,size:o,className:t})),...l,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},43756:(e,t,r)=>{r.d(t,{B:()=>v});var a=r(95155),n=r(12115),o=r(32467),s=r(27937),i=r(64269);function d(e){let{...t}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...t,"data-sentry-component":"Breadcrumb","data-sentry-source-file":"breadcrumb.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,i.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t),...r,"data-sentry-component":"BreadcrumbList","data-sentry-source-file":"breadcrumb.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,i.cn)("inline-flex items-center gap-1.5",t),...r,"data-sentry-component":"BreadcrumbItem","data-sentry-source-file":"breadcrumb.tsx"})}function u(e){let{asChild:t,className:r,...n}=e,s=t?o.DX:"a";return(0,a.jsx)(s,{"data-slot":"breadcrumb-link",className:(0,i.cn)("hover:text-foreground transition-colors",r),...n,"data-sentry-element":"Comp","data-sentry-component":"BreadcrumbLink","data-sentry-source-file":"breadcrumb.tsx"})}function m(e){let{className:t,...r}=e;return(0,a.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("text-foreground font-normal",t),...r,"data-sentry-component":"BreadcrumbPage","data-sentry-source-file":"breadcrumb.tsx"})}function b(e){let{children:t,className:r,...n}=e;return(0,a.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...n,"data-sentry-component":"BreadcrumbSeparator","data-sentry-source-file":"breadcrumb.tsx",children:null!=t?t:(0,a.jsx)(s.A,{})})}var g=r(20063);let f={"/dashboard":[{title:"Dashboard",link:"/dashboard"}],"/dashboard/employee":[{title:"Dashboard",link:"/dashboard"},{title:"Employee",link:"/dashboard/employee"}],"/dashboard/product":[{title:"Dashboard",link:"/dashboard"},{title:"Product",link:"/dashboard/product"}],"/courses":[{title:"Home",link:"/"},{title:"Available Courses",link:"/courses"}],"/my-courses":[{title:"Home",link:"/"},{title:"My Courses",link:"/my-courses"}]};var p=r(65508);function v(){let e=function(){let e=(0,g.usePathname)();return(0,n.useMemo)(()=>{if(f[e])return f[e];let t=e.split("/").filter(Boolean);return t.map((e,r)=>{let a="/".concat(t.slice(0,r+1).join("/"));return{title:e.charAt(0).toUpperCase()+e.slice(1),link:a}})},[e])}();return 0===e.length?null:(0,a.jsx)(d,{"data-sentry-element":"Breadcrumb","data-sentry-component":"Breadcrumbs","data-sentry-source-file":"breadcrumbs.tsx",children:(0,a.jsx)(l,{"data-sentry-element":"BreadcrumbList","data-sentry-source-file":"breadcrumbs.tsx",children:e.map((t,r)=>(0,a.jsxs)(n.Fragment,{children:[r!==e.length-1&&(0,a.jsx)(c,{className:"hidden md:block",children:(0,a.jsx)(u,{href:t.link,children:t.title})}),r<e.length-1&&(0,a.jsx)(b,{className:"hidden md:block",children:(0,a.jsx)(p.A,{})}),r===e.length-1&&(0,a.jsx)(m,{children:t.title})]},t.title))})})}},64269:(e,t,r)=>{r.d(t,{cn:()=>o,z:()=>s});var a=r(2821),n=r(75889);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function s(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:n=0,sizeType:o="normal"}=a;if(0===e)return"0 Byte";let s=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,s)).toFixed(n)," ").concat("accurate"===o?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][s])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][s])?r:"Bytes")}},66094:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>s,wL:()=>c});var a=r(95155);r(12115);var n=r(64269);function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},88021:(e,t,r)=>{r.d(t,{E:()=>d});var a=r(95155);r(12115);var n=r(32467),o=r(83101),s=r(64269);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,asChild:o=!1,...d}=e,l=o?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(i({variant:r}),t),...d,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}}]);