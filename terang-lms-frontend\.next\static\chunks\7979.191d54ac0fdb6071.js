try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="228d170a-9260-4b83-a624-12926b17e198",e._sentryDebugIdIdentifier="sentry-dbid-228d170a-9260-4b83-a624-12926b17e198")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7979],{47979:(e,n,d)=>{d.r(n),d.d(n,{diff:()=>t});var r={"+":"inserted","-":"deleted","@":"meta"};let t={name:"diff",token:function(e){var n=e.string.search(/[\t ]+?$/);if(!e.sol()||0===n)return e.skipToEnd(),("error "+(r[e.string.charAt(0)]||"")).replace(/ $/,"");var d=r[e.peek()]||e.skipToEnd();return -1===n?e.skipToEnd():e.pos=n,d}}}}]);