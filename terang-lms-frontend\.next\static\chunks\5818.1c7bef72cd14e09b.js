try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="63a21713-0364-49f5-8997-514f1961ab3e",e._sentryDebugIdIdentifier="sentry-dbid-63a21713-0364-49f5-8997-514f1961ab3e")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5818],{75818:(e,t,a)=>{a.r(t),a.d(t,{spreadsheet:()=>r});let r={name:"spreadsheet",startState:function(){return{stringType:null,stack:[]}},token:function(e,t){if(e){switch(0===t.stack.length&&('"'==e.peek()||"'"==e.peek())&&(t.stringType=e.peek(),e.next(),t.stack.unshift("string")),t.stack[0]){case"string":for(;"string"===t.stack[0]&&!e.eol();)e.peek()===t.stringType?(e.next(),t.stack.shift()):"\\"===e.peek()?(e.next(),e.next()):e.match(/^.[^\\\"\']*/);return"string";case"characterClass":for(;"characterClass"===t.stack[0]&&!e.eol();)e.match(/^[^\]\\]+/)||e.match(/^\\./)||t.stack.shift();return"operator"}var a=e.peek();switch(a){case"[":return e.next(),t.stack.unshift("characterClass"),"bracket";case":":return e.next(),"operator";case"\\":if(e.match(/\\[a-z]+/))return"string.special";return e.next(),"atom";case".":case",":case";":case"*":case"-":case"+":case"^":case"<":case"/":case"=":return e.next(),"atom";case"$":return e.next(),"builtin"}if(e.match(/\d+/))return e.match(/^\w+/)?"error":"number";if(e.match(/^[a-zA-Z_]\w*/))return e.match(/(?=[\(.])/,!1)?"keyword":"variable";if(-1!=["[","]","(",")","{","}"].indexOf(a))return e.next(),"bracket";e.eatSpace()||e.next();return null}}}}}]);