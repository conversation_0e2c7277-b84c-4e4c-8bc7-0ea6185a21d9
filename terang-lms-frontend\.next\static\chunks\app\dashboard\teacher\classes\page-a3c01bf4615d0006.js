try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1d635195-0034-4ea5-acd2-aae4db3fae16",e._sentryDebugIdIdentifier="sentry-dbid-1d635195-0034-4ea5-acd2-aae4db3fae16")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8440],{5917:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6191:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},11010:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},12833:(e,t,s)=>{"use strict";s.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>m,lp:()=>x,mB:()=>h,rI:()=>o,ty:()=>i});var a=s(95155);s(12115);var r=s(47971),n=s(5917),d=s(64269);function o(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function i(e){let{...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function l(e){let{className:t,sideOffset:s=4,...n}=e;return(0,a.jsx)(r.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,a.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:s,variant:n="default",...o}=e;return(0,a.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":n,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:s,checked:o,...i}=e;return(0,a.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,a.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(n.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),s]})}function x(e){let{className:t,inset:s,...n}=e;return(0,a.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":s,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},20764:(e,t,s)=>{"use strict";s.d(t,{$:()=>i,r:()=>o});var a=s(95155);s(12115);var r=s(32467),n=s(83101),d=s(64269);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,size:n,asChild:i=!1,...l}=e,c=i?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,d.cn)(o({variant:s,size:n,className:t})),...l,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},31936:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155);s(12115);var r=s(64269);function n(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},35299:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},37772:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},42196:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},47886:(e,t,s)=>{"use strict";s.d(t,{WG:()=>r,cl:()=>d,qs:()=>a});let a={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},r=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=a.getUser();return e||(window.location.href="/auth/sign-in",null)},d=e=>{let t=n();return t?t.role!==e?(window.location.href=r(t),null):t:null}},47937:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},64269:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n,z:()=>d});var a=s(2821),r=s(75889);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function d(e){var t,s;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=a;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(s=["Bytes","KB","MB","GB","TB"][d])?s:"Bytes")}},66094:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>d,wL:()=>c});var a=s(95155);s(12115);var r=s(64269);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},71360:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74888:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(95155),r=s(12115),n=s(66094),d=s(20764),o=s(31936),i=s(12833),l=s(6191),c=s(86651),u=s(42196),m=s(37772),x=s(11010),h=s(91169),p=s(35299),f=s(71360),y=s(47937),g=s(52619),v=s.n(g),b=s(47886),w=s(18720),j=s(88941);let N=()=>(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-component":"ClassSkeleton","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(j.E,{className:"h-48 w-full rounded-lg mb-4","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-6 w-3/4 mb-2","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-4 w-full mb-1","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-4 w-2/3","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.E,{className:"h-4 w-20","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-4 w-24","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.E,{className:"h-4 w-20","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-4 w-24","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)(j.E,{className:"h-6 w-20","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-4 w-32","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"})]})]}),(0,a.jsxs)(n.wL,{className:"flex justify-end gap-3","data-sentry-element":"CardFooter","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(j.E,{className:"h-9 w-16","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(j.E,{className:"h-9 w-16","data-sentry-element":"Skeleton","data-sentry-source-file":"page.tsx"})]})]});function k(){let[e,t]=(0,r.useState)(""),[s,g]=(0,r.useState)([]),[j,k]=(0,r.useState)(!0),[C,M]=(0,r.useState)(null);(0,r.useEffect)(()=>{A()},[]);let A=async()=>{try{let e=b.qs.getUser();if(!e)return void w.oR.error("Please log in to view classes");let t=await fetch("/api/classes?teacherId=".concat(e.id)),s=await t.json();s.success?g(s.classes||[]):w.oR.error(s.error||"Failed to fetch classes")}catch(e){console.error("Error fetching classes:",e),w.oR.error("Failed to fetch classes")}finally{k(!1)}},S=async e=>{if(confirm("Are you sure you want to delete this class? This action cannot be undone.")){M(e);try{let t=b.qs.getUser();if(!t)return void w.oR.error("Please log in to delete classes");let s=await fetch("/api/classes/".concat(e,"?teacherId=").concat(t.id),{method:"DELETE"}),a=await s.json();a.success?(w.oR.success("Class deleted successfully"),A()):w.oR.error(a.error||"Failed to delete class")}catch(e){console.error("Error deleting class:",e),w.oR.error("Failed to delete class")}finally{M(null)}}},_=s.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ClassesPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Classes"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your classes and student groups"})]}),(0,a.jsx)(v(),{href:"/dashboard/teacher/classes/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(d.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Create Class"]})})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(c.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(o.p,{placeholder:"Search classes...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:j?Array.from({length:6}).map((e,t)=>(0,a.jsx)(N,{},t)):0===_.length?(0,a.jsxs)("div",{className:"col-span-full flex flex-col items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC69‍\uD83C\uDFEB"}),(0,a.jsx)("h3",{className:"mt-2 text-xl font-semibold",children:"No classes found"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:e?"Try adjusting your search terms.":"Get started by creating a new class."}),!e&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)(v(),{href:"/dashboard/teacher/classes/new",children:(0,a.jsxs)(d.$,{children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Create Class"]})})})]}):_.map(e=>(0,a.jsxs)(n.Zp,{className:"relative overflow-hidden animate-fade-in w-full",children:[(0,a.jsxs)(n.aR,{className:"p-0",children:[(0,a.jsx)("div",{className:"p-6 pb-0",children:(0,a.jsx)("div",{className:"h-48 w-full overflow-hidden rounded-lg",children:e.coverPicture?(0,a.jsx)("img",{src:e.coverPicture,alt:"Cover for ".concat(e.name),loading:"lazy",className:"h-full w-full object-cover"}):(0,a.jsx)("div",{className:"h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,a.jsx)(u.A,{className:"h-16 w-16 text-gray-400"})})})}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1 flex-1",children:[(0,a.jsx)(n.ZB,{className:"text-xl",children:e.name}),(0,a.jsxs)(n.BT,{className:"text-sm",children:[e.description.substring(0,100),e.description.length>100?"...":""]})]}),(0,a.jsxs)(i.rI,{children:[(0,a.jsx)(i.ty,{asChild:!0,children:(0,a.jsx)(d.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(i.SQ,{align:"end",children:[(0,a.jsx)(i._2,{asChild:!0,children:(0,a.jsxs)(v(),{href:"/dashboard/teacher/classes/".concat(e.id),children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,a.jsx)(i._2,{asChild:!0,children:(0,a.jsxs)(v(),{href:"/dashboard/teacher/classes/".concat(e.id,"/students"),children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Manage Students"]})}),(0,a.jsxs)(i._2,{className:"text-red-600",onClick:()=>S(e.id),disabled:C===e.id,children:[C===e.id?(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),C===e.id?"Deleting...":"Delete"]})]})]})]})})]}),(0,a.jsx)(n.Wu,{className:"px-6 pt-5 pb-4 space-y-2 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"text-muted-foreground h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.studentCount}),(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:"Students"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"text-muted-foreground h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.courseCount}),(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:"Courses"})]})]})}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(v(),{href:"/dashboard/teacher/classes/".concat(e.id),children:(0,a.jsx)(d.$,{children:"View Class"})})})]},e.id))})]})}},86651:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88941:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(95155),r=s(64269);function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,r.cn)("bg-accent animate-pulse rounded-md",t),...s,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},91169:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},96346:(e,t,s)=>{Promise.resolve().then(s.bind(s,74888))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,7971,4850,8441,3840,7358],()=>t(96346)),_N_E=e.O()}]);