try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="61af8773-69d6-4231-a626-ce1dc778b235",e._sentryDebugIdIdentifier="sentry-dbid-61af8773-69d6-4231-a626-ce1dc778b235")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7949],{26737:(e,s,t)=>{"use strict";t.d(s,{k:()=>l});var a=t(95155),r=t(12115),n=t(9484),d=t(64269);let l=r.forwardRef((e,s)=>{let{className:t,value:r,...l}=e;return(0,a.jsx)(n.bL,{ref:s,className:(0,d.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,a.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});l.displayName=n.bL.displayName},30710:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(95155),r=t(66094),n=t(20764),d=t(88021),l=t(26737),i=t(47937),c=t(1524),o=t(52472),u=t(26983),x=t(52619),m=t.n(x),f=t(47886),y=t(15868),p=t(12115);function h(){let[e,s]=(0,p.useState)(f.qs.getUser()),[t,x]=(0,p.useState)(!0);if((0,p.useEffect)(()=>{s(f.qs.getUser()),x(!1)},[]),t)return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading..."});if(!(null==e?void 0:e.institutionId))return(0,a.jsx)(y.A,{userRole:"student"});let h={enrolledCourses:3,completedCourses:1,certificates:1,totalHours:24};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentDashboard","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track your learning progress and access your courses"})]}),(0,a.jsx)(m(),{href:"/dashboard/student/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(n.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"}),"Browse Courses"]})})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Enrolled Courses"}),(0,a.jsx)(i.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.enrolledCourses}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Active enrollments"})]})]}),(0,a.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Completed"}),(0,a.jsx)(c.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.completedCourses}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Courses completed"})]})]}),(0,a.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Certificates"}),(0,a.jsx)(o.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Award","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.certificates}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Earned certificates"})]})]}),(0,a.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Study Hours"}),(0,a.jsx)(u.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Clock","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:h.totalHours}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Total hours"})]})]})]}),(0,a.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(r.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(r.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"My Courses"}),(0,a.jsx)(r.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your current course enrollments and progress"})]}),(0,a.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"space-y-4",children:[{id:1,name:"Introduction to Mathematics",type:"self_paced",progress:85,status:"in_progress",dueDate:"2024-12-15"},{id:2,name:"Basic Physics",type:"verified",progress:45,status:"in_progress",dueDate:"2024-11-30"},{id:3,name:"Chemistry Fundamentals",type:"self_paced",progress:100,status:"completed",dueDate:"2024-10-20"}].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.E,{variant:"verified"===e.type?"default":"secondary",children:e.type}),(0,a.jsx)(d.E,{variant:"completed"===e.status?"default":"outline",children:e.status})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)(l.k,{value:e.progress,className:"h-2"})]}),(0,a.jsxs)("p",{className:"text-muted-foreground text-xs",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)(m(),{href:"/dashboard/student/courses/".concat(e.id),children:(0,a.jsx)(n.$,{variant:"outline",size:"sm",children:"completed"===e.status?"Review":"Continue"})})})]},e.id))}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(m(),{href:"/dashboard/student/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(n.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"View All Courses"})})})]})]})]})}},45723:(e,s,t)=>{Promise.resolve().then(t.bind(t,30710))}},e=>{var s=s=>e(e.s=s);e.O(0,[4909,7055,9034,1512,3984,4850,8441,3840,7358],()=>s(45723)),_N_E=e.O()}]);