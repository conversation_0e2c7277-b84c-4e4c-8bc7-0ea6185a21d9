/* MDX Editor Styles */
@import '@mdxeditor/editor/style.css';

.mdx-editor-wrapper {
  border: 1px solid hsl(var(--border));
  border-radius: calc(var(--radius) - 2px);
  overflow: hidden;
}

.mdx-editor-wrapper .mdxeditor,
.mdx-editor-wrapper [data-mdxeditor] {
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  font-family: inherit !important;
}

.mdx-editor-wrapper .mdxeditor-toolbar {
  background: hsl(var(--muted));
  border-bottom: 1px solid hsl(var(--border));
  padding: 8px;
}

.mdx-editor-wrapper .mdxeditor-toolbar button {
  background: transparent;
  border: 1px solid transparent;
  border-radius: calc(var(--radius) - 2px);
  color: hsl(var(--foreground));
  padding: 6px 8px;
  margin: 0 2px;
  transition: all 0.2s;
}

.mdx-editor-wrapper .mdxeditor-toolbar button:hover {
  background: hsl(var(--accent));
  border-color: hsl(var(--border));
}

.mdx-editor-wrapper .mdxeditor-toolbar button[data-state="on"] {
  background: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.mdx-editor-wrapper .mdxeditor-content-editable {
  padding: 16px;
  min-height: 200px;
  font-size: 14px;
  line-height: 1.6;
}

.mdx-editor-wrapper .mdxeditor-content-editable:focus {
  outline: none;
}

/* Header styles with higher specificity */
.mdx-editor-wrapper .mdxeditor-content-editable h1,
.mdx-editor-wrapper [data-mdxeditor] h1 {
  font-size: 2em !important;
  font-weight: bold !important;
  margin: 1em 0 0.5em 0 !important;
  line-height: 1.2 !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable h2,
.mdx-editor-wrapper [data-mdxeditor] h2 {
  font-size: 1.5em !important;
  font-weight: bold !important;
  margin: 0.8em 0 0.4em 0 !important;
  line-height: 1.3 !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable h3,
.mdx-editor-wrapper [data-mdxeditor] h3 {
  font-size: 1.25em !important;
  font-weight: bold !important;
  margin: 0.6em 0 0.3em 0 !important;
  line-height: 1.4 !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable h4,
.mdx-editor-wrapper [data-mdxeditor] h4 {
  font-size: 1.1em !important;
  font-weight: bold !important;
  margin: 0.5em 0 0.25em 0 !important;
  line-height: 1.4 !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable h5,
.mdx-editor-wrapper [data-mdxeditor] h5 {
  font-size: 1em !important;
  font-weight: bold !important;
  margin: 0.4em 0 0.2em 0 !important;
  line-height: 1.4 !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable h6,
.mdx-editor-wrapper [data-mdxeditor] h6 {
  font-size: 0.9em !important;
  font-weight: bold !important;
  margin: 0.3em 0 0.15em 0 !important;
  line-height: 1.4 !important;
  color: hsl(var(--foreground)) !important;
}

/* Paragraph and list styles */
.mdx-editor-wrapper .mdxeditor-content-editable p,
.mdx-editor-wrapper [data-mdxeditor] p {
  margin: 0.5em 0 !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable ul,
.mdx-editor-wrapper .mdxeditor-content-editable ol,
.mdx-editor-wrapper [data-mdxeditor] ul,
.mdx-editor-wrapper [data-mdxeditor] ol {
  margin: 0.5em 0 !important;
  padding-left: 1.5em !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable li,
.mdx-editor-wrapper [data-mdxeditor] li {
  margin: 0.25em 0 !important;
  color: hsl(var(--foreground)) !important;
}

/* Blockquote and code styles */
.mdx-editor-wrapper .mdxeditor-content-editable blockquote,
.mdx-editor-wrapper [data-mdxeditor] blockquote {
  border-left: 4px solid hsl(var(--border)) !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  font-style: italic !important;
  color: hsl(var(--muted-foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable code,
.mdx-editor-wrapper [data-mdxeditor] code {
  background: hsl(var(--muted)) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.9em !important;
  color: hsl(var(--foreground)) !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable pre,
.mdx-editor-wrapper [data-mdxeditor] pre {
  background: hsl(var(--muted)) !important;
  padding: 1em !important;
  border-radius: calc(var(--radius) - 2px) !important;
  overflow-x: auto !important;
  margin: 1em 0 !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable pre code,
.mdx-editor-wrapper [data-mdxeditor] pre code {
  background: transparent !important;
  padding: 0 !important;
}

.mdx-editor-wrapper .mdxeditor-content-editable table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable th,
.mdx-editor-wrapper .mdxeditor-content-editable td {
  border: 1px solid hsl(var(--border));
  padding: 8px 12px;
  text-align: left;
}

.mdx-editor-wrapper .mdxeditor-content-editable th {
  background: hsl(var(--muted));
  font-weight: bold;
}

.mdx-editor-wrapper .mdxeditor-content-editable img {
  max-width: 100%;
  height: auto;
  border-radius: calc(var(--radius) - 2px);
  margin: 0.5em 0;
}

.mdx-editor-wrapper .mdxeditor-content-editable a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.mdx-editor-wrapper .mdxeditor-content-editable a:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Dark mode adjustments */
.dark .mdx-editor-wrapper .mdxeditor-toolbar {
  background: hsl(var(--muted));
}

.dark .mdx-editor-wrapper .mdxeditor-toolbar button:hover {
  background: hsl(var(--accent));
}

/* Focus styles */
.mdx-editor-wrapper:focus-within {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Placeholder styles */
.mdx-editor-wrapper .mdxeditor-content-editable[data-placeholder]:empty::before {
  content: attr(data-placeholder);
  color: hsl(var(--muted-foreground));
  font-style: italic;
}
