try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="63eff13a-a964-43be-a1c4-a950d87c1c5c",e._sentryDebugIdIdentifier="sentry-dbid-63eff13a-a964-43be-a1c4-a950d87c1c5c")}catch(e){}(()=>{var e={};e.id=2856,e.ids=[2856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4978:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6902:(e,t,s)=>{Promise.resolve().then(s.bind(s,28782))},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var r=s(91754);s(93491);var a=s(82233);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,s)=>{Promise.resolve().then(s.bind(s,7346)),Promise.resolve().then(s.bind(s,21444)),Promise.resolve().then(s.bind(s,3033)),Promise.resolve().then(s.bind(s,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21626:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(91754);s(93491);var a=s(66207),n=s(82233);function i({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},21820:e=>{"use strict";e.exports=require("os")},25718:(e,t,s)=>{Promise.resolve().then(s.bind(s,83418))},28354:e=>{"use strict";e.exports=require("util")},28782:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),i=s(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof o?new Proxy(o,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin",componentType:"Layout",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):o;let c=void 0,u=void 0,p=void 0,m=r},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40254:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(81012);function a(){return{toast:({title:e,description:t,variant:s="default"})=>{"destructive"===s?r.oR.error(e,{description:t}):r.oR.success(e,{description:t})}}}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46814:(e,t,s)=>{Promise.resolve().then(s.bind(s,49540))},48161:e=>{"use strict";e.exports=require("node:os")},49540:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(91754);function a({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}s(93491),s(76328)},52466:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>v,generateImageMetadata:()=>f,generateMetadata:()=>h,generateViewport:()=>g,metadata:()=>p});var a=s(63033),n=s(18188),i=s(5434),o=s(45188),d=s(67999),l=s(4590),c=s(23064),u=s(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),s=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:s,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...a},y="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;r=new Proxy(m,{apply:(e,t,s)=>{let r,a,n;try{let e=y?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}});let h=void 0,f=void 0,g=void 0,v=r},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66207:(e,t,s)=>{"use strict";s.d(t,{b:()=>o});var r=s(93491),a=s(90604),n=s(91754),i=r.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},69122:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>l,yv:()=>c});var r=s(91754);s(93491);var a=s(97543),n=s(33093),i=s(87435),o=s(20388),d=s(82233);function l({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u({className:e,size:t="default",children:s,...i}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[s,(0,r.jsx)(a.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,r.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function p({className:e,children:t,position:s="popper",...n}){return(0,r.jsx)(a.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,r.jsx)(x,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:t}),(0,r.jsx)(y,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function m({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,r.jsx)(i.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,r.jsx)(a.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:t})]})}function x({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,r.jsx)(o.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function y({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,r.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},72574:(e,t,s)=>{Promise.resolve().then(s.bind(s,89012))},73024:e=>{"use strict";e.exports=require("node:fs")},73562:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,s)=>{Promise.resolve().then(s.bind(s,5434)),Promise.resolve().then(s.bind(s,45188)),Promise.resolve().then(s.bind(s,67999)),Promise.resolve().then(s.bind(s,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78381:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var r=s(95500),a=s(56947),n=s(26052),i=s(13636),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["dashboard",{children:["admin",{children:["users",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83418)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,28782)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\[id]\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/users/[id]/page",pathname:"/dashboard/admin/users/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80506:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},83418:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),i=s(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\users\\[id]\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof o?new Proxy(o,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin/users/[id]",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):o;let c=void 0,u=void 0,p=void 0,m=r},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89012:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var r=s(91754),a=s(93491),n=s(21372),i=s(9260),o=s(56682),d=s(59672),l=s(21626),c=s(69122),u=s(88373),p=s(4978),m=s(52466),x=s(80506),y=s(73562),h=s(16041),f=s.n(h),g=s(40254);function v(){let e=(0,n.useRouter)(),t=(0,n.useParams)().id,{toast:s}=(0,g.d)(),[h,v]=(0,a.useState)(!1),[b,j]=(0,a.useState)(!0),[S,w]=(0,a.useState)(null),[A,N]=(0,a.useState)([]),[C,P]=(0,a.useState)(!1),[q,_]=(0,a.useState)(!1),[k,D]=(0,a.useState)({name:"",email:"",password:"",role:"student",institutionId:""}),I=async r=>{r.preventDefault(),v(!0);try{let r={name:k.name,email:k.email,role:k.role,institutionId:k.institutionId&&"none"!==k.institutionId?parseInt(k.institutionId):null};k.password.trim()&&(r.password=k.password);let a=await fetch(`/api/users/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),n=await a.json();n.success?(s({title:"Success",description:"User updated successfully"}),e.push("/dashboard/admin/users")):s({title:"Error",description:n.error||"Failed to update user",variant:"destructive"})}catch(e){console.error("Error updating user:",e),s({title:"Error",description:"Failed to update user",variant:"destructive"})}finally{v(!1)}},E=(e,t)=>{D(s=>({...s,[e]:t}))};return b?(0,r.jsxs)("div",{className:"flex min-h-[400px] items-center justify-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("p",{className:"ml-2",children:"Loading user..."})]}):S?(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"EditUserPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(f(),{href:"/dashboard/admin/users","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(o.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit User"}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Update user information for ",S.name]})]})]}),(0,r.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"User Details"}),(0,r.jsxs)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:["Update the information for ",S.name]})]}),(0,r.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Full Name"}),(0,r.jsx)(d.p,{id:"name",value:k.name,onChange:e=>E("name",e.target.value),placeholder:"Enter full name",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"email","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Email Address"}),(0,r.jsx)(d.p,{id:"email",type:"email",value:k.email,onChange:e=>E("email",e.target.value),placeholder:"Enter email address",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"password","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"New Password (Optional)"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.p,{id:"password",type:q?"text":"password",value:k.password,onChange:e=>E("password",e.target.value),placeholder:"Leave empty to keep current password",className:"pr-20","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center space-x-1 pr-2",children:(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>_(!q),className:"h-7 w-7 p-0","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:q?(0,r.jsx)(m.A,{className:"h-4 w-4"}):(0,r.jsx)(x.A,{className:"h-4 w-4"})})})]}),(0,r.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",t="";for(let s=0;s<12;s++)t+=e.charAt(Math.floor(Math.random()*e.length));E("password",t)},className:"mt-2","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Generate New Password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"role","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"User Role"}),(0,r.jsxs)(c.l6,{value:k.role,onValueChange:e=>E("role",e),required:!0,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(c.yv,{placeholder:"Select user role","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsxs)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.eb,{value:"student","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Student"}),(0,r.jsx)(c.eb,{value:"teacher","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Teacher"}),(0,r.jsx)(c.eb,{value:"super_admin","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Super Admin"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,r.jsx)(l.J,{htmlFor:"institution","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,r.jsxs)(c.l6,{value:k.institutionId,onValueChange:e=>E("institutionId",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(c.yv,{placeholder:"Select institution (optional)","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsxs)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.eb,{value:"none","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"No Institution"}),A.map(e=>(0,r.jsx)(c.eb,{value:e.id.toString(),children:e.name},e.id))]})]})]})]}),(0,r.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(i.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"User Information"})}),(0,r.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-semibold",children:"Account Details:"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:[(0,r.jsxs)("p",{children:["User ID: ",S.id]}),(0,r.jsxs)("p",{children:["Created: ",new Date(S.created_at).toLocaleDateString()]}),(0,r.jsxs)("p",{children:["Last Updated: ",new Date(S.updated_at).toLocaleDateString()]}),(0,r.jsxs)("p",{children:["Current Institution: ",S.institution_name||"None"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-semibold",children:"Role Permissions:"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:["student"===k.role&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"• Can enroll in courses"}),(0,r.jsx)("p",{children:"• Can take quizzes and assignments"}),(0,r.jsx)("p",{children:"• Can view progress and grades"})]}),"teacher"===k.role&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"• Can create and manage courses"}),(0,r.jsx)("p",{children:"• Can create quizzes and assignments"}),(0,r.jsx)("p",{children:"• Can grade student submissions"})]}),"super_admin"===k.role&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("p",{children:"• Full platform access"}),(0,r.jsx)("p",{children:"• Can manage all institutions and users"}),(0,r.jsx)("p",{children:"• Can access billing and analytics"})]})]})]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(f(),{href:"/dashboard/admin/users","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(o.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,r.jsxs)(o.$,{type:"submit",disabled:h,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),h?"Updating...":"Update User"]})]})]})})]})]}):(0,r.jsxs)("div",{className:"py-8 text-center",children:[(0,r.jsx)("p",{children:"User not found"}),(0,r.jsx)(f(),{href:"/dashboard/admin/users",children:(0,r.jsx)(o.$,{className:"mt-4",children:"Back to Users"})})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8134,8634],()=>s(78381));module.exports=r})();
//# sourceMappingURL=page.js.map