try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4b4195a6-dbc6-42eb-96ab-dd3917437736",e._sentryDebugIdIdentifier="sentry-dbid-4b4195a6-dbc6-42eb-96ab-dd3917437736")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2100],{72100:(e,t,n)=>{function r(e){for(var t={},n=e.split(" "),r=0;r<n.length;++r)t[n[r]]=!0;return t}n.r(t),n.d(t,{ecl:()=>w});var a,o=r("abs acos allnodes ascii asin asstring atan atan2 ave case choose choosen choosesets clustersize combine correlation cos cosh count covariance cron dataset dedup define denormalize distribute distributed distribution ebcdic enth error evaluate event eventextra eventname exists exp failcode failmessage fetch fromunicode getisvalid global graph group hash hash32 hash64 hashcrc hashmd5 having if index intformat isvalid iterate join keyunicode length library limit ln local log loop map matched matchlength matchposition matchtext matchunicode max merge mergejoin min nolocal nonempty normalize parse pipe power preload process project pull random range rank ranked realformat recordof regexfind regexreplace regroup rejected rollup round roundup row rowdiff sample set sin sinh sizeof soapcall sort sorted sqrt stepped stored sum table tan tanh thisnode topn tounicode transfer trim truncate typeof ungroup unicodeorder variance which workunit xmldecode xmlencode xmltext xmlunicode"),i=r("apply assert build buildindex evaluate fail keydiff keypatch loadxml nothor notify output parallel sequential soapcall wait"),l=r("__compressed__ all and any as atmost before beginc++ best between case const counter csv descend encrypt end endc++ endmacro except exclusive expire export extend false few first flat from full function group header heading hole ifblock import in interface joined keep keyed last left limit load local locale lookup macro many maxcount maxlength min skew module named nocase noroot noscan nosort not of only opt or outer overwrite packed partition penalty physicallength pipe quote record relationship repeat return right scan self separator service shared skew skip sql store terminator thor threshold token transform trim true type unicodeorder unsorted validate virtual whole wild within xml xpath"),s=r("ascii big_endian boolean data decimal ebcdic integer pattern qstring real record rule set of string token udecimal unicode unsigned varstring varunicode"),u=r("checkpoint deprecated failcode failmessage failure global independent onwarning persist priority recovery stored success wait when"),c=r("catch class do else finally for if switch try while"),d=r("true false null"),p={"#":function(e,t){return!!t.startOfLine&&(e.skipToEnd(),"meta")}},f=/[+\-*&%=<>!?|\/]/;function m(e,t){var n,r=e.next();if(p[r]){var y=p[r](e,t);if(!1!==y)return y}if('"'==r||"'"==r){return n=r,t.tokenize=function(e,t){for(var r,a=!1,o=!1;null!=(r=e.next());){if(r==n&&!a){o=!0;break}a=!a&&"\\"==r}return(o||!a)&&(t.tokenize=m),"string"},t.tokenize(e,t)}if(/[\[\]{}\(\),;\:\.]/.test(r))return a=r,null;if(/\d/.test(r))return e.eatWhile(/[\w\.]/),"number";if("/"==r){if(e.eat("*"))return t.tokenize=h,h(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(f.test(r))return e.eatWhile(f),"operator";e.eatWhile(/[\w\$_]/);var b=e.current().toLowerCase();if(o.propertyIsEnumerable(b))return c.propertyIsEnumerable(b)&&(a="newstatement"),"keyword";if(i.propertyIsEnumerable(b))return c.propertyIsEnumerable(b)&&(a="newstatement"),"variable";if(l.propertyIsEnumerable(b))return c.propertyIsEnumerable(b)&&(a="newstatement"),"modifier";if(s.propertyIsEnumerable(b))return c.propertyIsEnumerable(b)&&(a="newstatement"),"type";if(u.propertyIsEnumerable(b))return c.propertyIsEnumerable(b)&&(a="newstatement"),"builtin";for(var g=b.length-1;g>=0&&(!isNaN(b[g])||"_"==b[g]);)--g;if(g>0){var w=b.substr(0,g+1);if(s.propertyIsEnumerable(w))return c.propertyIsEnumerable(w)&&(a="newstatement"),"type"}return d.propertyIsEnumerable(b)?"atom":null}function h(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=m;break}r="*"==n}return"comment"}function y(e,t,n,r,a){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=a}function b(e,t,n){return e.context=new y(e.indented,t,n,null,e.context)}function g(e){var t=e.context.type;return(")"==t||"]"==t||"}"==t)&&(e.indented=e.context.indented),e.context=e.context.prev}let w={name:"ecl",startState:function(e){return{tokenize:null,context:new y(-e,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,t){var n=t.context;if(e.sol()&&(null==n.align&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return null;a=null;var r=(t.tokenize||m)(e,t);if("comment"==r||"meta"==r)return r;if(null==n.align&&(n.align=!0),(";"==a||":"==a)&&"statement"==n.type)g(t);else if("{"==a)b(t,e.column(),"}");else if("["==a)b(t,e.column(),"]");else if("("==a)b(t,e.column(),")");else if("}"==a){for(;"statement"==n.type;)n=g(t);for("}"==n.type&&(n=g(t));"statement"==n.type;)n=g(t)}else a==n.type?g(t):("}"==n.type||"top"==n.type||"statement"==n.type&&"newstatement"==a)&&b(t,e.column(),"statement");return t.startOfLine=!1,r},indent:function(e,t,n){if(e.tokenize!=m&&null!=e.tokenize)return 0;var r=e.context,a=t&&t.charAt(0);"statement"==r.type&&"}"==a&&(r=r.prev);var o=a==r.type;return"statement"==r.type?r.indented+("{"==a?0:n.unit):r.align?r.column+ +!o:r.indented+(o?0:n.unit)},languageData:{indentOnInput:/^\s*[{}]$/}}}}]);