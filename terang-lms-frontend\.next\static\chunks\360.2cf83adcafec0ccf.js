try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="d90930eb-8c97-4d72-809f-f8680a790960",e._sentryDebugIdIdentifier="sentry-dbid-d90930eb-8c97-4d72-809f-f8680a790960")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[360],{87979:(e,r,t)=>{function n(e){var r,t,n=e.statementIndent,a=e.jsonld,i=e.json||a,o=e.typescript,u=e.wordCharacters||/[\w$\xa1-\uffff]/,s=function(){function e(e){return{type:e,style:"keyword"}}var r=e("keyword a"),t=e("keyword b"),n=e("keyword c"),a=e("keyword d"),i=e("operator"),o={type:"atom",style:"atom"};return{if:e("if"),while:r,with:r,else:t,do:t,try:t,finally:t,return:a,break:a,continue:a,new:e("new"),delete:n,void:n,throw:n,debugger:e("debugger"),var:e("var"),const:e("var"),let:e("var"),function:e("function"),catch:e("catch"),for:e("for"),switch:e("switch"),case:e("case"),default:e("default"),in:i,typeof:i,instanceof:i,true:o,false:o,null:o,undefined:o,NaN:o,Infinity:o,this:e("this"),class:e("class"),super:e("atom"),yield:n,export:e("export"),import:e("import"),extends:n,await:n}}(),f=/[+\-*&%=<>!?|~^@]/,l=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function c(e,n,a){return r=e,t=a,n}function d(e,r){var t,n,i,o=e.next();if('"'==o||"'"==o){return t=o,r.tokenize=function(e,r){var n,i=!1;if(a&&"@"==e.peek()&&e.match(l))return r.tokenize=d,c("jsonld-keyword","meta");for(;null!=(n=e.next())&&(n!=t||i);)i=!i&&"\\"==n;return i||(r.tokenize=d),c("string","string")},r.tokenize(e,r)}if("."==o&&e.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return c("number","number");if("."==o&&e.match(".."))return c("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(o))return c(o);if("="==o&&e.eat(">"))return c("=>","operator");else if("0"==o&&e.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return c("number","number");else if(/\d/.test(o))return e.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),c("number","number");else if("/"==o)if(e.eat("*"))return r.tokenize=m,m(e,r);else{if(e.eat("/"))return e.skipToEnd(),c("comment","comment");if(n=e,(i=r).tokenize==d&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(i.lastType)||"quasi"==i.lastType&&/\{\s*$/.test(n.string.slice(0,n.pos-1)))return!function(e){for(var r,t=!1,n=!1;null!=(r=e.next());){if(!t){if("/"==r&&!n)return;"["==r?n=!0:n&&"]"==r&&(n=!1)}t=!t&&"\\"==r}}(e),e.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),c("regexp","string.special");else return e.eat("="),c("operator","operator",e.current())}else if("`"==o)return r.tokenize=p,p(e,r);else if("#"==o&&"!"==e.peek())return e.skipToEnd(),c("meta","meta");else if("#"==o&&e.eatWhile(u))return c("variable","property");else if("<"==o&&e.match("!--")||"-"==o&&e.match("->")&&!/\S/.test(e.string.slice(0,e.start)))return e.skipToEnd(),c("comment","comment");else if(f.test(o))return((">"!=o||!r.lexical||">"!=r.lexical.type)&&(e.eat("=")?("!"==o||"="==o)&&e.eat("="):/[<>*+\-|&?]/.test(o)&&(e.eat(o),">"==o&&e.eat(o))),"?"==o&&e.eat("."))?c("."):c("operator","operator",e.current());else if(u.test(o)){e.eatWhile(u);var k=e.current();if("."!=r.lastType){if(s.propertyIsEnumerable(k)){var y=s[k];return c(y.type,y.style,k)}if("async"==k&&e.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return c("async","keyword",k)}return c("variable","variable",k)}}function m(e,r){for(var t,n=!1;t=e.next();){if("/"==t&&n){r.tokenize=d;break}n="*"==t}return c("comment","comment")}function p(e,r){for(var t,n=!1;null!=(t=e.next());){if(!n&&("`"==t||"$"==t&&e.eat("{"))){r.tokenize=d;break}n=!n&&"\\"==t}return c("quasi","string.special",e.current())}function k(e,r){r.fatArrowAt&&(r.fatArrowAt=null);var t=e.string.indexOf("=>",e.start);if(!(t<0)){if(o){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(e.string.slice(e.start,t));n&&(t=n.index)}for(var a=0,i=!1,s=t-1;s>=0;--s){var f=e.string.charAt(s),l="([{}])".indexOf(f);if(l>=0&&l<3){if(!a){++s;break}if(0==--a){"("==f&&(i=!0);break}}else if(l>=3&&l<6)++a;else if(u.test(f))i=!0;else if(/["'\/`]/.test(f))for(;;--s){if(0==s)return;if(e.string.charAt(s-1)==f&&"\\"!=e.string.charAt(s-2)){s--;break}}else if(i&&!a){++s;break}}i&&!a&&(r.fatArrowAt=s)}}var y={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function v(e,r,t,n,a,i){this.indented=e,this.column=r,this.type=t,this.prev=a,this.info=i,null!=n&&(this.align=n)}var w={state:null,column:null,marked:null,cc:null};function b(){for(var e=arguments.length-1;e>=0;e--)w.cc.push(arguments[e])}function h(){return b.apply(null,arguments),!0}function g(e,r){for(var t=r;t;t=t.next)if(t.name==e)return!0;return!1}function x(r){var t=w.state;if(w.marked="def",t.context){if("var"==t.lexical.info&&t.context&&t.context.block){var n=function e(r,t){if(!t)return null;if(t.block){var n=e(r,t.prev);return n?n==t.prev?t:new A(n,t.vars,!0):null}return g(r,t.vars)?t:new A(t.prev,new z(r,t.vars),!1)}(r,t.context);if(null!=n){t.context=n;return}}else if(!g(r,t.localVars)){t.localVars=new z(r,t.localVars);return}}e.globalVars&&!g(r,t.globalVars)&&(t.globalVars=new z(r,t.globalVars))}function V(e){return"public"==e||"private"==e||"protected"==e||"abstract"==e||"readonly"==e}function A(e,r,t){this.prev=e,this.vars=r,this.block=t}function z(e,r){this.name=e,this.next=r}var T=new z("this",new z("arguments",null));function _(){w.state.context=new A(w.state.context,w.state.localVars,!1),w.state.localVars=T}function j(){w.state.context=new A(w.state.context,w.state.localVars,!0),w.state.localVars=null}function $(){w.state.localVars=w.state.context.vars,w.state.context=w.state.context.prev}function I(e,r){var t=function(){var t=w.state,n=t.indented;if("stat"==t.lexical.type)n=t.lexical.indented;else for(var a=t.lexical;a&&")"==a.type&&a.align;a=a.prev)n=a.indented;t.lexical=new v(n,w.stream.column(),e,null,t.lexical,r)};return t.lex=!0,t}function E(){var e=w.state;e.lexical.prev&&(")"==e.lexical.type&&(e.indented=e.lexical.indented),e.lexical=e.lexical.prev)}function O(e){return function r(t){return t==e?h():";"==e||"}"==t||")"==t||"]"==t?b():h(r)}}function q(e,r){if("var"==e)return h(I("vardef",r),ev,O(";"),E);if("keyword a"==e)return h(I("form"),S,q,E);if("keyword b"==e)return h(I("form"),q,E);if("keyword d"==e)return w.stream.match(/^\s*$/,!1)?h():h(I("stat"),W,O(";"),E);if("debugger"==e)return h(O(";"));if("{"==e)return h(I("}"),j,er,E,$);if(";"==e)return h();if("if"==e)return"else"==w.state.lexical.info&&w.state.cc[w.state.cc.length-1]==E&&w.state.cc.pop()(),h(I("form"),S,q,E,eV);if("function"==e)return h(e_);if("for"==e)return h(I("form"),j,eA,q,$,E);if("class"==e||o&&"interface"==r)return w.marked="keyword",h(I("form","class"==e?e:r),eO,E);if("variable"==e)if(o&&"declare"==r)return w.marked="keyword",h(q);else if(o&&("module"==r||"enum"==r||"type"==r)&&w.stream.match(/^\s*\w/,!1))return(w.marked="keyword","enum"==r)?h(eH):"type"==r?h(e$,O("operator"),eo,O(";")):h(I("form"),ew,O("{"),I("}"),er,E,E);else if(o&&"namespace"==r)return w.marked="keyword",h(I("form"),N,q,E);else if(o&&"abstract"==r)return w.marked="keyword",h(q);else return h(I("stat"),M);return"switch"==e?h(I("form"),S,O("{"),I("}","switch"),j,er,E,E,$):"case"==e?h(N,O(":")):"default"==e?h(O(":")):"catch"==e?h(I("form"),_,D,q,E,$):"export"==e?h(I("stat"),eC,E):"import"==e?h(I("stat"),eP,E):"async"==e?h(q):"@"==r?h(N,q):b(I("stat"),N,O(";"),E)}function D(e){if("("==e)return h(eI,O(")"))}function N(e,r){return P(e,r,!1)}function C(e,r){return P(e,r,!0)}function S(e){return"("!=e?b():h(I(")"),W,O(")"),E)}function P(e,r,t){if(w.state.fatArrowAt==w.stream.start){var n,a=t?J:H;if("("==e)return h(_,I(")"),Z(eI,")"),E,O("=>"),a,$);if("variable"==e)return b(_,ew,O("=>"),a,$)}var i=t?F:B;return y.hasOwnProperty(e)?h(i):"function"==e?h(e_,i):"class"==e||o&&"interface"==r?(w.marked="keyword",h(I("form"),eE,E)):"keyword c"==e||"async"==e?h(t?C:N):"("==e?h(I(")"),W,O(")"),E,i):"operator"==e||"spread"==e?h(t?C:N):"["==e?h(I("]"),eG,E,i):"{"==e?ee(R,"}",null,i):"quasi"==e?b(U,i):"new"==e?h((n=t,function(e){return"."==e?h(n?L:K):"variable"==e&&o?h(ep,n?F:B):b(n?C:N)})):h()}function W(e){return e.match(/[;\}\)\],]/)?b():b(N)}function B(e,r){return","==e?h(W):F(e,r,!1)}function F(e,r,t){var n=!1==t?B:F,a=!1==t?N:C;if("=>"==e)return h(_,t?J:H,$);if("operator"==e)return/\+\+|--/.test(r)||o&&"!"==r?h(n):o&&"<"==r&&w.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?h(I(">"),Z(eo,">"),E,n):"?"==r?h(N,O(":"),a):h(a);if("quasi"==e)return b(U,n);if(";"!=e){if("("==e)return ee(C,")","call",n);if("."==e)return h(Q,n);if("["==e)return h(I("]"),W,O("]"),E,n);if(o&&"as"==r)return w.marked="keyword",h(eo,n);if("regexp"==e)return w.state.lastType=w.marked="operator",w.stream.backUp(w.stream.pos-w.stream.start-1),h(a)}}function U(e,r){return"quasi"!=e?b():"${"!=r.slice(r.length-2)?h(U):h(W,G)}function G(e){if("}"==e)return w.marked="string.special",w.state.tokenize=p,h(U)}function H(e){return k(w.stream,w.state),b("{"==e?q:N)}function J(e){return k(w.stream,w.state),b("{"==e?q:C)}function K(e,r){if("target"==r)return w.marked="keyword",h(B)}function L(e,r){if("target"==r)return w.marked="keyword",h(F)}function M(e){return":"==e?h(E,q):b(B,O(";"),E)}function Q(e){if("variable"==e)return w.marked="property",h()}function R(e,r){if("async"==e)return w.marked="property",h(R);if("variable"==e||"keyword"==w.style){var t;return(w.marked="property","get"==r||"set"==r)?h(X):(o&&w.state.fatArrowAt==w.stream.start&&(t=w.stream.match(/^\s*:\s*/,!1))&&(w.state.fatArrowAt=w.stream.pos+t[0].length),h(Y))}if("number"==e||"string"==e)return w.marked=a?"property":w.style+" property",h(Y);if("jsonld-keyword"==e)return h(Y);if(o&&V(r))return w.marked="keyword",h(R);else if("["==e)return h(N,et,O("]"),Y);else if("spread"==e)return h(C,Y);else if("*"==r)return w.marked="keyword",h(R);else if(":"==e)return b(Y)}function X(e){return"variable"!=e?b(Y):(w.marked="property",h(e_))}function Y(e){return":"==e?h(C):"("==e?b(e_):void 0}function Z(e,r,t){function n(a,i){if(t?t.indexOf(a)>-1:","==a){var o=w.state.lexical;return"call"==o.info&&(o.pos=(o.pos||0)+1),h(function(t,n){return t==r||n==r?b():b(e)},n)}return a==r||i==r?h():t&&t.indexOf(";")>-1?b(e):h(O(r))}return function(t,a){return t==r||a==r?h():b(e,n)}}function ee(e,r,t){for(var n=3;n<arguments.length;n++)w.cc.push(arguments[n]);return h(I(r,t),Z(e,r),E)}function er(e){return"}"==e?h():b(q,er)}function et(e,r){if(o){if(":"==e)return h(eo);if("?"==r)return h(et)}}function en(e,r){if(o&&(":"==e||"in"==r))return h(eo)}function ea(e){if(o&&":"==e)if(w.stream.match(/^\s*\w+\s+is\b/,!1))return h(N,ei,eo);else return h(eo)}function ei(e,r){if("is"==r)return w.marked="keyword",h()}function eo(e,r){return"keyof"==r||"typeof"==r||"infer"==r||"readonly"==r?(w.marked="keyword",h("typeof"==r?C:eo)):"variable"==e||"void"==r?(w.marked="type",h(em)):"|"==r||"&"==r?h(eo):"string"==e||"number"==e||"atom"==e?h(em):"["==e?h(I("]"),Z(eo,"]",","),E,em):"{"==e?h(I("}"),es,E,em):"("==e?h(Z(ed,")"),eu,em):"<"==e?h(Z(eo,">"),eo):"quasi"==e?b(el,em):void 0}function eu(e){if("=>"==e)return h(eo)}function es(e){return e.match(/[\}\)\]]/)?h():","==e||";"==e?h(es):b(ef,es)}function ef(e,r){if("variable"==e||"keyword"==w.style)return w.marked="property",h(ef);if("?"==r||"number"==e||"string"==e)return h(ef);if(":"==e)return h(eo);if("["==e)return h(O("variable"),en,O("]"),ef);if("("==e)return b(ej,ef);else if(!e.match(/[;\}\)\],]/))return h()}function el(e,r){return"quasi"!=e?b():"${"!=r.slice(r.length-2)?h(el):h(eo,ec)}function ec(e){if("}"==e)return w.marked="string.special",w.state.tokenize=p,h(el)}function ed(e,r){return"variable"==e&&w.stream.match(/^\s*[?:]/,!1)||"?"==r?h(ed):":"==e?h(eo):"spread"==e?h(ed):b(eo)}function em(e,r){return"<"==r?h(I(">"),Z(eo,">"),E,em):"|"==r||"."==e||"&"==r?h(eo):"["==e?h(eo,O("]"),em):"extends"==r||"implements"==r?(w.marked="keyword",h(eo)):"?"==r?h(eo,O(":"),eo):void 0}function ep(e,r){if("<"==r)return h(I(">"),Z(eo,">"),E,em)}function ek(){return b(eo,ey)}function ey(e,r){if("="==r)return h(eo)}function ev(e,r){return"enum"==r?(w.marked="keyword",h(eH)):b(ew,et,eg,ex)}function ew(e,r){return o&&V(r)?(w.marked="keyword",h(ew)):"variable"==e?(x(r),h()):"spread"==e?h(ew):"["==e?ee(eh,"]"):"{"==e?ee(eb,"}"):void 0}function eb(e,r){return"variable"!=e||w.stream.match(/^\s*:/,!1)?("variable"==e&&(w.marked="property"),"spread"==e)?h(ew):"}"==e?b():"["==e?h(N,O("]"),O(":"),eb):h(O(":"),ew,eg):(x(r),h(eg))}function eh(){return b(ew,eg)}function eg(e,r){if("="==r)return h(C)}function ex(e){if(","==e)return h(ev)}function eV(e,r){if("keyword b"==e&&"else"==r)return h(I("form","else"),q,E)}function eA(e,r){return"await"==r?h(eA):"("==e?h(I(")"),ez,E):void 0}function ez(e){return"var"==e?h(ev,eT):"variable"==e?h(eT):b(eT)}function eT(e,r){return")"==e?h():";"==e?h(eT):"in"==r||"of"==r?(w.marked="keyword",h(N,eT)):b(N,eT)}function e_(e,r){return"*"==r?(w.marked="keyword",h(e_)):"variable"==e?(x(r),h(e_)):"("==e?h(_,I(")"),Z(eI,")"),E,ea,q,$):o&&"<"==r?h(I(">"),Z(ek,">"),E,e_):void 0}function ej(e,r){return"*"==r?(w.marked="keyword",h(ej)):"variable"==e?(x(r),h(ej)):"("==e?h(_,I(")"),Z(eI,")"),E,ea,$):o&&"<"==r?h(I(">"),Z(ek,">"),E,ej):void 0}function e$(e,r){return"keyword"==e||"variable"==e?(w.marked="type",h(e$)):"<"==r?h(I(">"),Z(ek,">"),E):void 0}function eI(e,r){return("@"==r&&h(N,eI),"spread"==e)?h(eI):o&&V(r)?(w.marked="keyword",h(eI)):o&&"this"==e?h(et,eg):b(ew,et,eg)}function eE(e,r){return"variable"==e?eO(e,r):eq(e,r)}function eO(e,r){if("variable"==e)return x(r),h(eq)}function eq(e,r){return"<"==r?h(I(">"),Z(ek,">"),E,eq):"extends"==r||"implements"==r||o&&","==e?("implements"==r&&(w.marked="keyword"),h(o?eo:N,eq)):"{"==e?h(I("}"),eD,E):void 0}function eD(e,r){return"async"==e||"variable"==e&&("static"==r||"get"==r||"set"==r||o&&V(r))&&w.stream.match(/^\s+#?[\w$\xa1-\uffff]/,!1)?(w.marked="keyword",h(eD)):"variable"==e||"keyword"==w.style?(w.marked="property",h(eN,eD)):"number"==e||"string"==e?h(eN,eD):"["==e?h(N,et,O("]"),eN,eD):"*"==r?(w.marked="keyword",h(eD)):o&&"("==e?b(ej,eD):";"==e||","==e?h(eD):"}"==e?h():"@"==r?h(N,eD):void 0}function eN(e,r){if("!"==r||"?"==r)return h(eN);if(":"==e)return h(eo,eg);if("="==r)return h(C);var t=w.state.lexical.prev;return b(t&&"interface"==t.info?ej:e_)}function eC(e,r){return"*"==r?(w.marked="keyword",h(eU,O(";"))):"default"==r?(w.marked="keyword",h(N,O(";"))):"{"==e?h(Z(eS,"}"),eU,O(";")):b(q)}function eS(e,r){return"as"==r?(w.marked="keyword",h(O("variable"))):"variable"==e?b(C,eS):void 0}function eP(e){return"string"==e?h():"("==e?b(N):"."==e?b(B):b(eW,eB,eU)}function eW(e,r){return"{"==e?ee(eW,"}"):("variable"==e&&x(r),"*"==r&&(w.marked="keyword"),h(eF))}function eB(e){if(","==e)return h(eW,eB)}function eF(e,r){if("as"==r)return w.marked="keyword",h(eW)}function eU(e,r){if("from"==r)return w.marked="keyword",h(N)}function eG(e){return"]"==e?h():b(Z(C,"]"))}function eH(){return b(I("form"),ew,O("{"),I("}"),Z(eJ,"}"),E,E)}function eJ(){return b(ew,eg)}return _.lex=j.lex=!0,$.lex=!0,E.lex=!0,{name:e.name,startState:function(r){var t={tokenize:d,lastType:"sof",cc:[],lexical:new v(-r,0,"block",!1),localVars:e.localVars,context:e.localVars&&new A(null,null,!1),indented:0};return e.globalVars&&"object"==typeof e.globalVars&&(t.globalVars=e.globalVars),t},token:function(e,n){if(e.sol()&&(n.lexical.hasOwnProperty("align")||(n.lexical.align=!1),n.indented=e.indentation(),k(e,n)),n.tokenize!=m&&e.eatSpace())return null;var a=n.tokenize(e,n);if("comment"==r)return a;n.lastType="operator"==r&&("++"==t||"--"==t)?"incdec":r;var o=r,u=t,s=n.cc;for(w.state=n,w.stream=e,w.marked=null,w.cc=s,w.style=a,n.lexical.hasOwnProperty("align")||(n.lexical.align=!0);;)if((s.length?s.pop():i?N:q)(o,u)){for(;s.length&&s[s.length-1].lex;)s.pop()();if(w.marked)return w.marked;if("variable"==o&&function(e,r){for(var t=e.localVars;t;t=t.next)if(t.name==r)return!0;for(var n=e.context;n;n=n.prev)for(var t=n.vars;t;t=t.next)if(t.name==r)return!0}(n,u))return"variableName.local";return a}},indent:function(r,t,a){if(r.tokenize==m||r.tokenize==p)return null;if(r.tokenize!=d)return 0;var i,o=t&&t.charAt(0),u=r.lexical;if(!/^\s*else\b/.test(t))for(var s=r.cc.length-1;s>=0;--s){var l=r.cc[s];if(l==E)u=u.prev;else if(l!=eV&&l!=$)break}for(;("stat"==u.type||"form"==u.type)&&("}"==o||(i=r.cc[r.cc.length-1])&&(i==B||i==F)&&!/^[,\.=+\-*:?[\(]/.test(t));)u=u.prev;n&&")"==u.type&&"stat"==u.prev.type&&(u=u.prev);var c=u.type,k=o==c;if("vardef"==c)return u.indented+("operator"==r.lastType||","==r.lastType?u.info.length+1:0);if("form"==c&&"{"==o)return u.indented;if("form"==c)return u.indented+a.unit;if("stat"==c)return u.indented+("operator"==r.lastType||","==r.lastType||f.test(t.charAt(0))||/[,.]/.test(t.charAt(0))?n||a.unit:0);if("switch"==u.info&&!k&&!1!=e.doubleIndentSwitch)return u.indented+(/^(?:case|default)\b/.test(t)?a.unit:2*a.unit);else if(u.align)return u.column+ +!k;else return u.indented+(k?0:a.unit)},languageData:{indentOnInput:/^\s*(?:case .*?:|default:|\{|\})$/,commentTokens:i?void 0:{line:"//",block:{open:"/*",close:"*/"}},closeBrackets:{brackets:["(","[","{","'",'"',"`"]},wordChars:"$"}}}t.r(r),t.d(r,{javascript:()=>a,json:()=>i,jsonld:()=>o,typescript:()=>u});let a=n({name:"javascript"}),i=n({name:"json",json:!0}),o=n({name:"json",jsonld:!0}),u=n({name:"typescript",typescript:!0})}}]);