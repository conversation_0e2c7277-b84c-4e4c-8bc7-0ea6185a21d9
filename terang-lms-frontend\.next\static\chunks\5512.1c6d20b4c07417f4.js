try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="1d618e5d-8686-40fc-9da8-76a5a54e9d09",e._sentryDebugIdIdentifier="sentry-dbid-1d618e5d-8686-40fc-9da8-76a5a54e9d09")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5512],{75512:(e,n,t)=>{function r(e){return RegExp("^(("+e.join(")|(")+"))\\b")}t.r(n),t.d(n,{octave:()=>g});var a=RegExp("^[\\+\\-\\*/&|\\^~<>!@'\\\\]"),i=RegExp("^[\\(\\[\\{\\},:=;\\.]"),o=RegExp("^((==)|(~=)|(<=)|(>=)|(<<)|(>>)|(\\.[\\+\\-\\*/\\^\\\\]))"),s=RegExp("^((!=)|(\\+=)|(\\-=)|(\\*=)|(/=)|(&=)|(\\|=)|(\\^=))"),c=RegExp("^((>>=)|(<<=))"),d=RegExp("^[\\]\\)]"),l=RegExp("^[_A-Za-z\xa1-￿][_A-Za-z0-9\xa1-￿]*"),u=r(["error","eval","function","abs","acos","atan","asin","cos","cosh","exp","log","prod","sum","log10","max","min","sign","sin","sinh","sqrt","tan","reshape","break","zeros","default","margin","round","ones","rand","syn","ceil","floor","size","clear","zeros","eye","mean","std","cov","det","eig","inv","norm","rank","trace","expm","logm","sqrtm","linspace","plot","title","xlabel","ylabel","legend","text","grid","meshgrid","mesh","num2str","fft","ifft","arrayfun","cellfun","input","fliplr","flipud","ismember"]),f=r(["return","case","switch","else","elseif","end","endif","endfunction","if","otherwise","do","for","while","try","catch","classdef","properties","events","methods","global","persistent","endfor","endwhile","printf","sprintf","disp","until","continue","pkg"]);function m(e,n){return e.sol()||"'"!==e.peek()?(n.tokenize=h,h(e,n)):(e.next(),n.tokenize=h,"operator")}function p(e,n){return e.match(/^.*%}/)?n.tokenize=h:e.skipToEnd(),"comment"}function h(e,n){if(e.eatSpace())return null;if(e.match("%{"))return n.tokenize=p,e.skipToEnd(),"comment";if(e.match(/^[%#]/))return e.skipToEnd(),"comment";if(e.match(/^[0-9\.+-]/,!1)){if(e.match(/^[+-]?0x[0-9a-fA-F]+[ij]?/))return e.tokenize=h,"number";if(e.match(/^[+-]?\d*\.\d+([EeDd][+-]?\d+)?[ij]?/)||e.match(/^[+-]?\d+([EeDd][+-]?\d+)?[ij]?/))return"number"}if(e.match(r(["nan","NaN","inf","Inf"])))return"number";var t=e.match(/^"(?:[^"]|"")*("|$)/)||e.match(/^'(?:[^']|'')*('|$)/);return t?t[1]?"string":"error":e.match(f)?"keyword":e.match(u)?"builtin":e.match(l)?"variable":e.match(a)||e.match(o)?"operator":e.match(i)||e.match(s)||e.match(c)?null:e.match(d)?(n.tokenize=m,null):(e.next(),"error")}let g={name:"octave",startState:function(){return{tokenize:h}},token:function(e,n){var t=n.tokenize(e,n);return("number"===t||"variable"===t)&&(n.tokenize=m),t},languageData:{commentTokens:{line:"%"}}}}}]);