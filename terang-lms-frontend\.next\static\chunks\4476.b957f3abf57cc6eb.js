try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="2592285f-0aa9-4520-af0a-6f1d8440a42d",e._sentryDebugIdIdentifier="sentry-dbid-2592285f-0aa9-4520-af0a-6f1d8440a42d")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4476],{4476:(e,t,n)=>{n.r(t),n.d(t,{xQuery:()=>y});var r=function(){function e(e){return{type:e,style:"keyword"}}for(var t=e("operator"),n={type:"atom",style:"atom"},r={type:"axis_specifier",style:"qualifier"},a={",":{type:"punctuation",style:null}},i=["after","all","allowing","ancestor","ancestor-or-self","any","array","as","ascending","at","attribute","base-uri","before","boundary-space","by","case","cast","castable","catch","child","collation","comment","construction","contains","content","context","copy","copy-namespaces","count","decimal-format","declare","default","delete","descendant","descendant-or-self","descending","diacritics","different","distance","document","document-node","element","else","empty","empty-sequence","encoding","end","entire","every","exactly","except","external","first","following","following-sibling","for","from","ftand","ftnot","ft-option","ftor","function","fuzzy","greatest","group","if","import","in","inherit","insensitive","insert","instance","intersect","into","invoke","is","item","language","last","lax","least","let","levels","lowercase","map","modify","module","most","namespace","next","no","node","nodes","no-inherit","no-preserve","not","occurs","of","only","option","order","ordered","ordering","paragraph","paragraphs","parent","phrase","preceding","preceding-sibling","preserve","previous","processing-instruction","relationship","rename","replace","return","revalidation","same","satisfies","schema","schema-attribute","schema-element","score","self","sensitive","sentence","sentences","sequence","skip","sliding","some","stable","start","stemming","stop","strict","strip","switch","text","then","thesaurus","times","to","transform","treat","try","tumbling","type","typeswitch","union","unordered","update","updating","uppercase","using","validate","value","variable","version","weight","when","where","wildcards","window","with","without","word","words","xquery"],s=0,o=i.length;s<o;s++)a[i[s]]=e(i[s]);for(var c=["xs:anyAtomicType","xs:anySimpleType","xs:anyType","xs:anyURI","xs:base64Binary","xs:boolean","xs:byte","xs:date","xs:dateTime","xs:dateTimeStamp","xs:dayTimeDuration","xs:decimal","xs:double","xs:duration","xs:ENTITIES","xs:ENTITY","xs:float","xs:gDay","xs:gMonth","xs:gMonthDay","xs:gYear","xs:gYearMonth","xs:hexBinary","xs:ID","xs:IDREF","xs:IDREFS","xs:int","xs:integer","xs:item","xs:java","xs:language","xs:long","xs:Name","xs:NCName","xs:negativeInteger","xs:NMTOKEN","xs:NMTOKENS","xs:nonNegativeInteger","xs:nonPositiveInteger","xs:normalizedString","xs:NOTATION","xs:numeric","xs:positiveInteger","xs:precisionDecimal","xs:QName","xs:short","xs:string","xs:time","xs:token","xs:unsignedByte","xs:unsignedInt","xs:unsignedLong","xs:unsignedShort","xs:untyped","xs:untypedAtomic","xs:yearMonthDuration"],s=0,o=c.length;s<o;s++)a[c[s]]=n;for(var l=["eq","ne","lt","le","gt","ge",":=","=",">",">=","<","<=",".","|","?","and","or","div","idiv","mod","*","/","+","-"],s=0,o=l.length;s<o;s++)a[l[s]]=t;for(var u=["self::","attribute::","child::","descendant::","descendant-or-self::","parent::","ancestor::","ancestor-or-self::","following::","preceding::","following-sibling::","preceding-sibling::"],s=0,o=u.length;s<o;s++)a[u[s]]=r;return a}();function a(e,t,n){return t.tokenize=n,n(e,t)}function i(e,t){var n=e.next(),p=!1,y='"'===(h=e).current()?h.match(/^[^\"]+\"\:/,!1):"'"===h.current()&&h.match(/^[^\"]+\'\:/,!1);if("<"==n){if(e.match("!--",!0))return a(e,t,u);if(e.match("![CDATA",!1))return t.tokenize=f,"tag";if(e.match("?",!1))return a(e,t,d);var h,b,k,v=e.eat("/");e.eatSpace();for(var w,z="";w=e.eat(/[^\s\u00a0=<>\"\'\/?]/);)z+=w;return a(e,t,(b=z,k=v,function(e,t){return(e.eatSpace(),k&&e.eat(">"))?(x(t),t.tokenize=i):(e.eat("/")||m(t,{type:"tag",name:b,tokenize:i}),e.eat(">")?t.tokenize=i:t.tokenize=l),"tag"}))}if("{"==n)return m(t,{type:"codeblock"}),null;if("}"==n)return x(t),null;if(g(t,"tag"))if(">"==n)return"tag";else if("/"==n&&e.eat(">"))return x(t),"tag";else return"variable";else{if(/\d/.test(n))return e.match(/^\d*(?:\.\d*)?(?:E[+\-]?\d+)?/),"atom";if("("===n&&e.eat(":"))return m(t,{type:"comment"}),a(e,t,s);if(!y&&('"'===n||"'"===n))return o(e,t,n);if("$"===n)return a(e,t,c);if(":"===n&&e.eat("="))return"keyword";if("("===n)return m(t,{type:"paren"}),null;if(")"===n)return x(t),null;if("["===n)return m(t,{type:"bracket"}),null;if("]"===n)return x(t),null;var I=r.propertyIsEnumerable(n)&&r[n];if(y&&'"'===n)for(;'"'!==e.next(););if(y&&"'"===n)for(;"'"!==e.next(););I||e.eatWhile(/[\w\$_-]/);var T=e.eat(":");!e.eat(":")&&T&&e.eatWhile(/[\w\$_-]/),e.match(/^[ \t]*\(/,!1)&&(p=!0);var _=e.current();return(I=r.propertyIsEnumerable(_)&&r[_],p&&!I&&(I={type:"function_call",style:"def"}),g(t,"xmlconstructor"))?(x(t),"variable"):(("element"==_||"attribute"==_||"axis_specifier"==I.type)&&m(t,{type:"xmlconstructor"}),I?I.style:"variable")}}function s(e,t){for(var n,r=!1,a=!1,i=0;n=e.next();){if(")"==n&&r)if(i>0)i--;else{x(t);break}else":"==n&&a&&i++;r=":"==n,a="("==n}return"comment"}function o(e,t,n,r){let s=function(e,t){for(var a;a=e.next();)if(a==n){x(t),r&&(t.tokenize=r);break}else if(e.match("{",!1)&&p(t)){m(t,{type:"codeblock"}),t.tokenize=i;break}return"string"};return m(t,{type:"string",name:n,tokenize:s}),a(e,t,s)}function c(e,t){var n=/[\w\$_-]/;if(e.eat('"')){for(;'"'!==e.next(););e.eat(":")}else e.eatWhile(n),e.match(":=",!1)||e.eat(":");return e.eatWhile(n),t.tokenize=i,"variable"}function l(e,t){var n=e.next();return"/"==n&&e.eat(">")?(p(t)&&x(t),g(t,"tag")&&x(t),"tag"):">"==n?(p(t)&&x(t),"tag"):"="==n?null:'"'==n||"'"==n?o(e,t,n,l):(p(t)||m(t,{type:"attribute",tokenize:l}),e.eat(/[a-zA-Z_:]/),e.eatWhile(/[-a-zA-Z0-9_:.]/),e.eatSpace(),(e.match(">",!1)||e.match("/",!1))&&(x(t),t.tokenize=i),"attribute")}function u(e,t){for(var n;n=e.next();)if("-"==n&&e.match("->",!0))return t.tokenize=i,"comment"}function f(e,t){for(var n;n=e.next();)if("]"==n&&e.match("]",!0))return t.tokenize=i,"comment"}function d(e,t){for(var n;n=e.next();)if("?"==n&&e.match(">",!0))return t.tokenize=i,"processingInstruction"}function p(e){return g(e,"attribute")}function g(e,t){return e.stack.length&&e.stack[e.stack.length-1].type==t}function m(e,t){e.stack.push(t)}function x(e){e.stack.pop();var t=e.stack.length&&e.stack[e.stack.length-1].tokenize;e.tokenize=t||i}let y={name:"xquery",startState:function(){return{tokenize:i,cc:[],stack:[]}},token:function(e,t){return e.eatSpace()?null:t.tokenize(e,t)},languageData:{commentTokens:{block:{open:"(:",close:":)"}}}}}}]);