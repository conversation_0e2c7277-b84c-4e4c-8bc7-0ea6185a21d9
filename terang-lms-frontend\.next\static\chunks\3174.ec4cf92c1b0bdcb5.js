try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1cbf2b61-522d-4cf9-9a05-688894d17a55",e._sentryDebugIdIdentifier="sentry-dbid-1cbf2b61-522d-4cf9-9a05-688894d17a55")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3174],{83174:(e,t,r)=>{function n(e){e?(t=/^(exx?|(ld|cp)([di]r?)?|[lp]ea|pop|push|ad[cd]|cpl|daa|dec|inc|neg|sbc|sub|and|bit|[cs]cf|x?or|res|set|r[lr]c?a?|r[lr]d|s[lr]a|srl|djnz|nop|[de]i|halt|im|in([di]mr?|ir?|irx|2r?)|ot(dmr?|[id]rx|imr?)|out(0?|[di]r?|[di]2r?)|tst(io)?|slp)(\.([sl]?i)?[sl])?\b/i,r=/^(((call|j[pr]|rst|ret[in]?)(\.([sl]?i)?[sl])?)|(rs|st)mix)\b/i):(t=/^(exx?|(ld|cp|in)([di]r?)?|pop|push|ad[cd]|cpl|daa|dec|inc|neg|sbc|sub|and|bit|[cs]cf|x?or|res|set|r[lr]c?a?|r[lr]d|s[lr]a|srl|djnz|nop|rst|[de]i|halt|im|ot[di]r|out[di]?)\b/i,r=/^(call|j[pr]|ret[in]?|b_?(call|jump))\b/i);var t,r,n=/^(af?|bc?|c|de?|e|hl?|l|i[xy]?|r|sp)\b/i,i=/^(n?[zc]|p[oe]?|m)\b/i,l=/^([hl][xy]|i[xy][hl]|slia|sll)\b/i,s=/^([\da-f]+h|[0-7]+o|[01]+b|\d+d?)\b/i;return{name:"z80",startState:function(){return{context:0}},token:function(a,d){var c;if(a.column()||(d.context=0),a.eatSpace())return null;if(a.eatWhile(/\w/)){if(e&&a.eat(".")&&a.eatWhile(/\w/),c=a.current(),a.indentation()){if((1==d.context||4==d.context)&&n.test(c))return d.context=4,"variable";if(2==d.context&&i.test(c))return d.context=4,"variableName.special";if(t.test(c))return d.context=1,"keyword";if(r.test(c))return d.context=2,"keyword";if(4==d.context&&s.test(c))return"number";if(l.test(c))return"error"}else if(a.match(s))return"number"}else if(a.eat(";"))return a.skipToEnd(),"comment";else if(a.eat('"')){for(;(c=a.next())&&'"'!=c;)"\\"==c&&a.next();return"string"}else if(a.eat("'")){if(a.match(/\\?.'/))return"number"}else if(a.eat(".")||a.sol()&&a.eat("#")){if(d.context=5,a.eatWhile(/\w/))return"def"}else if(a.eat("$")){if(a.eatWhile(/[\da-f]/i))return"number"}else if(a.eat("%")){if(a.eatWhile(/[01]/))return"number"}else a.next();return null}}}r.r(t),r.d(t,{ez80:()=>l,z80:()=>i});let i=n(!1),l=n(!0)}}]);