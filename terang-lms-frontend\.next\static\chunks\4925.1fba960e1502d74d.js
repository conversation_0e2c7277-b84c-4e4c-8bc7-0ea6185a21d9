try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="9b02a124-168a-4ae1-b7b3-880a8547f6d2",e._sentryDebugIdIdentifier="sentry-dbid-9b02a124-168a-4ae1-b7b3-880a8547f6d2")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4925],{84925:(e,t,n)=>{function r(e){for(var t={},n=e.split(" "),r=0;r<n.length;++r)t[n[r]]=!0;return t}n.r(t),n.d(t,{ttcn:()=>L});let i={name:"ttcn",keywords:r("activate address alive all alt altstep and and4b any break case component const continue control deactivate display do else encode enumerated except exception execute extends extension external for from function goto group if import in infinity inout interleave label language length log match message mixed mod modifies module modulepar mtc noblock not not4b nowait of on optional or or4b out override param pattern port procedure record recursive rem repeat return runs select self sender set signature system template testcase to type union value valueof var variant while with xor xor4b"),builtin:r("bit2hex bit2int bit2oct bit2str char2int char2oct encvalue decomp decvalue float2int float2str hex2bit hex2int hex2oct hex2str int2bit int2char int2float int2hex int2oct int2str int2unichar isbound ischosen ispresent isvalue lengthof log2str oct2bit oct2char oct2hex oct2int oct2str regexp replace rnd sizeof str2bit str2float str2hex str2int str2oct substr unichar2int unichar2char enum2int"),types:r("anytype bitstring boolean char charstring default float hexstring integer objid octetstring universal verdicttype timer"),timerOps:r("read running start stop timeout"),portOps:r("call catch check clear getcall getreply halt raise receive reply send trigger"),configOps:r("create connect disconnect done kill killed map unmap"),verdictOps:r("getverdict setverdict"),sutOps:r("action"),functionOps:r("apply derefers refers"),verdictConsts:r("error fail inconc none pass"),booleanConsts:r("true false"),otherConsts:r("null NULL omit"),visibilityModifiers:r("private public friend"),templateMatch:r("complement ifpresent subset superset permutation"),multiLineStrings:!0};var o,a=[];function s(e){if(e)for(var t in e)e.hasOwnProperty(t)&&a.push(t)}s(i.keywords),s(i.builtin),s(i.timerOps),s(i.portOps);var l=i.keywords||{},c=i.builtin||{},u=i.timerOps||{},p=i.portOps||{},d=i.configOps||{},f=i.verdictOps||{},m=i.sutOps||{},b=i.functionOps||{},y=i.verdictConsts||{},h=i.booleanConsts||{},g=i.otherConsts||{},v=i.types||{},x=i.visibilityModifiers||{},k=i.templateMatch||{},w=i.multiLineStrings,I=!1!==i.indentStatements,E=/[+\-*&@=<>!\/]/;function O(e,t){for(var n,r=!1;n=e.next();){if("/"==n&&r){t.tokenize=null;break}r="*"==n}return"comment"}function z(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function C(e,t,n){var r=e.indented;return e.context&&"statement"==e.context.type&&(r=e.context.indented),e.context=new z(r,t,n,null,e.context)}function _(e){var t=e.context.type;return(")"==t||"]"==t||"}"==t)&&(e.indented=e.context.indented),e.context=e.context.prev}let L={name:"ttcn",startState:function(){return{tokenize:null,context:new z(0,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,t){var n=t.context;if(e.sol()&&(null==n.align&&(n.align=!1),t.indented=e.indentation(),t.startOfLine=!0),e.eatSpace())return null;o=null;var r=(t.tokenize||function(e,t){var n,r=e.next();if('"'==r||"'"==r){return n=r,t.tokenize=function(e,t){for(var r,i=!1,o=!1;null!=(r=e.next());){if(r==n&&!i){var a=e.peek();a&&("b"==(a=a.toLowerCase())||"h"==a||"o"==a)&&e.next(),o=!0;break}i=!i&&"\\"==r}return(o||!(i||w))&&(t.tokenize=null),"string"},t.tokenize(e,t)}if(/[\[\]{}\(\),;\\:\?\.]/.test(r))return o=r,"punctuation";if("#"==r)return e.skipToEnd(),"atom";if("%"==r)return e.eatWhile(/\b/),"atom";if(/\d/.test(r))return e.eatWhile(/[\w\.]/),"number";if("/"==r){if(e.eat("*"))return t.tokenize=O,O(e,t);if(e.eat("/"))return e.skipToEnd(),"comment"}if(E.test(r))return"@"==r&&(e.match("try")||e.match("catch")||e.match("lazy"))?"keyword":(e.eatWhile(E),"operator");e.eatWhile(/[\w\$_\xa1-\uffff]/);var i=e.current();return l.propertyIsEnumerable(i)?"keyword":c.propertyIsEnumerable(i)?"builtin":u.propertyIsEnumerable(i)||d.propertyIsEnumerable(i)||f.propertyIsEnumerable(i)||p.propertyIsEnumerable(i)||m.propertyIsEnumerable(i)||b.propertyIsEnumerable(i)?"def":y.propertyIsEnumerable(i)||h.propertyIsEnumerable(i)||g.propertyIsEnumerable(i)?"string":v.propertyIsEnumerable(i)?"typeName.standard":x.propertyIsEnumerable(i)?"modifier":k.propertyIsEnumerable(i)?"atom":"variable"})(e,t);if("comment"==r)return r;if(null==n.align&&(n.align=!0),(";"==o||":"==o||","==o)&&"statement"==n.type)_(t);else if("{"==o)C(t,e.column(),"}");else if("["==o)C(t,e.column(),"]");else if("("==o)C(t,e.column(),")");else if("}"==o){for(;"statement"==n.type;)n=_(t);for("}"==n.type&&(n=_(t));"statement"==n.type;)n=_(t)}else o==n.type?_(t):I&&(("}"==n.type||"top"==n.type)&&";"!=o||"statement"==n.type&&"newstatement"==o)&&C(t,e.column(),"statement");return t.startOfLine=!1,r},languageData:{indentOnInput:/^\s*[{}]$/,commentTokens:{line:"//",block:{open:"/*",close:"*/"}},autocomplete:a}}}}]);