try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d3986ad7-d5f9-4857-a3f1-a4ffd182ddfc",e._sentryDebugIdIdentifier="sentry-dbid-d3986ad7-d5f9-4857-a3f1-a4ffd182ddfc")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[606],{45978:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(95155),n=r(12115),a=r(47886);function d(e){let{children:t}=e;return(0,n.useEffect)(()=>{(0,a.cl)("student")},[]),(0,s.jsx)(s.Fragment,{children:t})}},47886:(e,t,r)=>{"use strict";r.d(t,{WG:()=>n,cl:()=>d,qs:()=>s});let s={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==s.getUser(),hasRole:e=>{let t=s.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>s.hasRole("super_admin"),isTeacher:()=>s.hasRole("teacher"),isStudent:()=>s.hasRole("student")},n=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},a=()=>{let e=s.getUser();return e||(window.location.href="/auth/sign-in",null)},d=e=>{let t=a();return t?t.role!==e?(window.location.href=n(t),null):t:null}},80034:(e,t,r)=>{Promise.resolve().then(r.bind(r,45978))}},e=>{var t=t=>e(e.s=t);e.O(0,[4850,8441,3840,7358],()=>t(80034)),_N_E=e.O()}]);