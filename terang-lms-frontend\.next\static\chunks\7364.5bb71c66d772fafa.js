try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="7f65c2b7-8b15-42c4-a78a-fd2342fbe521",e._sentryDebugIdIdentifier="sentry-dbid-7f65c2b7-8b15-42c4-a78a-fd2342fbe521")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7364],{47364:(e,a,t)=>{t.r(a),t.d(a,{default:()=>D});var r=t(95155);t(12115);var n=t(18720),o=t(43909),s=t(33289),d=t(31561),l=t(70062),i=t(43042),c=t(9813),p=t(4556),f=t(62331),u=t(2066),y=t(4831),b=t(90140),g=t(2806),h=t(45757),j=t(16178),x=t(43066),m=t(33045),w=t(75481),k=t(37796),_=t(5498),E=t(97325),I=t(65259),S=t(61981),C=t(71937);function D(e){let{markdown:a,onChange:t,placeholder:D="Enter your content here...",className:v="min-h-[200px]"}=e;return(0,r.jsx)(o.R,{markdown:a,onChange:t,plugins:[(0,s.xO)(),(0,d.Zq)(),(0,l.G)(),(0,i.Y)(),(0,c.r)(),(0,p.O)(),(0,f.Mi)(),(0,u.Pz)({imageUploadHandler:async e=>{try{let a=await fetch("/api/upload?filename=".concat(e.name),{method:"POST",body:e});if(!a.ok)throw Error("Upload failed");return(await a.json()).url}catch(e){return console.error("Image upload failed:",e),n.oR.error("Failed to upload image"),""}}}),(0,y.c3)(),(0,b.oe)({defaultCodeBlockLanguage:"javascript"}),(0,g.sl)({codeBlockLanguages:{javascript:"JavaScript",typescript:"TypeScript",python:"Python",html:"HTML",css:"CSS",json:"JSON",markdown:"Markdown"}}),(0,h.F7)({toolbarContents:()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.l,{}),(0,r.jsx)(x.p,{}),(0,r.jsx)(m.S,{}),(0,r.jsx)(w._,{}),(0,r.jsx)(k.d,{}),(0,r.jsx)(_.N,{}),(0,r.jsx)(E.I,{}),(0,r.jsx)(I._,{}),(0,r.jsx)(S.a,{}),(0,r.jsx)(C.O,{})]})})],placeholder:D,className:v,"data-sentry-element":"MDXEditor","data-sentry-component":"MDXEditorWrapper","data-sentry-source-file":"mdx-editor-wrapper.tsx"})}}}]);