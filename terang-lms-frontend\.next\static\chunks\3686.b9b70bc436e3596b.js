try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="083a8272-0ecc-462b-b34d-9170c81a5814",e._sentryDebugIdIdentifier="sentry-dbid-083a8272-0ecc-462b-b34d-9170c81a5814")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3686],{33686:(e,t,n)=>{function r(e){for(var t={},n=0;n<e.length;++n)t[e[n]]=!0;return t}n.r(t),n.d(t,{r:()=>k});var a,i=["NULL","NA","Inf","NaN","NA_integer_","NA_real_","NA_complex_","NA_character_","TRUE","FALSE"],l=["list","quote","bquote","eval","return","call","parse","deparse"],c=["if","else","repeat","while","function","for","in","next","break"],o=r(i),u=r(l),s=r(c),f=r(["if","else","repeat","while","function","for"]),d=/[+\-*\/^<>=!&|~$:]/;function p(e,t){a=null;var n,r=e.next();if("#"==r)return e.skipToEnd(),"comment";if("0"==r&&e.eat("x"))return e.eatWhile(/[\da-f]/i),"number";if("."==r&&e.eat(/\d/))return e.match(/\d*(?:e[+\-]?\d+)?/),"number";if(/\d/.test(r))return e.match(/\d*(?:\.\d+)?(?:e[+\-]\d+)?L?/),"number";if("'"==r||'"'==r){return n=r,t.tokenize=function(e,t){if(e.eat("\\")){var r,a=e.next();return"x"==a?e.match(/^[a-f0-9]{2}/i):("u"==a||"U"==a)&&e.eat("{")&&e.skipTo("}")?e.next():"u"==a?e.match(/^[a-f0-9]{4}/i):"U"==a?e.match(/^[a-f0-9]{8}/i):/[0-7]/.test(a)&&e.match(/^[0-7]{1,2}/),"string.special"}for(;null!=(r=e.next());){if(r==n){t.tokenize=p;break}if("\\"==r){e.backUp(1);break}}return"string"},"string"}else if("`"==r)return e.match(/[^`]+`/),"string.special";else if("."==r&&e.match(/.(?:[.]|\d+)/))return"keyword";else if(/[a-zA-Z\.]/.test(r)){e.eatWhile(/[\w\.]/);var i=e.current();return o.propertyIsEnumerable(i)?"atom":s.propertyIsEnumerable(i)?(f.propertyIsEnumerable(i)&&!e.match(/\s*if(\s+|$)/,!1)&&(a="block"),"keyword"):u.propertyIsEnumerable(i)?"builtin":"variable"}else if("%"==r)return e.skipTo("%")&&e.next(),"variableName.special";else if("<"==r&&e.eat("-")||"<"==r&&e.match("<-")||"-"==r&&e.match(/>>?/))return"operator";else if("="==r&&t.ctx.argList)return"operator";else if(d.test(r))return"$"==r||e.eatWhile(d),"operator";else if(!/[\(\){}\[\];]/.test(r))return null;else return(a=r,";"==r)?"punctuation":null}function b(e,t,n){e.ctx={type:t,indent:e.indent,flags:0,column:n.column(),prev:e.ctx}}function m(e,t){var n=e.ctx;e.ctx={type:n.type,indent:n.indent,flags:n.flags|t,column:n.column,prev:n.prev}}function g(e){e.indent=e.ctx.indent,e.ctx=e.ctx.prev}let k={name:"r",startState:function(e){return{tokenize:p,ctx:{type:"top",indent:-e,flags:2},indent:0,afterIdent:!1}},token:function(e,t){if(e.sol()&&((3&t.ctx.flags)==0&&(t.ctx.flags|=2),4&t.ctx.flags&&g(t),t.indent=e.indentation()),e.eatSpace())return null;var n=t.tokenize(e,t);return"comment"!=n&&(2&t.ctx.flags)==0&&m(t,1),(";"==a||"{"==a||"}"==a)&&"block"==t.ctx.type&&g(t),"{"==a?b(t,"}",e):"("==a?(b(t,")",e),t.afterIdent&&(t.ctx.argList=!0)):"["==a?b(t,"]",e):"block"==a?b(t,"block",e):a==t.ctx.type?g(t):"block"==t.ctx.type&&"comment"!=n&&m(t,4),t.afterIdent="variable"==n||"keyword"==n,n},indent:function(e,t,n){if(e.tokenize!=p)return 0;var r=t&&t.charAt(0),a=e.ctx,i=r==a.type;return(4&a.flags&&(a=a.prev),"block"==a.type)?a.indent+("{"==r?0:n.unit):1&a.flags?a.column+ +!i:a.indent+(i?0:n.unit)},languageData:{wordChars:".",commentTokens:{line:"#"},autocomplete:i.concat(l,c)}}}}]);