try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b6e0e183-c2bb-49d2-a259-cea2bac917d1",e._sentryDebugIdIdentifier="sentry-dbid-b6e0e183-c2bb-49d2-a259-cea2bac917d1")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1475],{10489:(e,t,a)=>{"use strict";a.d(t,{b:()=>l});var s=a(12115),r=a(97602),n=a(95155),i=s.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},15894:(e,t,a)=>{"use strict";a.d(t,{d:()=>r});var s=a(18720);function r(){return{toast:e=>{let{title:t,description:a,variant:r="default"}=e;"destructive"===r?s.oR.error(t,{description:a}):s.oR.success(t,{description:a})}}}},20063:(e,t,a)=>{"use strict";var s=a(47260);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},20764:(e,t,a)=>{"use strict";a.d(t,{$:()=>d,r:()=>l});var s=a(95155);a(12115);var r=a(32467),n=a(83101),i=a(64269);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},25532:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>o,yv:()=>c});var s=a(95155);a(12115);var r=a(47887),n=a(24033),i=a(5917),l=a(12108),d=a(64269);function o(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u(e){let{className:t,size:a="default",children:i,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[i,(0,s.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,s.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)(x,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:a}),(0,s.jsx)(y,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function p(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(i.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,s.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:a})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function y(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,s.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},31936:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},35626:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35900:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(95155),r=a(12115),n=a(20063),i=a(66094),l=a(20764),d=a(31936),o=a(42526),c=a(25532),u=a(35626),m=a(70532),p=a(57828),x=a(46046),y=a(52619),f=a.n(y),h=a(15894);function g(){let e=(0,n.useRouter)(),{toast:t}=(0,h.d)(),[a,y]=(0,r.useState)(!1),[g,v]=(0,r.useState)(!1),[b,j]=(0,r.useState)([]),[w,S]=(0,r.useState)({name:"",email:"",password:"",role:"student",institutionId:"none"});(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/institutions"),t=await e.json();t.success&&j(t.data.institutions)}catch(e){console.error("Error fetching institutions:",e)}})()},[]);let N=async a=>{a.preventDefault(),y(!0);try{let a={...w,institutionId:w.institutionId&&"none"!==w.institutionId?parseInt(w.institutionId):null},s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),r=await s.json();r.success?(t({title:"Success",description:"User created successfully"}),e.push("/dashboard/admin/users")):t({title:"Error",description:r.error||"Failed to create user",variant:"destructive"})}catch(e){console.error("Error creating user:",e),t({title:"Error",description:"Failed to create user",variant:"destructive"})}finally{y(!1)}},C=(e,t)=>{S(a=>({...a,[e]:t}))};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewUserPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(f(),{href:"/dashboard/admin/users","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(l.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Add New User"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create a new user account on the platform"})]})]}),(0,s.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"User Details"}),(0,s.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter the basic information for the new user"})]}),(0,s.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Full Name"}),(0,s.jsx)(d.p,{id:"name",value:w.name,onChange:e=>C("name",e.target.value),placeholder:"Enter full name",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"email","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Email Address"}),(0,s.jsx)(d.p,{id:"email",type:"email",value:w.email,onChange:e=>C("email",e.target.value),placeholder:"Enter email address",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"password","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"password",type:g?"text":"password",value:w.password,onChange:e=>C("password",e.target.value),placeholder:"Enter password",required:!0,className:"pr-20","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center space-x-1 pr-2",children:(0,s.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>v(!g),className:"h-7 w-7 p-0","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:g?(0,s.jsx)(m.A,{className:"h-4 w-4"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})})]}),(0,s.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",t="";for(let a=0;a<12;a++)t+=e.charAt(Math.floor(Math.random()*e.length));C("password",t)},className:"mt-2","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Generate Password"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"role","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"User Role"}),(0,s.jsxs)(c.l6,{value:w.role,onValueChange:e=>C("role",e),required:!0,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(c.yv,{placeholder:"Select user role","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.eb,{value:"student","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Student"}),(0,s.jsx)(c.eb,{value:"teacher","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Teacher"}),(0,s.jsx)(c.eb,{value:"super_admin","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Super Admin"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,s.jsx)(o.J,{htmlFor:"institution","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution (Optional)"}),(0,s.jsxs)(c.l6,{value:w.institutionId,onValueChange:e=>C("institutionId",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(c.yv,{placeholder:"Select institution (optional)","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(c.eb,{value:"none","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"No Institution"}),b.map(e=>(0,s.jsx)(c.eb,{value:e.id.toString(),children:e.name},e.id))]})]})]})]}),(0,s.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(i.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Role Information"})}),(0,s.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:["student"===w.role&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Student Role:"}),(0,s.jsxs)("ul",{className:"mt-1 space-y-1 text-muted-foreground",children:[(0,s.jsx)("li",{children:"• Can enroll in courses and view course materials"}),(0,s.jsx)("li",{children:"• Can take quizzes and submit assignments"}),(0,s.jsx)("li",{children:"• Can view their progress and grades"}),(0,s.jsx)("li",{children:"• Limited to their assigned institution (if any)"})]})]}),"teacher"===w.role&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Teacher Role:"}),(0,s.jsxs)("ul",{className:"mt-1 space-y-1 text-muted-foreground",children:[(0,s.jsx)("li",{children:"• Can create and manage courses"}),(0,s.jsx)("li",{children:"• Can create quizzes and assignments"}),(0,s.jsx)("li",{children:"• Can view and grade student submissions"}),(0,s.jsx)("li",{children:"• Can manage students within their institution"})]})]}),"super_admin"===w.role&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Super Admin Role:"}),(0,s.jsxs)("ul",{className:"mt-1 space-y-1 text-muted-foreground",children:[(0,s.jsx)("li",{children:"• Full access to all platform features"}),(0,s.jsx)("li",{children:"• Can manage all institutions and users"}),(0,s.jsx)("li",{children:"• Can view billing and subscription information"}),(0,s.jsx)("li",{children:"• Can access system-wide analytics and reports"})]})]})]})})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)(f(),{href:"/dashboard/admin/users","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(l.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,s.jsxs)(l.$,{type:"submit",disabled:a,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),a?"Creating...":"Create User"]})]})]})})]})]})}},41145:(e,t,a)=>{Promise.resolve().then(a.bind(a,35900))},42526:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(95155);a(12115);var r=a(10489),n=a(64269);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},46046:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},57828:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},64269:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,z:()=>i});var s=a(2821),r=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){var t,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=s;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(r)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][i])?a:"Bytes")}},66094:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var s=a(95155);a(12115);var r=a(64269);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},70532:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(71847).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,9568,4850,8441,3840,7358],()=>t(41145)),_N_E=e.O()}]);