try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ca47ce37-5996-4618-9366-c612a3bbcce0",e._sentryDebugIdIdentifier="sentry-dbid-ca47ce37-5996-4618-9366-c612a3bbcce0")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1799],{3468:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>a});var n=r(12115),l=r(95155);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,o=n.useMemo(()=>a,Object.values(a));return(0,l.jsx)(r.Provider,{value:o,children:t})};return a.displayName=e+"Provider",[a,function(l){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return a.scopeName=e,[function(t,a){let o=n.createContext(a),i=r.length;r=[...r,a];let u=t=>{let{scope:r,children:a,...u}=t,s=r?.[e]?.[i]||o,c=n.useMemo(()=>u,Object.values(u));return(0,l.jsx)(s.Provider,{value:c,children:a})};return u.displayName=t+"Provider",[u,function(r,l){let u=l?.[e]?.[i]||o,s=n.useContext(u);if(s)return s;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(a,...t)]}},6132:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},9484:(e,t,r)=>{r.d(t,{C1:()=>w,bL:()=>x});var n=r(12115),l=r(3468),a=r(97602),o=r(95155),i="Progress",[u,s]=(0,l.A)(i),[c,d]=u(i),f=n.forwardRef((e,t)=>{var r,n,l,i;let{__scopeProgress:u,value:s=null,max:d,getValueLabel:f=v,...p}=e;(d||0===d)&&!g(d)&&console.error((r="".concat(d),n="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(n,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let m=g(d)?d:100;null===s||b(s,m)||console.error((l="".concat(s),i="Progress","Invalid prop `value` of value `".concat(l,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let x=b(s,m)?s:null,w=h(x)?f(x,m):void 0;return(0,o.jsx)(c,{scope:u,value:x,max:m,children:(0,o.jsx)(a.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":h(x)?x:void 0,"aria-valuetext":w,role:"progressbar","data-state":y(x,m),"data-value":null!=x?x:void 0,"data-max":m,...p,ref:t})})});f.displayName=i;var p="ProgressIndicator",m=n.forwardRef((e,t)=>{var r;let{__scopeProgress:n,...l}=e,i=d(p,n);return(0,o.jsx)(a.sG.div,{"data-state":y(i.value,i.max),"data-value":null!=(r=i.value)?r:void 0,"data-max":i.max,...l,ref:t})});function v(e,t){return"".concat(Math.round(e/t*100),"%")}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function g(e){return h(e)&&!isNaN(e)&&e>0}function b(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=p;var x=f,w=m},32467:(e,t,r)=>{r.d(t,{DX:()=>i,Dc:()=>s,TL:()=>o});var n=r(12115),l=r(94446),a=r(95155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var o;let e,i,u=(o=r,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let r={...t};for(let n in t){let l=e[n],a=t[n];/^on[A-Z]/.test(n)?l&&a?r[n]=(...e)=>{let t=a(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...a}:"className"===n&&(r[n]=[l,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,l.t)(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,i=n.Children.toArray(l),u=i.find(c);if(u){let e=u.props.children,l=i.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,a.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var i=o("Slot"),u=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},42529:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47937:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},52472:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},71847:(e,t,r)=>{r.d(t,{A:()=>u});var n=r(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:r,strokeWidth:u?24*Number(i)/Number(l):i,className:a("lucide",s),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:u,...s}=r;return(0,n.createElement)(i,{ref:o,iconNode:t,className:a("lucide-".concat(l(e)),u),...s})});return r.displayName="".concat(e),r}},83101:(e,t,r)=>{r.d(t,{F:()=>o});var n=r(2821);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,u=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let a=l(t)||l(n);return o[e][a]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,u,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...s}[t]):({...i,...s})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},85921:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},89715:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},94446:(e,t,r)=>{r.d(t,{s:()=>o,t:()=>a});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(a(...e),e)}},97602:(e,t,r)=>{r.d(t,{hO:()=>u,sG:()=>i});var n=r(12115),l=r(47650),a=r(32467),o=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(l?r:t,{...a,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function u(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},99708:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(71847).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])}}]);