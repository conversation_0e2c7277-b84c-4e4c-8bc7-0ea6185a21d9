try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="f3cff796-82cc-48cf-b227-da44879e22b3",e._sentryDebugIdIdentifier="sentry-dbid-f3cff796-82cc-48cf-b227-da44879e22b3")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5297],{5006:(e,s,a)=>{Promise.resolve().then(a.bind(a,21156))},12800:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>i,av:()=>c,j7:()=>o,tU:()=>l});var t=a(95155),r=a(12115),n=a(25667),d=a(64269);let l=n.bL,o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.B8,{ref:s,className:(0,d.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...r})});o.displayName=n.B8.displayName;let i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.l9,{ref:s,className:(0,d.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",a),...r})});i.displayName=n.l9.displayName;let c=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(n.UC,{ref:s,className:(0,d.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...r})});c.displayName=n.UC.displayName},21156:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(95155),r=a(66094),n=a(88021),d=a(26737),l=a(12800),o=a(26991),i=a(98128),c=a(68425),u=a(47734),m=a(73697),x=a(23508),f=a(16533),p=a(80019),y=a(47643),g=a(47937),h=a(26983),j=a(30814),v=a(52472),b=a(1524),N=a(42529);function C(){let e=[{week:"Week 1",hours:4,score:85},{week:"Week 2",hours:6,score:88},{week:"Week 3",hours:3,score:82},{week:"Week 4",hours:5,score:90},{week:"Week 5",hours:4,score:87},{week:"Week 6",hours:2,score:78}];return(0,t.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentProgressPage","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Progress"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Track your learning journey and achievements"})]})}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-6",children:[(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Courses"}),(0,t.jsx)(g.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"})]}),(0,t.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:3}),(0,t.jsxs)("p",{className:"text-muted-foreground text-xs",children:[1," completed"]})]})]}),(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Study Hours"}),(0,t.jsx)(h.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Clock","data-sentry-source-file":"page.tsx"})]}),(0,t.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:24}),(0,t.jsx)("p",{className:"text-muted-foreground text-xs",children:"Total hours"})]})]}),(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Average Score"}),(0,t.jsx)(j.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Target","data-sentry-source-file":"page.tsx"})]}),(0,t.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[82,"%"]}),(0,t.jsx)("p",{className:"text-muted-foreground text-xs",children:"Across all quizzes"})]})]}),(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Certificates"}),(0,t.jsx)(v.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Award","data-sentry-source-file":"page.tsx"})]}),(0,t.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:1}),(0,t.jsx)("p",{className:"text-muted-foreground text-xs",children:"Earned"})]})]}),(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Streak"}),(0,t.jsx)(b.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,t.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:7}),(0,t.jsx)("p",{className:"text-muted-foreground text-xs",children:"Days active"})]})]}),(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Completion"}),(0,t.jsx)(N.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"CheckCircle","data-sentry-source-file":"page.tsx"})]}),(0,t.jsxs)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[Math.round(33.33333333333333),"%"]}),(0,t.jsx)("p",{className:"text-muted-foreground text-xs",children:"Overall progress"})]})]})]}),(0,t.jsxs)(l.tU,{defaultValue:"courses",className:"space-y-6","data-sentry-element":"Tabs","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(l.j7,{"data-sentry-element":"TabsList","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(l.Xi,{value:"courses","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"Course Progress"}),(0,t.jsx)(l.Xi,{value:"analytics","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"Analytics"})]}),(0,t.jsx)(l.av,{value:"courses","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,t.jsx)("div",{className:"space-y-6",children:[{id:1,name:"Introduction to Algebra",progress:85,modules:[{name:"Module 1",progress:100,score:92},{name:"Module 2",progress:100,score:88},{name:"Module 3",progress:100,score:95},{name:"Module 4",progress:100,score:85},{name:"Module 5",progress:100,score:90},{name:"Module 6",progress:100,score:87},{name:"Module 7",progress:100,score:93},{name:"Module 8",progress:40,score:null}],quizzes:[{name:"Module 1 Quiz",score:92,maxScore:100,passed:!0},{name:"Module 2 Quiz",score:88,maxScore:100,passed:!0},{name:"Module 3 Quiz",score:95,maxScore:100,passed:!0},{name:"Module 4 Quiz",score:85,maxScore:100,passed:!0},{name:"Module 5 Quiz",score:90,maxScore:100,passed:!0},{name:"Module 6 Quiz",score:87,maxScore:100,passed:!0},{name:"Module 7 Quiz",score:93,maxScore:100,passed:!0}]},{id:2,name:"Physics Fundamentals",progress:45,modules:[{name:"Module 1",progress:100,score:78},{name:"Module 2",progress:100,score:82},{name:"Module 3",progress:100,score:75},{name:"Module 4",progress:100,score:88},{name:"Module 5",progress:100,score:80},{name:"Module 6",progress:60,score:null},{name:"Module 7",progress:0,score:null},{name:"Module 8",progress:0,score:null}],quizzes:[{name:"Module 1 Quiz",score:78,maxScore:100,passed:!0},{name:"Module 2 Quiz",score:82,maxScore:100,passed:!0},{name:"Module 3 Quiz",score:75,maxScore:100,passed:!0},{name:"Module 4 Quiz",score:88,maxScore:100,passed:!0},{name:"Module 5 Quiz",score:80,maxScore:100,passed:!0}]},{id:3,name:"Chemistry Basics",progress:100,modules:[{name:"Module 1",progress:100,score:95},{name:"Module 2",progress:100,score:92},{name:"Module 3",progress:100,score:98},{name:"Module 4",progress:100,score:94},{name:"Module 5",progress:100,score:96},{name:"Module 6",progress:100,score:93}],quizzes:[{name:"Module 1 Quiz",score:95,maxScore:100,passed:!0},{name:"Module 2 Quiz",score:92,maxScore:100,passed:!0},{name:"Module 3 Quiz",score:98,maxScore:100,passed:!0},{name:"Module 4 Quiz",score:94,maxScore:100,passed:!0},{name:"Module 5 Quiz",score:96,maxScore:100,passed:!0},{name:"Module 6 Quiz",score:93,maxScore:100,passed:!0},{name:"Final Exam",score:95,maxScore:100,passed:!0}]}].map(e=>(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(r.ZB,{children:e.name}),(0,t.jsxs)(r.BT,{children:[e.modules.filter(e=>100===e.progress).length," ","of ",e.modules.length," modules completed"]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[e.progress,"%"]}),(0,t.jsx)(n.E,{variant:100===e.progress?"default":"secondary",children:100===e.progress?"Completed":"In Progress"})]})]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(d.k,{value:e.progress,className:"h-3"}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-3 font-semibold",children:"Module Progress"}),(0,t.jsx)("div",{className:"space-y-2",children:e.modules.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.k,{value:e.progress,className:"h-2 w-16"}),(0,t.jsxs)("span",{className:"w-12 text-right",children:[e.progress,"%"]}),e.score&&(0,t.jsxs)(n.E,{variant:"outline",className:"text-xs",children:[e.score,"%"]})]})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"mb-3 font-semibold",children:"Quiz Results"}),(0,t.jsx)("div",{className:"space-y-2",children:e.quizzes.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{children:[e.score,"/",e.maxScore]}),(0,t.jsx)(n.E,{variant:e.passed?"default":"destructive",children:e.passed?"Passed":"Failed"})]})]},s))})]})]})]})})]},e.id))})}),(0,t.jsx)(l.av,{value:"analytics","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Weekly Study Hours"}),(0,t.jsx)(r.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your study time over the past 6 weeks"})]}),(0,t.jsx)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,t.jsx)(o.u,{width:"100%",height:300,"data-sentry-element":"ResponsiveContainer","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)(i.E,{data:e,"data-sentry-element":"BarChart","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(c.d,{strokeDasharray:"3 3","data-sentry-element":"CartesianGrid","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(u.W,{dataKey:"week","data-sentry-element":"XAxis","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(m.h,{"data-sentry-element":"YAxis","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(x.m,{"data-sentry-element":"Tooltip","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(f.y,{dataKey:"hours",fill:"#3b82f6","data-sentry-element":"Bar","data-sentry-source-file":"page.tsx"})]})})})]}),(0,t.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(r.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(r.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Score Trend"}),(0,t.jsx)(r.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your average quiz scores over time"})]}),(0,t.jsx)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,t.jsx)(o.u,{width:"100%",height:300,"data-sentry-element":"ResponsiveContainer","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)(p.b,{data:e,"data-sentry-element":"LineChart","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)(c.d,{strokeDasharray:"3 3","data-sentry-element":"CartesianGrid","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(u.W,{dataKey:"week","data-sentry-element":"XAxis","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(m.h,{domain:[70,100],"data-sentry-element":"YAxis","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(x.m,{"data-sentry-element":"Tooltip","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(y.N,{type:"monotone",dataKey:"score",stroke:"#10b981",strokeWidth:2,dot:{fill:"#10b981"},"data-sentry-element":"Line","data-sentry-source-file":"page.tsx"})]})})})]})]})})]})]})}},26737:(e,s,a)=>{"use strict";a.d(s,{k:()=>l});var t=a(95155),r=a(12115),n=a(9484),d=a(64269);let l=r.forwardRef((e,s)=>{let{className:a,value:r,...l}=e;return(0,t.jsx)(n.bL,{ref:s,className:(0,d.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...l,children:(0,t.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});l.displayName=n.bL.displayName},64269:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n,z:()=>d});var t=a(2821),r=a(75889);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,r.QP)((0,t.$)(s))}function d(e){var s,a;let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:n="normal"}=t;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(r)," ").concat("accurate"===n?null!=(s=["Bytes","KiB","MiB","GiB","TiB"][d])?s:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][d])?a:"Bytes")}},66094:(e,s,a)=>{"use strict";a.d(s,{BT:()=>o,Wu:()=>i,ZB:()=>l,Zp:()=>n,aR:()=>d,wL:()=>c});var t=a(95155);a(12115);var r=a(64269);function n(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function i(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:s,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},88021:(e,s,a)=>{"use strict";a.d(s,{E:()=>o});var t=a(95155);a(12115);var r=a(32467),n=a(83101),d=a(64269);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:s,variant:a,asChild:n=!1,...o}=e,i=n?r.DX:"span";return(0,t.jsx)(i,{"data-slot":"badge",className:(0,d.cn)(l({variant:a}),s),...o,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4909,4736,5667,5608,9334,1186,4850,8441,3840,7358],()=>s(5006)),_N_E=e.O()}]);