try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},T=(new e.Error).stack;T&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[T]="70bc3bf3-7d1c-4ef2-8f62-f954cdf0ce2d",e._sentryDebugIdIdentifier="sentry-dbid-70bc3bf3-7d1c-4ef2-8f62-f954cdf0ce2d")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9888],{59888:(e,T,O)=>{function E(e){for(var T={},O=e.split(" "),E=0;E<O.length;++E)T[O[E]]=!0;return T}O.r(T),O.d(T,{pig:()=>i});var t="ABS ACOS ARITY ASIN ATAN AVG BAGSIZE BINSTORAGE BLOOM BUILDBLOOM CBRT CEIL CONCAT COR COS COSH COUNT COUNT_STAR COV CONSTANTSIZE CUBEDIMENSIONS DIFF DISTINCT DOUBLEABS DOUBLEAVG DOUBLEBASE DOUBLEMAX DOUBLEMIN DOUBLEROUND DOUBLESUM EXP FLOOR FLOATABS FLOATAVG FLOATMAX FLOATMIN FLOATROUND FLOATSUM GENERICINVOKER INDEXOF INTABS INTAVG INTMAX INTMIN INTSUM INVOKEFORDOUBLE INVOKEFORFLOAT INVOKEFORINT INVOKEFORLONG INVOKEFORSTRING INVOKER ISEMPTY JSONLOADER JSONMETADATA JSONSTORAGE LAST_INDEX_OF LCFIRST LOG LOG10 LOWER LONGABS LONGAVG LONGMAX LONGMIN LONGSUM MAX MIN MAPSIZE MONITOREDUDF NONDETERMINISTIC OUTPUTSCHEMA  PIGSTORAGE PIGSTREAMING RANDOM REGEX_EXTRACT REGEX_EXTRACT_ALL REPLACE ROUND SIN SINH SIZE SQRT STRSPLIT SUBSTRING SUM STRINGCONCAT STRINGMAX STRINGMIN STRINGSIZE TAN TANH TOBAG TOKENIZE TOMAP TOP TOTUPLE TRIM TEXTLOADER TUPLESIZE UCFIRST UPPER UTF8STORAGECONVERTER ",I="VOID IMPORT RETURNS DEFINE LOAD FILTER FOREACH ORDER CUBE DISTINCT COGROUP JOIN CROSS UNION SPLIT INTO IF OTHERWISE ALL AS BY USING INNER OUTER ONSCHEMA PARALLEL PARTITION GROUP AND OR NOT GENERATE FLATTEN ASC DESC IS STREAM THROUGH STORE MAPREDUCE SHIP CACHE INPUT OUTPUT STDERROR STDIN STDOUT LIMIT SAMPLE LEFT RIGHT FULL EQ GT LT GTE LTE NEQ MATCHES TRUE FALSE DUMP",N="BOOLEAN INT LONG FLOAT DOUBLE CHARARRAY BYTEARRAY BAG TUPLE MAP ",r=E(t),A=E(I),R=E(N),n=/[*+\-%<>=&?:\/!|]/;function S(e,T,O){return T.tokenize=O,O(e,T)}function L(e,T){for(var O,E=!1;O=e.next();){if("/"==O&&E){T.tokenize=U;break}E="*"==O}return"comment"}function U(e,T){var O=e.next();if('"'==O||"'"==O)return S(e,T,function(e,T){for(var E,t=!1,I=!1;null!=(E=e.next());){if(E==O&&!t){I=!0;break}t=!t&&"\\"==E}return(I||!t)&&(T.tokenize=U),"error"});if(/[\[\]{}\(\),;\.]/.test(O))return null;if(/\d/.test(O))return e.eatWhile(/[\w\.]/),"number";if("/"==O)if(e.eat("*"))return S(e,T,L);else return e.eatWhile(n),"operator";if("-"==O)if(e.eat("-"))return e.skipToEnd(),"comment";else return e.eatWhile(n),"operator";else if(n.test(O))return e.eatWhile(n),"operator";else return(e.eatWhile(/[\w\$_]/),A&&A.propertyIsEnumerable(e.current().toUpperCase())&&!e.eat(")")&&!e.eat("."))?"keyword":r&&r.propertyIsEnumerable(e.current().toUpperCase())?"builtin":R&&R.propertyIsEnumerable(e.current().toUpperCase())?"type":"variable"}let i={name:"pig",startState:function(){return{tokenize:U,startOfLine:!0}},token:function(e,T){return e.eatSpace()?null:T.tokenize(e,T)},languageData:{autocomplete:(t+N+I).split(" ")}}}}]);