try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="ad734380-e28a-4897-87a1-596707035973",e._sentryDebugIdIdentifier="sentry-dbid-ad734380-e28a-4897-87a1-596707035973")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8623],{18623:(e,n,t)=>{t.r(n),t.d(n,{brainfuck:()=>i});var r="><+-.,[]".split("");let i={name:"brainfuck",startState:function(){return{commentLine:!1,left:0,right:0,commentLoop:!1}},token:function(e,n){if(e.eatSpace())return null;e.sol()&&(n.commentLine=!1);var t=e.next().toString();if(-1===r.indexOf(t))return n.commentLine=!0,e.eol()&&(n.commentLine=!1),"comment";if(!0===n.commentLine)return e.eol()&&(n.commentLine=!1),"comment";if("]"===t||"["===t)return"["===t?n.left++:n.right++,"bracket";if("+"===t||"-"===t)return"keyword";if("<"===t||">"===t)return"atom";if("."===t||","===t)return"def";e.eol()&&(n.commentLine=!1)}}}}]);