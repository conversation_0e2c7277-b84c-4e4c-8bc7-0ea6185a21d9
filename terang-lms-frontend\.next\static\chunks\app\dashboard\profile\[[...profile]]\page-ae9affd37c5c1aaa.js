try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="01de6c1f-e524-4188-beba-2c1ee714e825",e._sentryDebugIdIdentifier="sentry-dbid-01de6c1f-e524-4188-beba-2c1ee714e825")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3411],{3468:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,q:()=>s});var a=r(12115),n=r(95155);function s(e,t){let r=a.createContext(t),s=e=>{let{children:t,...s}=e,i=a.useMemo(()=>s,Object.values(s));return(0,n.jsx)(r.Provider,{value:i,children:t})};return s.displayName=e+"Provider",[s,function(n){let s=a.useContext(r);if(s)return s;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],s=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return s.scopeName=e,[function(t,s){let i=a.createContext(s),l=r.length;r=[...r,s];let o=t=>{let{scope:r,children:s,...o}=t,d=r?.[e]?.[l]||i,u=a.useMemo(()=>o,Object.values(o));return(0,n.jsx)(d.Provider,{value:u,children:s})};return o.displayName=t+"Provider",[o,function(r,n){let o=n?.[e]?.[l]||i,d=a.useContext(o);if(d)return d;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:a})=>{let n=r(e)[`__scope${a}`];return{...t,...n}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(s,...t)]}},4129:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var a=r(12115),n=globalThis?.document?a.useLayoutEffect:()=>{}},8910:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(95155),n=r(61289),s=r(20764),i=r(66094),l=r(12547);function o(){let{user:e,loading:t,signOut:r}=(0,n.A)();return t?(0,a.jsx)("div",{children:"Loading..."}):e?(0,a.jsxs)("div",{className:"space-y-6 p-4 sm:p-6 lg:p-8","data-sentry-component":"ProfileViewPage","data-sentry-source-file":"profile-view-page.tsx",children:[(0,a.jsx)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"profile-view-page.tsx",children:(0,a.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"profile-view-page.tsx",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 text-center sm:flex-row sm:text-left",children:[(0,a.jsxs)(l.Avatar,{className:"h-24 w-24 text-3xl","data-sentry-element":"Avatar","data-sentry-source-file":"profile-view-page.tsx",children:[(0,a.jsx)(l.AvatarImage,{src:"https://ui-avatars.com/api/?name=".concat(e.name,"&background=random"),"data-sentry-element":"AvatarImage","data-sentry-source-file":"profile-view-page.tsx"}),(0,a.jsx)(l.AvatarFallback,{"data-sentry-element":"AvatarFallback","data-sentry-source-file":"profile-view-page.tsx",children:e.name.charAt(0)})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(i.ZB,{className:"text-2xl","data-sentry-element":"CardTitle","data-sentry-source-file":"profile-view-page.tsx",children:e.name}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.email})]})]})})}),(0,a.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"profile-view-page.tsx",children:[(0,a.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"profile-view-page.tsx",children:(0,a.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"profile-view-page.tsx",children:"Account Details"})}),(0,a.jsxs)(i.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"profile-view-page.tsx",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Full Name"}),(0,a.jsx)("p",{children:e.name})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Email address"}),(0,a.jsx)("p",{children:e.email})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Role"}),(0,a.jsx)("p",{className:"capitalize",children:e.role.replace("_"," ")})]}),e.institutionId&&(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Institution ID"}),(0,a.jsx)("p",{children:e.institutionId})]})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4",children:(0,a.jsx)(s.$,{onClick:r,variant:"destructive","data-sentry-element":"Button","data-sentry-source-file":"profile-view-page.tsx",children:"Sign Out"})})]})]})]}):(0,a.jsx)("div",{children:"Please sign in to view your profile."})}},12547:(e,t,r)=>{"use strict";r.d(t,{Avatar:()=>i,AvatarFallback:()=>o,AvatarImage:()=>l});var a=r(95155);r(12115);var n=r(46591),s=r(64269);function i(e){let{className:t,...r}=e;return(0,a.jsx)(n.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)(n._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",t),...r,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...r,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}},14806:(e,t,r)=>{"use strict";e.exports=r(30125)},20063:(e,t,r)=>{"use strict";var a=r(47260);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},20764:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>l});var a=r(95155);r(12115);var n=r(32467),s=r(83101),i=r(64269);let l=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:s,asChild:o=!1,...d}=e,u=o?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:s,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},30125:(e,t,r)=>{"use strict";var a=r(12115),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=a.useState,i=a.useEffect,l=a.useLayoutEffect,o=a.useDebugValue;function d(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),a=s({inst:{value:r,getSnapshot:t}}),n=a[0].inst,u=a[1];return l(function(){n.value=r,n.getSnapshot=t,d(n)&&u({inst:n})},[e,r,t]),i(function(){return d(n)&&u({inst:n}),e(function(){d(n)&&u({inst:n})})},[e]),o(r),r};t.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:u},32467:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>i});var a=r(12115),n=r(94446),s=r(95155);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(d.ref=t?(0,n.t)(t,o):o),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...i}=e,l=a.Children.toArray(n),o=l.find(u);if(o){let e=o.props.children,n=l.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function u(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},46591:(e,t,r)=>{"use strict";r.d(t,{H4:()=>S,_V:()=>N,bL:()=>j});var a=r(12115),n=r(3468),s=r(70222),i=r(4129),l=r(97602),o=r(14806);function d(){return()=>{}}var u=r(95155),c="Avatar",[f,m]=(0,n.A)(c),[v,p]=f(c),g=a.forwardRef((e,t)=>{let{__scopeAvatar:r,...n}=e,[s,i]=a.useState("idle");return(0,u.jsx)(v,{scope:r,imageLoadingStatus:s,onImageLoadingStatusChange:i,children:(0,u.jsx)(l.sG.span,{...n,ref:t})})});g.displayName=c;var x="AvatarImage",h=a.forwardRef((e,t)=>{let{__scopeAvatar:r,src:n,onLoadingStatusChange:c=()=>{},...f}=e,m=p(x,r),v=function(e,t){let{referrerPolicy:r,crossOrigin:n}=t,s=(0,o.useSyncExternalStore)(d,()=>!0,()=>!1),l=a.useRef(null),u=s?(l.current||(l.current=new window.Image),l.current):null,[c,f]=a.useState(()=>w(u,e));return(0,i.N)(()=>{f(w(u,e))},[u,e]),(0,i.N)(()=>{let e=e=>()=>{f(e)};if(!u)return;let t=e("loaded"),a=e("error");return u.addEventListener("load",t),u.addEventListener("error",a),r&&(u.referrerPolicy=r),"string"==typeof n&&(u.crossOrigin=n),()=>{u.removeEventListener("load",t),u.removeEventListener("error",a)}},[u,n,r]),c}(n,f),g=(0,s.c)(e=>{c(e),m.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==v&&g(v)},[v,g]),"loaded"===v?(0,u.jsx)(l.sG.img,{...f,ref:t,src:n}):null});h.displayName=x;var y="AvatarFallback",b=a.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:n,...s}=e,i=p(y,r),[o,d]=a.useState(void 0===n);return a.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>d(!0),n);return()=>window.clearTimeout(e)}},[n]),o&&"loaded"!==i.imageLoadingStatus?(0,u.jsx)(l.sG.span,{...s,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=y;var j=g,N=h,S=b},47886:(e,t,r)=>{"use strict";r.d(t,{WG:()=>n,cl:()=>i,qs:()=>a});let a={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},n=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},s=()=>{let e=a.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let t=s();return t?t.role!==e?(window.location.href=n(t),null):t:null}},58326:(e,t,r)=>{Promise.resolve().then(r.bind(r,8910))},61289:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(12115),n=r(47886),s=r(20063);function i(){let[e,t]=(0,a.useState)(null),[r,i]=(0,a.useState)(!0),l=(0,s.useRouter)();return(0,a.useEffect)(()=>{let e=n.qs.getUser();e&&t(e),i(!1)},[]),{user:e,loading:r,signOut:()=>{n.qs.removeUser(),t(null),l.push("/auth/sign-in")}}}},64269:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s,z:()=>i});var a=r(2821),n=r(75889);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function i(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:n=0,sizeType:s="normal"}=a;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(n)," ").concat("accurate"===s?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][i])?r:"Bytes")}},66094:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>i,wL:()=>u});var a=r(95155);r(12115);var n=r(64269);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function u(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},70222:(e,t,r)=>{"use strict";r.d(t,{c:()=>n});var a=r(12115);function n(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var a=r(2821);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,i=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let s=n(t)||n(a);return i[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,o,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},94446:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>s});var a=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return a.useCallback(s(...e),e)}},97602:(e,t,r)=>{"use strict";r.d(t,{hO:()=>o,sG:()=>l});var a=r(12115),n=r(47650),s=r(32467),i=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...s,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function o(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,4850,8441,3840,7358],()=>t(58326)),_N_E=e.O()}]);