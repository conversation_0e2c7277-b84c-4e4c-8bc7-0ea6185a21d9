try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d9317285-a38d-424a-a73a-bb38f7553d23",e._sentryDebugIdIdentifier="sentry-dbid-d9317285-a38d-424a-a73a-bb38f7553d23")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5754],{3407:(e,t,n)=>{"use strict";n.d(t,{BM:()=>a,CW:()=>r,Ee:()=>f,HP:()=>c,JQ:()=>o,Ny:()=>h,On:()=>p,cx:()=>l,es:()=>d,lV:()=>i,ok:()=>u,ol:()=>s});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),l=m(/[#-'*+\--9=?A-Z^-~]/);function o(e){return null!==e&&(e<32||127===e)}let a=m(/\d/),u=m(/[\dA-Fa-f]/),s=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function p(e){return -2===e||-1===e||32===e}let d=m(/\p{P}|\p{S}/u),h=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},3922:(e,t,n)=>{"use strict";n.d(t,{K:()=>o});var r=n(17032),i=n(23362),l=n(41006);function o(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let l=this.previous,o=this.events,a=0;return function(u){return 126===l&&"characterEscape"!==o[o.length-1][1].type?r(u):(e.enter("strikethroughSequenceTemporary"),function o(u){let s=(0,i.S)(l);if(126===u)return a>1?r(u):(e.consume(u),a++,o);if(a<2&&!t)return r(u);let c=e.exit("strikethroughSequenceTemporary"),f=(0,i.S)(u);return c._open=!f||2===f&&!!s,c._close=!s||2===s&&!!f,n(u)}(u))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let i=n;for(;i--;)if("exit"===e[i][0]&&"strikethroughSequenceTemporary"===e[i][1].type&&e[i][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[i][1].end.offset-e[i][1].start.offset){e[n][1].type="strikethroughSequence",e[i][1].type="strikethroughSequence";let o={type:"strikethrough",start:Object.assign({},e[i][1].start),end:Object.assign({},e[n][1].end)},a={type:"strikethroughText",start:Object.assign({},e[i][1].end),end:Object.assign({},e[n][1].start)},u=[["enter",o,t],["enter",e[i][1],t],["exit",e[i][1],t],["enter",a,t]],s=t.parser.constructs.insideSpan.null;s&&(0,r.m)(u,u.length,0,(0,l.W)(s,e.slice(i+1,n),t)),(0,r.m)(u,u.length,0,[["exit",a,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",o,t]]),(0,r.m)(e,i-1,n-i+3,u),n=i+u.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}},5009:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{D:()=>r})},7053:(e,t,n)=>{"use strict";n.d(t,{oz:()=>e7});var r={};n.r(r),n.d(r,{boolean:()=>d,booleanish:()=>h,commaOrSpaceSeparated:()=>v,commaSeparated:()=>k,number:()=>g,overloadedBoolean:()=>m,spaceSeparated:()=>y});var i=n(63758),l=n(34091);let o=/[ \t\n\f\r]/g;function a(e){return""===e.replace(o,"")}class u{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function s(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new u(n,r,t)}function c(e){return e.toLowerCase()}u.prototype.normal={},u.prototype.property={},u.prototype.space=void 0;class f{constructor(e,t){this.attribute=t,this.property=e}}f.prototype.attribute="",f.prototype.booleanish=!1,f.prototype.boolean=!1,f.prototype.commaOrSpaceSeparated=!1,f.prototype.commaSeparated=!1,f.prototype.defined=!1,f.prototype.mustUseProperty=!1,f.prototype.number=!1,f.prototype.overloadedBoolean=!1,f.prototype.property="",f.prototype.spaceSeparated=!1,f.prototype.space=void 0;let p=0,d=x(),h=x(),m=x(),g=x(),y=x(),k=x(),v=x();function x(){return 2**++p}let b=Object.keys(r);class w extends f{constructor(e,t,n,i){let l=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++l<b.length;){let e=b[l];!function(e,t,n){n&&(e[t]=n)}(this,b[l],(n&r[e])===r[e])}}}function S(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let l=new w(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[c(r)]=r,n[c(l.attribute)]=r}return new u(t,n,e.space)}w.prototype.defined=!0;let C=S({properties:{ariaActiveDescendant:null,ariaAtomic:h,ariaAutoComplete:null,ariaBusy:h,ariaChecked:h,ariaColCount:g,ariaColIndex:g,ariaColSpan:g,ariaControls:y,ariaCurrent:null,ariaDescribedBy:y,ariaDetails:null,ariaDisabled:h,ariaDropEffect:y,ariaErrorMessage:null,ariaExpanded:h,ariaFlowTo:y,ariaGrabbed:h,ariaHasPopup:null,ariaHidden:h,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:y,ariaLevel:g,ariaLive:null,ariaModal:h,ariaMultiLine:h,ariaMultiSelectable:h,ariaOrientation:null,ariaOwns:y,ariaPlaceholder:null,ariaPosInSet:g,ariaPressed:h,ariaReadOnly:h,ariaRelevant:null,ariaRequired:h,ariaRoleDescription:y,ariaRowCount:g,ariaRowIndex:g,ariaRowSpan:g,ariaSelected:h,ariaSetSize:g,ariaSort:null,ariaValueMax:g,ariaValueMin:g,ariaValueNow:g,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function E(e,t){return t in e?e[t]:t}function P(e,t){return E(e,t.toLowerCase())}let I=S({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:k,acceptCharset:y,accessKey:y,action:null,allow:null,allowFullScreen:d,allowPaymentRequest:d,allowUserMedia:d,alt:null,as:null,async:d,autoCapitalize:null,autoComplete:y,autoFocus:d,autoPlay:d,blocking:y,capture:null,charSet:null,checked:d,cite:null,className:y,cols:g,colSpan:null,content:null,contentEditable:h,controls:d,controlsList:y,coords:g|k,crossOrigin:null,data:null,dateTime:null,decoding:null,default:d,defer:d,dir:null,dirName:null,disabled:d,download:m,draggable:h,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:d,formTarget:null,headers:y,height:g,hidden:m,high:g,href:null,hrefLang:null,htmlFor:y,httpEquiv:y,id:null,imageSizes:null,imageSrcSet:null,inert:d,inputMode:null,integrity:null,is:null,isMap:d,itemId:null,itemProp:y,itemRef:y,itemScope:d,itemType:y,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:d,low:g,manifest:null,max:null,maxLength:g,media:null,method:null,min:null,minLength:g,multiple:d,muted:d,name:null,nonce:null,noModule:d,noValidate:d,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:d,optimum:g,pattern:null,ping:y,placeholder:null,playsInline:d,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:d,referrerPolicy:null,rel:y,required:d,reversed:d,rows:g,rowSpan:g,sandbox:y,scope:null,scoped:d,seamless:d,selected:d,shadowRootClonable:d,shadowRootDelegatesFocus:d,shadowRootMode:null,shape:null,size:g,sizes:null,slot:null,span:g,spellCheck:h,src:null,srcDoc:null,srcLang:null,srcSet:null,start:g,step:null,style:null,tabIndex:g,target:null,title:null,translate:null,type:null,typeMustMatch:d,useMap:null,value:h,width:g,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:y,axis:null,background:null,bgColor:null,border:g,borderColor:null,bottomMargin:g,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:d,declare:d,event:null,face:null,frame:null,frameBorder:null,hSpace:g,leftMargin:g,link:null,longDesc:null,lowSrc:null,marginHeight:g,marginWidth:g,noResize:d,noHref:d,noShade:d,noWrap:d,object:null,profile:null,prompt:null,rev:null,rightMargin:g,rules:null,scheme:null,scrolling:h,standby:null,summary:null,text:null,topMargin:g,valueType:null,version:null,vAlign:null,vLink:null,vSpace:g,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:d,disableRemotePlayback:d,prefix:null,property:null,results:g,security:null,unselectable:null},space:"html",transform:P}),T=S({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:v,accentHeight:g,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:g,amplitude:g,arabicForm:null,ascent:g,attributeName:null,attributeType:null,azimuth:g,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:g,by:null,calcMode:null,capHeight:g,className:y,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:g,diffuseConstant:g,direction:null,display:null,dur:null,divisor:g,dominantBaseline:null,download:d,dx:null,dy:null,edgeMode:null,editable:null,elevation:g,enableBackground:null,end:null,event:null,exponent:g,externalResourcesRequired:null,fill:null,fillOpacity:g,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:k,g2:k,glyphName:k,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:g,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:g,horizOriginX:g,horizOriginY:g,id:null,ideographic:g,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:g,k:g,k1:g,k2:g,k3:g,k4:g,kernelMatrix:v,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:g,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:g,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:g,overlineThickness:g,paintOrder:null,panose1:null,path:null,pathLength:g,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:y,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:g,pointsAtY:g,pointsAtZ:g,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:v,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:v,rev:v,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:v,requiredFeatures:v,requiredFonts:v,requiredFormats:v,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:g,specularExponent:g,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:g,strikethroughThickness:g,string:null,stroke:null,strokeDashArray:v,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:g,strokeOpacity:g,strokeWidth:null,style:null,surfaceScale:g,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:v,tabIndex:g,tableValues:null,target:null,targetX:g,targetY:g,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:v,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:g,underlineThickness:g,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:g,values:null,vAlphabetic:g,vMathematical:g,vectorEffect:null,vHanging:g,vIdeographic:g,version:null,vertAdvY:g,vertOriginX:g,vertOriginY:g,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:g,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:E}),A=S({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),D=S({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:P}),O=S({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),L=s([C,I,A,D,O],"html"),F=s([C,T,A,D,O],"svg"),M=/[A-Z]/g,z=/-[a-z]/g,N=/^data[-\w.:]+$/i;function R(e){return"-"+e.toLowerCase()}function _(e){return e.charAt(1).toUpperCase()}let H={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var j=n(54185);let B=U("end"),V=U("start");function U(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}var q=n(18283);let W={}.hasOwnProperty,$=new Map,Y=/[A-Z]/g,Q=new Set(["table","tbody","thead","tfoot","tr"]),K=new Set(["td","th"]),J="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function X(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=F),e.ancestors.push(t);let l=et(e,t.tagName,!1),o=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&W.call(t.properties,r)){let l=function(e,t,n){let r=function(e,t){let n=c(t),r=t,i=f;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&N.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(z,_);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!z.test(e)){let n=e.replace(M,R);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=w}return new i(r,t)}(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?function(e,t){let n={};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}(n):n.join(" ").trim()),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return j(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new q.o("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=J+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)W.call(e,t)&&(n[function(e){let t=e.replace(Y,er);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?H[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(l){let[r,o]=l;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&K.has(t.tagName)?n=o:i[r]=o}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),u=ee(e,t);return Q.has(t.tagName)&&(u=u.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&a(e.value):a(e))})),Z(e,o,l,t),G(o,u),e.ancestors.pop(),e.schema=r,e.create(t,l,o,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,i.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}en(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.name&&"html"===r.space&&(e.schema=F),e.ancestors.push(t);let o=null===t.name?e.Fragment:et(e,t.name,!0),a=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type);let l=t.expression;(0,i.ok)("ObjectExpression"===l.type);let o=l.properties[0];(0,i.ok)("SpreadElement"===o.type),Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else en(e,t.position);else{let l,o=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type),l=e.evaluater.evaluateExpression(t.expression)}else en(e,t.position);else l=null===r.value||r.value;n[o]=l}return n}(e,t),u=ee(e,t);return Z(e,a,o,t),G(a,u),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);en(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return G(r,ee(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function Z(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function G(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function ee(e,t){let n=[],r=-1,i=e.passKeys?new Map:$;for(;++r<t.children.length;){let l,o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=i.get(e)||0;l=e+"-"+t,i.set(e,t+1)}}let a=X(e,o,l);void 0!==a&&n.push(a)}return n}function et(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),o=-1;for(;++o<n.length;){let t=(0,l.UU)(n[o])?{type:"Identifier",name:n[o]}:{type:"Literal",value:n[o]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(o&&"Literal"===t.type),optional:!1}:t}(0,i.ok)(e,"always a result"),r=e}else r=(0,l.UU)(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return W.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);en(e)}function en(e,t){let n=new q.o("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=J+"#cannot-handle-mdx-estrees-without-createevaluater",n}function er(e){return"-"+e.toLowerCase()}let ei={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var el=n(95155);n(12115);var eo=n(97853);function ea(e){let t=this;t.parser=function(n){return(0,eo.Y)(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}let eu="object"==typeof self?self:globalThis,es=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=i=>{if(e.has(i))return e.get(i);let[l,o]=t[i];switch(l){case 0:case -1:return n(o,i);case 1:{let e=n([],i);for(let t of o)e.push(r(t));return e}case 2:{let e=n({},i);for(let[t,n]of o)e[r(t)]=r(n);return e}case 3:return n(new Date(o),i);case 4:{let{source:e,flags:t}=o;return n(new RegExp(e,t),i)}case 5:{let e=n(new Map,i);for(let[t,n]of o)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,i);for(let t of o)e.add(r(t));return e}case 7:{let{name:e,message:t}=o;return n(new eu[e](t),i)}case 8:return n(BigInt(o),i);case"BigInt":return n(Object(BigInt(o)),i);case"ArrayBuffer":return n(new Uint8Array(o).buffer,o);case"DataView":{let{buffer:e}=new Uint8Array(o);return n(new DataView(e),o)}}return n(new eu[l](o),i)};return r},ec=e=>es(new Map,e)(0),{toString:ef}={},{keys:ep}=Object,ed=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=ef.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},eh=([e,t])=>0===e&&("function"===t||"symbol"===t),em=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},l=r=>{if(n.has(r))return n.get(r);let[o,a]=ed(r);switch(o){case 0:{let t=r;switch(a){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+a);t=null;break;case"undefined":return i([-1],r)}return i([o,t],r)}case 1:{if(a){let e=r;return"DataView"===a?e=new Uint8Array(r.buffer):"ArrayBuffer"===a&&(e=new Uint8Array(r)),i([a,[...e]],r)}let e=[],t=i([o,e],r);for(let t of r)e.push(l(t));return t}case 2:{if(a)switch(a){case"BigInt":return i([a,r.toString()],r);case"Boolean":case"Number":case"String":return i([a,r.valueOf()],r)}if(t&&"toJSON"in r)return l(r.toJSON());let n=[],u=i([o,n],r);for(let t of ep(r))(e||!eh(ed(r[t])))&&n.push([l(t),l(r[t])]);return u}case 3:return i([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([o,{source:e,flags:t}],r)}case 5:{let t=[],n=i([o,t],r);for(let[n,i]of r)(e||!(eh(ed(n))||eh(ed(i))))&&t.push([l(n),l(i)]);return n}case 6:{let t=[],n=i([o,t],r);for(let n of r)(e||!eh(ed(n)))&&t.push(l(n));return n}}let{message:u}=r;return i([o,{name:a,message:u}],r)};return l},eg=(e,{json:t,lossy:n}={})=>{let r=[];return em(!(t||n),!!t,new Map,r)(e),r},ey="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?ec(eg(e,t)):structuredClone(e):(e,t)=>ec(eg(e,t));var ek=n(3407);function ev(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let l=e.charCodeAt(n),o="";if(37===l&&(0,ek.lV)(e.charCodeAt(n+1))&&(0,ek.lV)(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){let t=e.charCodeAt(n+1);l<56320&&t>56319&&t<57344?(o=String.fromCharCode(l,t),i=1):o="�"}else o=String.fromCharCode(l);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+i+1,o=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function ex(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function eb(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var ew=n(95139);function eS(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),l=i[0];l&&"text"===l.type?l.value="["+l.value:i.unshift({type:"text",value:"["});let o=i[i.length-1];return o&&"text"===o.type?o.value+=r:i.push({type:"text",value:r}),i}function eC(e){let t=e.spread;return null==t?e.children.length>1:t}function eE(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let eP={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),l=ev(i.toLowerCase()),o=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(i,a);let u={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+l,id:r+"fnref-"+l+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,u);let s={type:"element",tagName:"sup",properties:{},children:[u]};return e.patch(t,s),e.applyData(t,s)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return eS(e,t);let i={src:ev(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)},image:function(e,t){let n={src:ev(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return eS(e,t);let i={href:ev(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)},link:function(e,t){let n={href:ev(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=eC(n[r])}return t}(n):eC(t),l={},o=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?o.push(e):o.push(...e.children)}let u=r[r.length-1];u&&(i||"element"!==u.type||"p"!==u.tagName)&&o.push({type:"text",value:"\n"});let s={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,s),e.applyData(t,s)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=V(t.children[1]),o=B(t.children[t.children.length-1]);l&&o&&(r.position={start:l,end:o}),i.push(r)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,o=l?l.length:t.children.length,a=-1,u=[];for(;++a<o;){let n=t.children[a],r={},o=l?l[a]:void 0;o&&(r.align=o);let s={type:"element",tagName:i,properties:r,children:[]};n&&(s.children=e.all(n),e.patch(n,s),s=e.applyData(n,s)),u.push(s)}let s={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,s),e.applyData(t,s)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,l=[];for(;r;)l.push(eE(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(eE(t.slice(i),i>0,!1)),l.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:eI,yaml:eI,definition:eI,footnoteDefinition:eI};function eI(){}let eT={}.hasOwnProperty,eA={};function eD(e,t){e.position&&(t.position=function(e){let t=V(e),n=B(e);if(t&&n)return{start:t,end:n}}(e))}function eO(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,ey(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function eL(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function eF(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function eM(e,t){let n=function(e,t){let n=t||eA,r=new Map,i=new Map,l={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=l.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=eF(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=eF(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:eO,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...eP,...n.handlers},one:function(e,t){let n=e.type,r=l.handlers[n];if(eT.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=ey(n);return r.children=l.all(e),r}return ey(e)}return(l.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(eT.call(n,"hProperties")||eT.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(l,e,t)},options:n,patch:eD,wrap:eL};return(0,ew.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l}(e,t),r=n.one(e,void 0),l=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||ex,r=e.options.footnoteBackLabel||eb,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],u=-1;for(;++u<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[u]);if(!i)continue;let l=e.all(i),o=String(i.identifier).toUpperCase(),s=ev(o.toLowerCase()),c=0,f=[],p=e.footnoteCounts.get(o);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(u,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+s+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(u,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let d=l[l.length-1];if(d&&"element"===d.type&&"p"===d.tagName){let e=d.children[d.children.length-1];e&&"text"===e.type?e.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...f)}else l.push(...f);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+s},children:e.wrap(l,!0)};e.patch(i,h),a.push(h)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...ey(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return l&&((0,i.ok)("children"in o),o.children.push({type:"text",value:"\n"},l)),o}function ez(e,t){return e&&"run"in e?async function(n,r){let i=eM(n,{file:r,...t});await e.run(i,r)}:function(n,r){return eM(n,{file:r,...e||t})}}function eN(e){if(e)throw e}var eR=n(70011);function e_(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let eH={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');ej(e);let r=0,i=-1,l=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else i<0&&(n=!0,i=l+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let o=-1,a=t.length-1;for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else o<0&&(n=!0,o=l+1),a>-1&&(e.codePointAt(l)===t.codePointAt(a--)?a<0&&(i=l):(a=-1,i=o));return r===i?i=o:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(ej(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;ej(e);let n=e.length,r=-1,i=0,l=-1,o=0;for(;n--;){let a=e.codePointAt(n);if(47===a){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===a?l<0?l=n:1!==o&&(o=1):l>-1&&(o=-1)}return l<0||r<0||0===o||1===o&&l===r-1&&l===i+1?"":e.slice(l,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)ej(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){ej(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",l=0,o=-1,a=0,u=-1;for(;++u<=e.length;){if(u<e.length)n=e.codePointAt(u);else if(47===n)break;else n=47;if(47===n){if(o===u-1||1===a);else if(o!==u-1&&2===a){if(i.length<2||2!==l||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",l=0):l=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),o=u,a=0;continue}}else if(i.length>0){i="",l=0,o=u,a=0;continue}}t&&(i=i.length>0?i+"/..":"..",l=2)}else i.length>0?i+="/"+e.slice(o+1,u):i=e.slice(o+1,u),l=u-o-1;o=u,a=0}else 46===n&&a>-1?a++:a=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function ej(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let eB={cwd:function(){return"/"}};function eV(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let eU=["history","path","basename","stem","extname","dirname"];class eq{constructor(e){let t,n;t=e?eV(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":eB.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<eU.length;){let e=eU[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)eU.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?eH.basename(this.path):void 0}set basename(e){e$(e,"basename"),eW(e,"basename"),this.path=eH.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?eH.dirname(this.path):void 0}set dirname(e){eY(this.basename,"dirname"),this.path=eH.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?eH.extname(this.path):void 0}set extname(e){if(eW(e,"extname"),eY(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=eH.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){eV(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!eV(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),e$(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?eH.basename(this.path,this.extname):void 0}set stem(e){e$(e,"stem"),eW(e,"stem"),this.path=eH.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new q.o(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function eW(e,t){if(e&&e.includes(eH.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+eH.sep+"`")}function e$(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function eY(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let eQ=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},eK={}.hasOwnProperty;class eJ extends eQ{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(l,...o){let a=e[++n],u=-1;if(l)return void r(l);for(;++u<t.length;)(null===o[u]||void 0===o[u])&&(o[u]=t[u]);t=o,a?(function(e,t){let n;return function(...t){let l,o=e.length>t.length;o&&t.push(r);try{l=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(l&&l.then&&"function"==typeof l.then?l.then(i,r):l instanceof Error?r(l):i(l))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...o):r(null,...o)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new eJ,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(eR(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(e0("data",this.frozen),this.namespace[e]=t,this):eK.call(this.namespace,e)&&this.namespace[e]||void 0:e?(e0("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=e4(e),n=this.parser||this.Parser;return eZ("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),eZ("process",this.parser||this.Parser),eG("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,l){let o=e4(e),a=n.parse(o);function u(e,n){e||!n?l(e):r?r(n):((0,i.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(a,o,function(e,t,r){var i,l;if(e||!t||!r)return u(e);let o=n.stringify(t,r);"string"==typeof(i=o)||(l=i)&&"object"==typeof l&&"byteLength"in l&&"byteOffset"in l?r.value=o:r.result=o,u(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),eZ("processSync",this.parser||this.Parser),eG("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,eN(e),t=r}),e2("processSync","process",n),(0,i.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){e1(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?l(void 0,n):new Promise(l);function l(l,o){(0,i.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let a=e4(t);r.run(e,a,function(t,r,a){let u=r||e;t?o(t):l?l(u):((0,i.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,u,a))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){eN(e),n=t,r=!0}),e2("runSync","run",r),(0,i.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=e4(t),r=this.compiler||this.Compiler;return eG("stringify",r),e1(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(e0("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?l(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=eR(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)o(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;o(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...l]=t,o=n[i][1];e_(o)&&e_(r)&&(r=eR(!0,o,r)),n[i]=[e,r,...l]}}}}let eX=new eJ().freeze();function eZ(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function eG(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function e0(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function e1(e){if(!e_(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function e2(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function e4(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new eq(e)}let e3=[],e5={allowDangerousHtml:!0},e9=/^(https?|ircs?|mailto|xmpp)$/i,e6=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function e7(e){let t=function(e){let t=e.rehypePlugins||e3,n=e.remarkPlugins||e3,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...e5}:e5;return eX().use(ea).use(n).use(ez,r).use(t)}(e),n=function(e){let t=e.children||"",n=new eq;return"string"==typeof t?n.value=t:(0,i.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,l=t.components,o=t.disallowedElements,a=t.skipHtml,u=t.unwrapDisallowed,s=t.urlTransform||e8;for(let e of e6)Object.hasOwn(t,e.from)&&(0,i.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&o&&(0,i.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),(0,ew.YR)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return a?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in ei)if(Object.hasOwn(ei,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=ei[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,i)),l&&i&&"number"==typeof t)return u&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,l,o;let a;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let u=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=u,r=t.jsxDEV,a=function(e,t,i,l){let o=Array.isArray(i.children),a=V(e);return r(t,i,l,o,{columnNumber:a?a.column-1:void 0,fileName:n,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,l=t.jsx,o=t.jsxs,a=function(e,t,n,r){let i=Array.isArray(n.children)?o:l;return r?i(t,n,r):i(t,n)}}let s={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:a,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:u,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?F:L,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},c=X(s,e,void 0);return c&&"string"!=typeof c?c:s.create(e,s.Fragment,{children:c||void 0},void 0)}(e,{Fragment:el.Fragment,components:l,ignoreInvalidStyle:!0,jsx:el.jsx,jsxs:el.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function e8(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||e9.test(e.slice(0,t))?e:""}},9038:(e,t,n)=>{"use strict";function r(e){return"&#x"+e.toString(16).toUpperCase()+";"}n.d(t,{T:()=>r})},9484:(e,t,n)=>{"use strict";n.d(t,{C1:()=>b,bL:()=>x});var r=n(12115),i=n(3468),l=n(97602),o=n(95155),a="Progress",[u,s]=(0,i.A)(a),[c,f]=u(a),p=r.forwardRef((e,t)=>{var n,r,i,a;let{__scopeProgress:u,value:s=null,max:f,getValueLabel:p=m,...d}=e;(f||0===f)&&!k(f)&&console.error((n="".concat(f),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let h=k(f)?f:100;null===s||v(s,h)||console.error((i="".concat(s),a="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(a,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let x=v(s,h)?s:null,b=y(x)?p(x,h):void 0;return(0,o.jsx)(c,{scope:u,value:x,max:h,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":h,"aria-valuemin":0,"aria-valuenow":y(x)?x:void 0,"aria-valuetext":b,role:"progressbar","data-state":g(x,h),"data-value":null!=x?x:void 0,"data-max":h,...d,ref:t})})});p.displayName=a;var d="ProgressIndicator",h=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...i}=e,a=f(d,r);return(0,o.jsx)(l.sG.div,{"data-state":g(a.value,a.max),"data-value":null!=(n=a.value)?n:void 0,"data-max":a.max,...i,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function k(e){return y(e)&&!isNaN(e)&&e>0}function v(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=d;var x=p,b=h},9548:(e,t,n)=>{"use strict";n.d(t,{K:()=>l,p:()=>i});let r=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function i(){return{canContainEols:["delete"],enter:{strikethrough:o},exit:{strikethrough:a}}}function l(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:r}],handlers:{delete:u}}}function o(e){this.enter({type:"delete",children:[]},e)}function a(e){this.exit(e)}function u(e,t,n,r){let i=n.createTracker(r),l=n.enter("strikethrough"),o=i.move("~~");return o+=n.containerPhrasing(e,{...i.current(),before:o,after:"~"}),o+=i.move("~~"),l(),o}u.peek=function(){return"~"}},12795:(e,t,n)=>{"use strict";n.d(t,{P:()=>o,S:()=>l});var r=n(63758),i=n(62737);function l(){return{exit:{taskListCheckValueChecked:a,taskListCheckValueUnchecked:a,paragraph:u}}}function o(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:s}}}function a(e){let t=this.stack[this.stack.length-2];(0,r.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function u(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,r.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,l=-1;for(;++l<i.length;){let e=i[l];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function s(e,t,n,r){let l=e.children[0],o="boolean"==typeof e.checked&&l&&"paragraph"===l.type,a="["+(e.checked?"x":" ")+"] ",u=n.createTracker(r);o&&u.move(a);let s=i.p.listItem(e,t,n,{...r,...u.current()});return o&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+a})),s}},16277:(e,t,n)=>{"use strict";function r(e){return e&&"object"==typeof e?"position"in e||"type"in e?l(e.position):"start"in e||"end"in e?l(e):"line"in e||"column"in e?i(e):"":""}function i(e){return o(e&&e.line)+":"+o(e&&e.column)}function l(e){return i(e&&e.start)+"-"+i(e&&e.end)}function o(e){return e&&"number"==typeof e?e:1}n.d(t,{L:()=>r})},17032:(e,t,n)=>{"use strict";function r(e,t,n,r){let i,l=e.length,o=0;if(t=t<0?-t>l?0:l+t:t>l?l:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);o<r.length;)(i=r.slice(o,o+1e4)).unshift(t,0),e.splice(...i),o+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},17039:(e,t,n)=>{"use strict";n.d(t,{s:()=>i});let r=document.createElement("i");function i(e){let t="&"+e+";";r.innerHTML=t;let n=r.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}},18283:(e,t,n)=>{"use strict";n.d(t,{o:()=>i});var r=n(16277);class i extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let i="",l={},o=!1;if(t&&(l="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?i=e:!l.cause&&e&&(o=!0,i=e.message,l.cause=e),!l.ruleId&&!l.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?l.ruleId=n:(l.source=n.slice(0,e),l.ruleId=n.slice(e+1))}if(!l.place&&l.ancestors&&l.ancestors){let e=l.ancestors[l.ancestors.length-1];e&&(l.place=e.position)}let a=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=a?a.column:void 0,this.fatal=void 0,this.file="",this.message=i,this.line=a?a.line:void 0,this.name=(0,r.L)(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=o&&l.cause&&"string"==typeof l.cause.stack?l.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}i.prototype.file="",i.prototype.name="",i.prototype.reason="",i.prototype.message="",i.prototype.stack="",i.prototype.column=void 0,i.prototype.line=void 0,i.prototype.ancestors=void 0,i.prototype.cause=void 0,i.prototype.fatal=void 0,i.prototype.place=void 0,i.prototype.ruleId=void 0,i.prototype.source=void 0},23362:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(3407);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},26983:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},30814:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},33634:(e,t,n)=>{"use strict";n.d(t,{B:()=>l});var r=n(79168),i=n(3407);let l={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,l,"linePrefix")(t):l(t)};function l(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}},34091:(e,t,n)=>{"use strict";n.d(t,{UU:()=>f,jD:()=>c,ni:()=>s});let r=/[$_\p{ID_Start}]/u,i=/[$_\u{200C}\u{200D}\p{ID_Continue}]/u,l=/[-$_\u{200C}\u{200D}\p{ID_Continue}]/u,o=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,u={};function s(e){return!!e&&r.test(String.fromCodePoint(e))}function c(e,t){let n=(t||u).jsx?l:i;return!!e&&n.test(String.fromCodePoint(e))}function f(e,t){return((t||u).jsx?a:o).test(e)}},35983:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},38220:(e,t,n)=>{"use strict";n.d(t,{y:()=>l});var r=n(17032);let i={}.hasOwnProperty;function l(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let l,o=(i.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(l in a){i.call(o,l)||(o[l]=[]);let e=a[l];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(o[l],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},41006:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}n.d(t,{W:()=>r})},42529:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48411:(e,t,n)=>{"use strict";n.d(t,{f:()=>o});var r=n(95139),i=n(56522),l=n(84151);function o(e,t){let n=!1;return(0,r.YR)(e,function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,i.dc}),!!((!e.depth||e.depth<3)&&(0,l.d)(e)&&(t.options.setext||n))}},49677:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,l=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},u=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var s;return(void 0===t&&(t={}),!(s=e)||i.test(s)||n.test(s))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,u):e.replace(l,u)).replace(r,a))}},54185:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(71729)),i=n(49677);function l(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}l.default=l,e.exports=l},55292:(e,t,n)=>{"use strict";function r(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}n.d(t,{C:()=>r})},55592:(e,t,n)=>{"use strict";n.d(t,{s:()=>o});var r=n(17039),i=n(55292);let l=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function o(e){return e.replace(l,a)}function a(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return(0,i.C)(n.slice(t?2:1),t?16:10)}return(0,r.s)(n)||e}},56522:(e,t,n)=>{"use strict";n.d(t,{dc:()=>l,VG:()=>o});var r=n(95502);let i=[],l=!1;function o(e,t,n,o){let a;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):a=t;let u=(0,r.C)(a),s=o?-1:1;(function e(r,a,c){let f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){let e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return p;function p(){var f;let p,d,h,m=i;if((!t||u(r,a,c[c.length-1]||void 0))&&(m=Array.isArray(f=n(r,c))?f:"number"==typeof f?[!0,f]:null==f?i:[f])[0]===l)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(d=(o?r.children.length:-1)+s,h=c.concat(r);d>-1&&d<r.children.length;){if((p=e(r.children[d],d,h)())[0]===l)return p;d="number"==typeof p[1]?p[1]:d+s}return m}})(e,void 0,[])()}},56716:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,l=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,u=/^\s+|\s+$/g;function s(e){return e?e.replace(u,""):""}e.exports=function(e,u){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];u=u||{};var c=1,f=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");f=~r?e.length-r:f+e.length}function d(){var e={line:c,column:f};return function(t){return t.position=new h(e),y(r),t}}function h(e){this.start=e,this.end={line:c,column:f},this.source=u.source}h.prototype.content=e;var m=[];function g(t){var n=Error(u.source+":"+c+":"+f+": "+t);if(n.reason=t,n.filename=u.source,n.line=c,n.column=f,n.source=e,u.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function k(e){var t;for(e=e||[];t=v();)!1!==t&&e.push(t);return e}function v(){var t=d();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}}y(r);var x,b=[];for(k(b);x=function(){var e=d(),n=y(i);if(n){if(v(),!y(l))return g("property missing ':'");var r=y(o),u=e({type:"declaration",property:s(n[0].replace(t,"")),value:r?s(r[0].replace(t,"")):""});return y(a),u}}();)!1!==x&&(b.push(x),k(b));return b}},57828:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(71847).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},62737:(e,t,n)=>{"use strict";function r(e,t,n){return">"+(n?"":" ")+e}n.d(t,{p:()=>P});var i=n(91305);function l(e,t,n,r){let l=-1;for(;++l<n.unsafe.length;)if("\n"===n.unsafe[l].character&&(0,i.q)(n.stack,n.unsafe[l]))return/[ \t]/.test(r.before)?"":" ";return"\\\n"}var o=n(69512);function a(e,t,n){return(n?"":"    ")+e}function u(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}var s=n(9038),c=n(23362);function f(e,t,n){let r=(0,c.S)(e),i=(0,c.S)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function p(e,t,n,r){let i=function(e){let t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),l=n.enter("emphasis"),o=n.createTracker(r),a=o.move(i),u=o.move(n.containerPhrasing(e,{after:i,before:a,...o.current()})),c=u.charCodeAt(0),p=f(r.before.charCodeAt(r.before.length-1),c,i);p.inside&&(u=(0,s.T)(c)+u.slice(1));let d=u.charCodeAt(u.length-1),h=f(r.after.charCodeAt(0),d,i);h.inside&&(u=u.slice(0,-1)+(0,s.T)(d));let m=o.move(i);return l(),n.attentionEncodeSurroundingInfo={after:h.outside,before:p.outside},a+u+m}p.peek=function(e,t,n){return n.options.emphasis||"*"};var d=n(48411);function h(e){return e.value||""}function m(e,t,n,r){let i=u(n),l='"'===i?"Quote":"Apostrophe",o=n.enter("image"),a=n.enter("label"),s=n.createTracker(r),c=s.move("![");return c+=s.move(n.safe(e.alt,{before:c,after:"]",...s.current()})),c+=s.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":")",...s.current()}))),a(),e.title&&(a=n.enter(`title${l}`),c+=s.move(" "+i),c+=s.move(n.safe(e.title,{before:c,after:i,...s.current()})),c+=s.move(i),a()),c+=s.move(")"),o(),c}function g(e,t,n,r){let i=e.referenceType,l=n.enter("imageReference"),o=n.enter("label"),a=n.createTracker(r),u=a.move("!["),s=n.safe(e.alt,{before:u,after:"]",...a.current()});u+=a.move(s+"]["),o();let c=n.stack;n.stack=[],o=n.enter("reference");let f=n.safe(n.associationId(e),{before:u,after:"]",...a.current()});return o(),n.stack=c,l(),"full"!==i&&s&&s===f?"shortcut"===i?u=u.slice(0,-1):u+=a.move("]"):u+=a.move(f+"]"),u}function y(e,t,n){let r=e.value||"",i="`",l=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<n.unsafe.length;){let e,t=n.unsafe[l],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}h.peek=function(){return"<"},m.peek=function(){return"!"},g.peek=function(){return"!"},y.peek=function(){return"`"};var k=n(84151);function v(e,t){let n=(0,k.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function x(e,t,n,r){let i,l,o=u(n),a='"'===o?"Quote":"Apostrophe",s=n.createTracker(r);if(v(e,n)){let t=n.stack;n.stack=[],i=n.enter("autolink");let r=s.move("<");return r+=s.move(n.containerPhrasing(e,{before:r,after:">",...s.current()})),r+=s.move(">"),i(),n.stack=t,r}i=n.enter("link"),l=n.enter("label");let c=s.move("[");return c+=s.move(n.containerPhrasing(e,{before:c,after:"](",...s.current()})),c+=s.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(l=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":")",...s.current()}))),l(),e.title&&(l=n.enter(`title${a}`),c+=s.move(" "+o),c+=s.move(n.safe(e.title,{before:c,after:o,...s.current()})),c+=s.move(o),l()),c+=s.move(")"),i(),c}function b(e,t,n,r){let i=e.referenceType,l=n.enter("linkReference"),o=n.enter("label"),a=n.createTracker(r),u=a.move("["),s=n.containerPhrasing(e,{before:u,after:"]",...a.current()});u+=a.move(s+"]["),o();let c=n.stack;n.stack=[],o=n.enter("reference");let f=n.safe(n.associationId(e),{before:u,after:"]",...a.current()});return o(),n.stack=c,l(),"full"!==i&&s&&s===f?"shortcut"===i?u=u.slice(0,-1):u+=a.move("]"):u+=a.move(f+"]"),u}function w(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function S(e){let t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}x.peek=function(e,t,n){return v(e,n)?"<":"["},b.peek=function(){return"["};let C=(0,n(95502).C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function E(e,t,n,r){let i=function(e){let t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),l=n.enter("strong"),o=n.createTracker(r),a=o.move(i+i),u=o.move(n.containerPhrasing(e,{after:i,before:a,...o.current()})),c=u.charCodeAt(0),p=f(r.before.charCodeAt(r.before.length-1),c,i);p.inside&&(u=(0,s.T)(c)+u.slice(1));let d=u.charCodeAt(u.length-1),h=f(r.after.charCodeAt(0),d,i);h.inside&&(u=u.slice(0,-1)+(0,s.T)(d));let m=o.move(i+i);return l(),n.attentionEncodeSurroundingInfo={after:h.outside,before:p.outside},a+u+m}E.peek=function(e,t,n){return n.options.strong||"*"};let P={blockquote:function(e,t,n,i){let l=n.enter("blockquote"),o=n.createTracker(i);o.move("> "),o.shift(2);let a=n.indentLines(n.containerFlow(e,o.current()),r);return l(),a},break:l,code:function(e,t,n,r){let i=function(e){let t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),l=e.value||"",u="`"===i?"GraveAccent":"Tilde";if((0,o.m)(e,n)){let e=n.enter("codeIndented"),t=n.indentLines(l,a);return e(),t}let s=n.createTracker(r),c=i.repeat(Math.max(function(e,t){let n=String(e),r=n.indexOf(t),i=r,l=0,o=0;if("string"!=typeof t)throw TypeError("Expected substring");for(;-1!==r;)r===i?++l>o&&(o=l):l=1,i=r+t.length,r=n.indexOf(t,i);return o}(l,i)+1,3)),f=n.enter("codeFenced"),p=s.move(c);if(e.lang){let t=n.enter(`codeFencedLang${u}`);p+=s.move(n.safe(e.lang,{before:p,after:" ",encode:["`"],...s.current()})),t()}if(e.lang&&e.meta){let t=n.enter(`codeFencedMeta${u}`);p+=s.move(" "),p+=s.move(n.safe(e.meta,{before:p,after:"\n",encode:["`"],...s.current()})),t()}return p+=s.move("\n"),l&&(p+=s.move(l+"\n")),p+=s.move(c),f(),p},definition:function(e,t,n,r){let i=u(n),l='"'===i?"Quote":"Apostrophe",o=n.enter("definition"),a=n.enter("label"),s=n.createTracker(r),c=s.move("[");return c+=s.move(n.safe(n.associationId(e),{before:c,after:"]",...s.current()})),c+=s.move("]: "),a(),!e.url||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...s.current()}))),a(),e.title&&(a=n.enter(`title${l}`),c+=s.move(" "+i),c+=s.move(n.safe(e.title,{before:c,after:i,...s.current()})),c+=s.move(i),a()),o(),c},emphasis:p,hardBreak:l,heading:function(e,t,n,r){let i=Math.max(Math.min(6,e.depth||1),1),l=n.createTracker(r);if((0,d.f)(e,n)){let t=n.enter("headingSetext"),r=n.enter("phrasing"),o=n.containerPhrasing(e,{...l.current(),before:"\n",after:"\n"});return r(),t(),o+"\n"+(1===i?"=":"-").repeat(o.length-(Math.max(o.lastIndexOf("\r"),o.lastIndexOf("\n"))+1))}let o="#".repeat(i),a=n.enter("headingAtx"),u=n.enter("phrasing");l.move(o+" ");let c=n.containerPhrasing(e,{before:"# ",after:"\n",...l.current()});return/^[\t ]/.test(c)&&(c=(0,s.T)(c.charCodeAt(0))+c.slice(1)),c=c?o+" "+c:o,n.options.closeAtx&&(c+=" "+o),u(),a(),c},html:h,image:m,imageReference:g,inlineCode:y,link:x,linkReference:b,list:function(e,t,n,r){let i=n.enter("list"),l=n.bulletCurrent,o=e.ordered?function(e){let t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):w(n),a=e.ordered?"."===o?")":".":function(e){let t=w(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n),u=!!t&&!!n.bulletLastUsed&&o===n.bulletLastUsed;if(!e.ordered){let t=e.children?e.children[0]:void 0;if("*"!==o&&"-"!==o||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(u=!0),S(n)===o&&t){let t=-1;for(;++t<e.children.length;){let n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){u=!0;break}}}}u&&(o=a),n.bulletCurrent=o;let s=n.containerFlow(e,r);return n.bulletLastUsed=o,n.bulletCurrent=l,i(),s},listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),l=n.bulletCurrent||w(n);t&&"list"===t.type&&t.ordered&&(l=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+l);let o=l.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(o=4*Math.ceil(o/4));let a=n.createTracker(r);a.move(l+" ".repeat(o-l.length)),a.shift(o);let u=n.enter("listItem"),s=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){return t?(n?"":" ".repeat(o))+e:(n?l:l+" ".repeat(o-l.length))+e});return u(),s},paragraph:function(e,t,n,r){let i=n.enter("paragraph"),l=n.enter("phrasing"),o=n.containerPhrasing(e,r);return l(),i(),o},root:function(e,t,n,r){return(e.children.some(function(e){return C(e)})?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:E,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){let r=(S(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){let t=e.options.ruleRepetition||3;if(t<3)throw Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}}},63758:(e,t,n)=>{"use strict";function r(){}function i(){}n.d(t,{HB:()=>i,ok:()=>r})},67047:(e,t,n)=>{"use strict";n.d(t,{A:()=>ec});var r=n(5009),i=n(63758),l=n(3407),o=n(56522),a=n(95502);let u="phrasing",s=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function f(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function d(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function h(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,a.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),l=-1;for(;++l<i.length;)(0,o.VG)(e,"text",u);function u(e,t){let n,o=-1;for(;++o<t.length;){let e=t[o],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[l][0],o=i[l][1],a=0,u=n.children.indexOf(e),s=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){let n=f.index,i={index:f.index,input:f.input,stack:[...t,e]},l=o(...f,i);if("string"==typeof l&&(l=l.length>0?{type:"text",value:l}:void 0),!1===l?r.lastIndex=n+1:(a!==n&&c.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(l)?c.push(...l):l&&c.push(l),a=n+f[0].length,s=!0),!r.global)break;f=r.exec(e.value)}return s?(a<e.value.length&&c.push({type:"text",value:e.value.slice(a)}),n.children.splice(u,1,...c)):c=[e],u+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,y],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,k]],{ignore:["link","linkReference"]})}function y(e,t,n,i,l){let o="";if(!v(l)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),l=(0,r.D)(e,"("),o=(0,r.D)(e,")");for(;-1!==i&&l>o;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),o++;return[e,n]}(n+i);if(!a[0])return!1;let u={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function k(e,t,n,r){return!(!v(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function v(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,l.Ny)(n)||(0,l.es)(n))&&(!t||47!==n)}var x=n(35983);function b(){this.buffer()}function w(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function S(){this.buffer()}function C(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function E(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,x.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function P(e){this.exit(e)}function I(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,x.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function T(e){this.exit(e)}function A(e,t,n,r){let i=n.createTracker(r),l=i.move("[^"),o=n.enter("footnoteReference"),a=n.enter("reference");return l+=i.move(n.safe(n.associationId(e),{after:"]",before:l})),a(),o(),l+=i.move("]")}function D(e,t,n){return 0===t?e:O(e,t,n)}function O(e,t,n){return(n?"":"    ")+e}A.peek=function(){return"["};var L=n(9548),F=n(74037),M=n(12795),z=n(38220);let N={tokenize:function(e,t,n){let r=0;return function t(l){return(87===l||119===l)&&r<3?(r++,e.consume(l),t):46===l&&3===r?(e.consume(l),i):n(l)};function i(e){return null===e?n(e):t(e)}},partial:!0},R={tokenize:function(e,t,n){let r,i,o;return a;function a(t){return 46===t||95===t?e.check(H,s,u)(t):null===t||(0,l.Ee)(t)||(0,l.Ny)(t)||45!==t&&(0,l.es)(t)?s(t):(o=!0,e.consume(t),a)}function u(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),a}function s(e){return i||r||!o?n(e):t(e)}},partial:!0},_={tokenize:function(e,t){let n=0,r=0;return i;function i(a){return 40===a?(n++,e.consume(a),i):41===a&&r<n?o(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(H,t,o)(a):null===a||(0,l.Ee)(a)||(0,l.Ny)(a)?t(a):(e.consume(a),i)}function o(t){return 41===t&&r++,e.consume(t),i}},partial:!0},H={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),o):93===a?(e.consume(a),i):60===a||null===a||(0,l.Ee)(a)||(0,l.Ny)(a)?t(a):n(a)}function i(e){return null===e||40===e||91===e||(0,l.Ee)(e)||(0,l.Ny)(e)?t(e):r(e)}function o(t){return(0,l.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,l.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},j={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,l.lV)(e)?n(e):t(e)}},partial:!0},B={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!$.call(r,r.previous)||J(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(N,e.attempt(R,e.attempt(_,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:$},V={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",o=!1;return function(t){return(72===t||104===t)&&Y.call(r,r.previous)&&!J(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),a):n(t)};function a(t){if((0,l.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),a;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),u}return n(t)}function u(t){return 47===t?(e.consume(t),o)?s:(o=!0,u):n(t)}function s(t){return null===t||(0,l.JQ)(t)||(0,l.Ee)(t)||(0,l.Ny)(t)||(0,l.es)(t)?n(t):e.attempt(R,e.attempt(_,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:Y},U={name:"emailAutolink",tokenize:function(e,t,n){let r,i,o=this;return function(t){return!K(t)||!Q.call(o,o.previous)||J(o.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return K(r)?(e.consume(r),t):64===r?(e.consume(r),a):n(r)}(t))};function a(t){return 46===t?e.check(j,s,u)(t):45===t||95===t||(0,l.lV)(t)?(i=!0,e.consume(t),a):s(t)}function u(t){return e.consume(t),r=!0,a}function s(a){return i&&r&&(0,l.CW)(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:Q},q={},W=48;for(;W<123;)q[W]=U,58==++W?W=65:91===W&&(W=97);function $(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,l.Ee)(e)}function Y(e){return!(0,l.CW)(e)}function Q(e){return!(47===e||K(e))}function K(e){return 43===e||45===e||46===e||95===e||(0,l.lV)(e)}function J(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}q[43]=U,q[45]=U,q[46]=U,q[95]=U,q[72]=[U,V],q[104]=[U,V],q[87]=[U,B],q[119]=[U,B];var X=n(33634),Z=n(79168);let G={tokenize:function(e,t,n){let r=this;return(0,Z.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function ee(e,t,n){let r,i=this,l=i.events.length,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;l--;){let e=i.events[l][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(l){if(!r||!r._balanced)return n(l);let a=(0,x.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===a.codePointAt(0)&&o.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),t(l)):n(l)}}function et(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},a=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",o,t],["exit",o,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...a),e}function en(e,t,n){let r,i=this,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),u};function u(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(u){if(a>999||93===u&&!r||null===u||91===u||(0,l.Ee)(u))return n(u);if(93===u){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return o.includes((0,x.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(u)}return(0,l.Ee)(u)||(r=!0),a++,e.consume(u),92===u?c:s}function c(t){return 91===t||92===t||93===t?(e.consume(t),a++,s):s(t)}}function er(e,t,n){let r,i,o=this,a=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),u=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(u>999||93===t&&!i||null===t||91===t||(0,l.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,x.B)(o.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return(0,l.Ee)(t)||(i=!0),u++,e.consume(t),92===t?f:c}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}function p(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a.includes(r)||a.push(r),(0,Z.N)(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function ei(e,t,n){return e.check(X.B,t,e.attempt(G,t,n))}function el(e){e.exit("gfmFootnoteDefinition")}var eo=n(3922),ea=n(99027),eu=n(80861);let es={};function ec(e){let t,n=e||es,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,z.y)([{text:q},{document:{91:{name:"gfmFootnoteDefinition",tokenize:er,continuation:{tokenize:ei},exit:el}},text:{91:{name:"gfmFootnoteCall",tokenize:en},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ee,resolveTo:et}}},(0,eo.K)(n),(0,ea.x)(),(0,eu.J)()])),l.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:f,literalAutolinkHttp:f,literalAutolinkWww:f},exit:{literalAutolink:m,literalAutolinkEmail:h,literalAutolinkHttp:p,literalAutolinkWww:d}},{enter:{gfmFootnoteCallString:b,gfmFootnoteCall:w,gfmFootnoteDefinitionLabelString:S,gfmFootnoteDefinition:C},exit:{gfmFootnoteCallString:E,gfmFootnoteCall:P,gfmFootnoteDefinitionLabelString:I,gfmFootnoteDefinition:T}},(0,L.p)(),(0,F.c)(),(0,M.S)()]),o.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:":",before:"[ps]",after:"\\/",inConstruct:u,notInConstruct:s}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let l=r.createTracker(i),o=l.move("[^"),a=r.enter("footnoteDefinition"),u=r.enter("label");return o+=l.move(r.safe(r.associationId(e),{before:o,after:"]"})),u(),o+=l.move("]:"),e.children&&e.children.length>0&&(l.shift(4),o+=l.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,l.current()),t?O:D))),a(),o},footnoteReference:A},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),(0,L.K)(),(0,F.d)(n),(0,M.P)()]})}},69512:(e,t,n)=>{"use strict";function r(e,t){return!!(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}n.d(t,{m:()=>r})},70011:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!l)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},u=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,s,c,f=arguments[0],p=1,d=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<d;++p)if(t=arguments[p],null!=t)for(n in t)r=u(f,n),f!==(i=u(t,n))&&(h&&i&&(o(i)||(s=l(i)))?(s?(s=!1,c=r&&l(r)?r:[]):c=r&&o(r)?r:{},a(f,{name:n,newValue:e(h,c,i)})):void 0!==i&&a(f,{name:n,newValue:i}));return f}},71729:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),l="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;l?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(56716))},74037:(e,t,n)=>{"use strict";n.d(t,{c:()=>a,d:()=>m});var r=n(63758);function i(e){return e.length}function l(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}var o=n(62737);function a(){return{enter:{table:u,tableData:p,tableHeader:p,tableRow:c},exit:{codeText:d,table:s,tableData:f,tableHeader:f,tableRow:f}}}function u(e){let t=e._align;(0,r.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function s(e){this.exit(e),this.data.inTable=void 0}function c(e){this.enter({type:"tableRow",children:[]},e)}function f(e){this.exit(e)}function p(e){this.enter({type:"tableCell",children:[]},e)}function d(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,h));let n=this.stack[this.stack.length-1];(0,r.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function h(e,t){return"|"===t?t:e}function m(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,a=t.stringLength,u=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=o.p.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return c(function(e,t,n){let r=e.children,i=-1,l=[],o=t.enter("table");for(;++i<r.length;)l[i]=f(r[i],t,n);return o(),l}(e,n,r),e.align)},tableCell:s,tableRow:function(e,t,n,r){let i=c([f(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function s(e,t,n,r){let i=n.enter("tableCell"),l=n.enter("phrasing"),o=n.containerPhrasing(e,{...r,before:u,after:u});return l(),i(),o}function c(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),o=n.stringLength||i,a=[],u=[],s=[],c=[],f=0,p=-1;for(;++p<e.length;){let t=[],r=[],i=-1;for(e[p].length>f&&(f=e[p].length);++i<e[p].length;){var d;let l=null==(d=e[p][i])?"":String(d);if(!1!==n.alignDelimiters){let e=o(l);r[i]=e,(void 0===c[i]||e>c[i])&&(c[i]=e)}t.push(l)}u[p]=t,s[p]=r}let h=-1;if("object"==typeof r&&"length"in r)for(;++h<f;)a[h]=l(r[h]);else{let e=l(r);for(;++h<f;)a[h]=e}h=-1;let m=[],g=[];for(;++h<f;){let e=a[h],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,c[h]-t.length-r.length),l=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>c[h]&&(c[h]=i),g[h]=i),m[h]=l}u.splice(1,0,m),s.splice(1,0,g),p=-1;let y=[];for(;++p<u.length;){let e=u[p],t=s[p];h=-1;let r=[];for(;++h<f;){let i=e[h]||"",l="",o="";if(!1!==n.alignDelimiters){let e=c[h]-(t[h]||0),n=a[h];114===n?l=" ".repeat(e):99===n?e%2?(l=" ".repeat(e/2+.5),o=" ".repeat(e/2-.5)):o=l=" ".repeat(e/2):o=" ".repeat(e)}!1===n.delimiterStart||h||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||h)&&r.push(" "),!1!==n.alignDelimiters&&r.push(l),r.push(i),!1!==n.alignDelimiters&&r.push(o),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||h!==f-1)&&r.push("|")}y.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return y.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:a})}function f(e,t,n){let r=e.children,i=-1,l=[],o=t.enter("tableRow");for(;++i<r.length;)l[i]=s(r[i],e,t,n);return o(),l}}},79168:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(3407);function i(e,t,n,i){let l=i?i-1:Number.POSITIVE_INFINITY,o=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(a){return(0,r.On)(a)&&o++<l?(e.consume(a),i):(e.exit(n),t(a))}(i)):t(i)}}},80861:(e,t,n)=>{"use strict";n.d(t,{J:()=>o});var r=n(79168),i=n(3407);let l={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),l):n(t)};function l(t){return(0,i.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),o):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),o):n(t)}function o(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),u):n(t)}function u(r){return(0,i.HP)(r)?t(r):(0,i.On)(r)?e.check({tokenize:a},t,n)(r):n(r)}}};function o(){return{text:{91:l}}}function a(e,t,n){return(0,r.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}},84151:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return l(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function l(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return o(e.children,t,n)}return Array.isArray(e)?o(e,t,n):""}function o(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=l(e[i],t,n);return r.join("")}},91305:(e,t,n)=>{"use strict";function r(e,t){return i(e,t.inConstruct,!0)&&!i(e,t.notInConstruct,!1)}function i(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}n.d(t,{q:()=>r})},95139:(e,t,n)=>{"use strict";n.d(t,{YR:()=>i});var r=n(56522);function i(e,t,n,i){let l,o,a;"function"==typeof t&&"function"!=typeof n?(o=void 0,a=t,l=n):(o=t,a=n,l=i),(0,r.VG)(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},l)}},95502:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return l;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function l(){return!0}},97853:(e,t,n)=>{"use strict";n.d(t,{Y:()=>ev});var r={};n.r(r),n.d(r,{attentionMarkers:()=>ep,contentInitial:()=>eo,disable:()=>ed,document:()=>el,flow:()=>eu,flowInitial:()=>ea,insideSpan:()=>ef,string:()=>es,text:()=>ec});var i=n(84151),l=n(17032);class o{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&a(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),a(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),a(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);a(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);a(this.left,t.reverse())}}}function a(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function u(e){let t,n,r,i,a,u,s,c={},f=-1,p=new o(e);for(;++f<p.length;){for(;f in c;)f=c[f];if(t=p.get(f),f&&"chunkFlow"===t[1].type&&"listItemPrefix"===p.get(f-1)[1].type&&((r=0)<(u=t[1]._tokenizer.events).length&&"lineEndingBlank"===u[r][1].type&&(r+=2),r<u.length&&"content"===u[r][1].type))for(;++r<u.length&&"content"!==u[r][1].type;)"chunkText"===u[r][1].type&&(u[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(c,function(e,t){let n,r,i=e.get(t)[1],l=e.get(t)[2],o=t-1,a=[],u=i._tokenizer;!u&&(u=l.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(u._contentTypeTextTrailing=!0));let s=u.events,c=[],f={},p=-1,d=i,h=0,m=0,g=[0];for(;d;){for(;e.get(++o)[1]!==d;);a.push(o),!d._tokenizer&&(n=l.sliceStream(d),d.next||n.push(null),r&&u.defineSkip(d.start),d._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=!0),u.write(n),d._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=void 0)),r=d,d=d.next}for(d=i;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(m=p+1,g.push(m),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(u.events=[],d?(d._tokenizer=void 0,d.previous=void 0):g.pop(),p=g.length;p--;){let t=s.slice(g[p],g[p+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)f[h+c[p][0]]=h+c[p][1],h+=c[p][1]-c[p][0]-1;return f}(p,f)),f=c[f],s=!0);else if(t[1]._container){for(r=f,n=void 0;r--;)if("lineEnding"===(i=p.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(p.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...p.get(n)[1].start},(a=p.slice(n,f)).unshift(t),p.splice(n,f-n+1,a))}}return(0,l.m)(e,0,Number.POSITIVE_INFINITY,p.slice(0)),!s}var s=n(38220),c=n(79168),f=n(3407);let p={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,f.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},d={tokenize:function(e){let t,n,r,i=this,o=[],a=0;return u;function u(t){if(a<o.length){let n=o[a];return i.containerState=n[1],e.attempt(n[0].continuation,s,c)(t)}return c(t)}function s(e){if(a++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&x();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}v(a);let u=r;for(;u<i.events.length;)i.events[u][1].end={...n},u++;return(0,l.m)(i.events,o+1,0,i.events.slice(r)),i.events.length=u,c(e)}return u(e)}function c(n){if(a===o.length){if(!t)return m(n);if(t.currentConstruct&&t.currentConstruct.concrete)return y(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(h,p,d)(n)}function p(e){return t&&x(),v(a),m(e)}function d(e){return i.parser.lazy[i.now().line]=a!==o.length,r=i.now().offset,y(e)}function m(t){return i.containerState={},e.attempt(h,g,y)(t)}function g(e){return a++,o.push([i.currentConstruct,i.containerState]),m(e)}function y(r){if(null===r){t&&x(),v(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){k(e.exit("chunkFlow"),!0),v(0),e.consume(n);return}return(0,f.HP)(n)?(e.consume(n),k(e.exit("chunkFlow")),a=0,i.interrupt=void 0,u):(e.consume(n),t)}(r)}function k(e,o){let u=i.sliceStream(e);if(o&&u.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(u),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let u=i.events.length,s=u;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(v(a),o=u;o<i.events.length;)i.events[o][1].end={...n},o++;(0,l.m)(i.events,s+1,0,i.events.slice(u)),i.events.length=o}}function v(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function x(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},h={tokenize:function(e,t,n){return(0,c.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var m=n(33634);let g={resolve:function(e){return u(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,f.HP)(t)?e.check(y,l,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function l(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},y={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,f.HP)(i))return n(i);let l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},k={tokenize:function(e){let t=this,n=e.attempt(m.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,c.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(g,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},v={resolveAll:S()},x=w("string"),b=w("text");function w(e){return{resolveAll:S("text"===e?C:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,l,o);return l;function l(e){return u(e)?i(e):o(e)}function o(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return u(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function u(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function S(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function C(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],l=t.sliceStream(i),o=l.length,a=-1,u=0;for(;o--;){let e=l[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)u++,a--;if(a)break;a=-1}else if(-2===e)r=!0,u++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(u=0),u){let l={type:n===e.length||r||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-u,offset:i.end.offset-u},end:{...i.end}};i.end={...l.start},i.start.offset===i.end.offset?Object.assign(i,l):(e.splice(n,0,["enter",l,t],["exit",l,t]),n+=2)}n++}return e}let E={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(l){var o;return e.enter("thematicBreak"),r=o=l,function l(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,f.On)(n)?(0,c.N)(e,l,"whitespace")(n):l(n))}(o)):i>=3&&(null===o||(0,f.HP)(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},P={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(m.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,c.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,f.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(T,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,c.N)(e,e.attempt(P,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],l=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,f.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(E,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,f.BM)(i)&&++o<10?(e.consume(i),t):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(m.B,r.interrupt?n:u,e.attempt(I,c,s))}function u(e){return r.containerState.initialBlankLine=!0,l++,c(e)}function s(t){return(0,f.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},I={partial:!0,tokenize:function(e,t,n){let r=this;return(0,c.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,f.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},T={partial:!0,tokenize:function(e,t,n){let r=this;return(0,c.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},A={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,f.On)(t)?(0,c.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(A,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,f.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function D(e,t,n,r,i,l,o,a,u){let s=u||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(l),e.consume(t),e.exit(l),p):null===t||32===t||41===t||(0,f.JQ)(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),m(t))};function p(n){return 62===n?(e.enter(l),e.consume(n),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),p(t)):null===t||60===t||(0,f.HP)(t)?n(t):(e.consume(t),92===t?h:d)}function h(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function m(i){return!c&&(null===i||41===i||(0,f.Ee)(i))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(i)):c<s&&40===i?(e.consume(i),c++,m):41===i?(e.consume(i),c--,m):null===i||32===i||40===i||(0,f.JQ)(i)?n(i):(e.consume(i),92===i?g:m)}function g(t){return 40===t||41===t||92===t?(e.consume(t),m):m(t)}}function O(e,t,n,r,i,l){let o,a=this,u=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(l),s};function s(p){return u>999||null===p||91===p||93===p&&!o||94===p&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(p):93===p?(e.exit(l),e.enter(i),e.consume(p),e.exit(i),e.exit(r),t):(0,f.HP)(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),c(p))}function c(t){return null===t||91===t||93===t||(0,f.HP)(t)||u++>999?(e.exit("chunkString"),s(t)):(e.consume(t),o||(o=!(0,f.On)(t)),92===t?p:c)}function p(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}}function L(e,t,n,r,i,l){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(l),u(n))}function u(t){return t===o?(e.exit(l),a(o)):null===t?n(t):(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(t))}function s(t){return t===o||null===t||(0,f.HP)(t)?(e.exit("chunkString"),u(t)):(e.consume(t),92===t?p:s)}function p(t){return t===o||92===t?(e.consume(t),s):s(t)}}function F(e,t){let n;return function r(i){return(0,f.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,f.On)(i)?(0,c.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var M=n(35983);let z={partial:!0,tokenize:function(e,t,n){return function(t){return(0,f.Ee)(t)?F(e,r)(t):n(t)};function r(t){return L(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,f.On)(t)?(0,c.N)(e,l,"whitespace")(t):l(t)}function l(e){return null===e||(0,f.HP)(e)?t(e):n(e)}}},N={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,c.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?l(n):(0,f.HP)(n)?e.attempt(R,t,l)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,f.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function l(n){return e.exit("codeIndented"),t(n)}}},R={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,c.N)(e,l,"linePrefix",5)(t)}function l(e){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(e):(0,f.HP)(e)?i(e):n(e)}}},_={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,l=e.length;for(;l--;)if("enter"===e[l][0]){if("content"===e[l][1].type){n=l;break}"paragraph"===e[l][1].type&&(r=l)}else"content"===e[l][1].type&&e.splice(l,1),i||"definition"!==e[l][1].type||(i=l);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var o;let a,u=i.events.length;for(;u--;)if("lineEnding"!==i.events[u][1].type&&"linePrefix"!==i.events[u][1].type&&"content"!==i.events[u][1].type){a="paragraph"===i.events[u][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||a)?(e.enter("setextHeadingLine"),r=t,o=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,f.On)(n)?(0,c.N)(e,l,"lineSuffix")(n):l(n))}(o)):n(t)};function l(r){return null===r||(0,f.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},H=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],j=["pre","script","style","textarea"],B={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(m.B,t,n)}}},V={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},U={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},q={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,l={partial:!0,tokenize:function(e,t,n){let l=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),(0,f.On)(t)?(0,c.N)(e,u,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l>=a?(e.exit("codeFencedFenceSequence"),(0,f.On)(i)?(0,c.N)(e,s,"whitespace")(i):s(i)):n(i)}(t)):n(t)}function s(r){return null===r||(0,f.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){var l=t;let s=i.events[i.events.length-1];return o=s&&"linePrefix"===s[1].type?s[2].sliceSerialize(s[1],!0).length:0,r=l,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),(0,f.On)(i)?(0,c.N)(e,u,"whitespace")(i):u(i))}(l)};function u(l){return null===l||(0,f.HP)(l)?(e.exit("codeFencedFence"),i.interrupt?t(l):e.check(U,p,g)(l)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,f.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(i)):(0,f.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,c.N)(e,s,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(l))}function s(t){return null===t||(0,f.HP)(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,f.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function p(t){return e.attempt(l,g,d)(t)}function d(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),h}function h(t){return o>0&&(0,f.On)(t)?(0,c.N)(e,m,"linePrefix",o+1)(t):m(t)}function m(t){return null===t||(0,f.HP)(t)?e.check(U,p,g)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,f.HP)(n)?(e.exit("codeFlowValue"),m(n)):(e.consume(n),t)}(t))}function g(n){return e.exit("codeFenced"),t(n)}}};var W=n(17039);let $={name:"characterReference",tokenize:function(e,t,n){let r,i,l=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),r=31,i=f.lV,s(t))}function u(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=f.ok,s):(e.enter("characterReferenceValue"),r=7,i=f.BM,s(t))}function s(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return i!==f.lV||(0,W.s)(l.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&o++<r?(e.consume(a),s):n(a)}}},Y={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,f.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},Q={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,c.N)(e,t,"linePrefix")}}};var K=n(41006);let J={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,l.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,o,a=e.length,u=0;for(;a--;)if(n=e[a][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[a][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[a][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=a,"labelLink"!==n.type)){u=2;break}}else"labelEnd"===n.type&&(i=a);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},f={type:"labelText",start:{...e[r+u+2][1].end},end:{...e[i-2][1].start}};return o=[["enter",s,t],["enter",c,t]],o=(0,l.V)(o,e.slice(r+1,r+u+3)),o=(0,l.V)(o,[["enter",f,t]]),o=(0,l.V)(o,(0,K.W)(t.parser.constructs.insideSpan.null,e.slice(r+u+4,i-3),t)),o=(0,l.V)(o,[["exit",f,t],e[i-2],e[i-1],["exit",c,t]]),o=(0,l.V)(o,e.slice(i+1)),o=(0,l.V)(o,[["exit",s,t]]),(0,l.m)(e,r,e.length,o),e},tokenize:function(e,t,n){let r,i,l=this,o=l.events.length;for(;o--;)if(("labelImage"===l.events[o][1].type||"labelLink"===l.events[o][1].type)&&!l.events[o][1]._balanced){r=l.events[o][1];break}return function(t){return r?r._inactive?c(t):(i=l.parser.defined.includes((0,M.B)(l.sliceSerialize({start:r.end,end:l.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(X,s,i?s:c)(t):91===t?e.attempt(Z,s,i?u:c)(t):i?s(t):c(t)}function u(t){return e.attempt(G,s,c)(t)}function s(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},X={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,f.Ee)(t)?F(e,i)(t):i(t)}function i(t){return 41===t?s(t):D(e,l,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function l(t){return(0,f.Ee)(t)?F(e,a)(t):s(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?L(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):s(t)}function u(t){return(0,f.Ee)(t)?F(e,s)(t):s(t)}function s(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},Z={tokenize:function(e,t,n){let r=this;return function(t){return O.call(r,e,i,l,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,M.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function l(e){return n(e)}}},G={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},ee={name:"labelStartImage",resolveAll:J.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),l):n(t)}function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var et=n(23362);let en={name:"attention",resolveAll:function(e,t){let n,r,i,o,a,u,s,c,f=-1;for(;++f<e.length;)if("enter"===e[f][0]&&"attentionSequence"===e[f][1].type&&e[f][1]._close){for(n=f;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[f][1]).charCodeAt(0)){if((e[n][1]._close||e[f][1]._open)&&(e[f][1].end.offset-e[f][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[f][1].end.offset-e[f][1].start.offset)%3))continue;u=e[n][1].end.offset-e[n][1].start.offset>1&&e[f][1].end.offset-e[f][1].start.offset>1?2:1;let p={...e[n][1].end},d={...e[f][1].start};er(p,-u),er(d,u),o={type:u>1?"strongSequence":"emphasisSequence",start:p,end:{...e[n][1].end}},a={type:u>1?"strongSequence":"emphasisSequence",start:{...e[f][1].start},end:d},i={type:u>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[f][1].start}},r={type:u>1?"strong":"emphasis",start:{...o.start},end:{...a.end}},e[n][1].end={...o.start},e[f][1].start={...a.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=(0,l.V)(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=(0,l.V)(s,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),s=(0,l.V)(s,(0,K.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,f),t)),s=(0,l.V)(s,[["exit",i,t],["enter",a,t],["exit",a,t],["exit",r,t]]),e[f][1].end.offset-e[f][1].start.offset?(c=2,s=(0,l.V)(s,[["enter",e[f][1],t],["exit",e[f][1],t]])):c=0,(0,l.m)(e,n-1,f-n+3,s),f=n+s.length-c-2;break}}for(f=-1;++f<e.length;)"attentionSequence"===e[f][1].type&&(e[f][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,l=(0,et.S)(i);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let u=e.exit("attentionSequence"),s=(0,et.S)(a),c=!s||2===s&&l||r.includes(a),f=!l||2===l&&s||r.includes(i);return u._open=!!(42===n?c:c&&(l||!f)),u._close=!!(42===n?f:f&&(s||!c)),t(a)}(o)}}};function er(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let ei={name:"labelStartLink",resolveAll:J.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},el={42:P,43:P,45:P,48:P,49:P,50:P,51:P,52:P,53:P,54:P,55:P,56:P,57:P,62:A},eo={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,O.call(i,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function l(t){return(r=(0,M.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return(0,f.Ee)(t)?F(e,a)(t):a(t)}function a(t){return D(e,u,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function u(t){return e.attempt(z,s,s)(t)}function s(t){return(0,f.On)(t)?(0,c.N)(e,p,"whitespace")(t):p(t)}function p(l){return null===l||(0,f.HP)(l)?(e.exit("definition"),i.parser.defined.push(r),t(l)):n(l)}}}},ea={[-2]:N,[-1]:N,32:N},eu={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},(0,l.m)(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var l;return e.enter("atxHeading"),l=i,e.enter("atxHeadingSequence"),function i(l){return 35===l&&r++<6?(e.consume(l),i):null===l||(0,f.Ee)(l)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,f.HP)(r)?(e.exit("atxHeading"),t(r)):(0,f.On)(r)?(0,c.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,f.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(l)):n(l)}(l)}}},42:E,45:[_,E],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,l,o,a,u=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),s};function s(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),i=!0,h):63===o?(e.consume(o),r=3,u.interrupt?t:F):(0,f.CW)(o)?(e.consume(o),l=String.fromCharCode(o),m):n(o)}function c(i){return 45===i?(e.consume(i),r=2,p):91===i?(e.consume(i),r=5,o=0,d):(0,f.CW)(i)?(e.consume(i),r=4,u.interrupt?t:F):n(i)}function p(r){return 45===r?(e.consume(r),u.interrupt?t:F):n(r)}function d(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?u.interrupt?t:E:d:n(r)}function h(t){return(0,f.CW)(t)?(e.consume(t),l=String.fromCharCode(t),m):n(t)}function m(o){if(null===o||47===o||62===o||(0,f.Ee)(o)){let a=47===o,s=l.toLowerCase();return!a&&!i&&j.includes(s)?(r=1,u.interrupt?t(o):E(o)):H.includes(l.toLowerCase())?(r=6,a)?(e.consume(o),g):u.interrupt?t(o):E(o):(r=7,u.interrupt&&!u.parser.lazy[u.now().line]?n(o):i?function t(n){return(0,f.On)(n)?(e.consume(n),t):S(n)}(o):y(o))}return 45===o||(0,f.lV)(o)?(e.consume(o),l+=String.fromCharCode(o),m):n(o)}function g(r){return 62===r?(e.consume(r),u.interrupt?t:E):n(r)}function y(t){return 47===t?(e.consume(t),S):58===t||95===t||(0,f.CW)(t)?(e.consume(t),k):(0,f.On)(t)?(e.consume(t),y):S(t)}function k(t){return 45===t||46===t||58===t||95===t||(0,f.lV)(t)?(e.consume(t),k):v(t)}function v(t){return 61===t?(e.consume(t),x):(0,f.On)(t)?(e.consume(t),v):y(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,b):(0,f.On)(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,f.Ee)(n)?v(n):(e.consume(n),t)}(t)}function b(t){return t===a?(e.consume(t),a=null,w):null===t||(0,f.HP)(t)?n(t):(e.consume(t),b)}function w(e){return 47===e||62===e||(0,f.On)(e)?y(e):n(e)}function S(t){return 62===t?(e.consume(t),C):n(t)}function C(t){return null===t||(0,f.HP)(t)?E(t):(0,f.On)(t)?(e.consume(t),C):n(t)}function E(t){return 45===t&&2===r?(e.consume(t),A):60===t&&1===r?(e.consume(t),D):62===t&&4===r?(e.consume(t),M):63===t&&3===r?(e.consume(t),F):93===t&&5===r?(e.consume(t),L):(0,f.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(B,z,P)(t)):null===t||(0,f.HP)(t)?(e.exit("htmlFlowData"),P(t)):(e.consume(t),E)}function P(t){return e.check(V,I,z)(t)}function I(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),T}function T(t){return null===t||(0,f.HP)(t)?P(t):(e.enter("htmlFlowData"),E(t))}function A(t){return 45===t?(e.consume(t),F):E(t)}function D(t){return 47===t?(e.consume(t),l="",O):E(t)}function O(t){if(62===t){let n=l.toLowerCase();return j.includes(n)?(e.consume(t),M):E(t)}return(0,f.CW)(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),O):E(t)}function L(t){return 93===t?(e.consume(t),F):E(t)}function F(t){return 62===t?(e.consume(t),M):45===t&&2===r?(e.consume(t),F):E(t)}function M(t){return null===t||(0,f.HP)(t)?(e.exit("htmlFlowData"),z(t)):(e.consume(t),M)}function z(n){return e.exit("htmlFlow"),t(n)}}},61:_,95:E,96:q,126:q},es={38:$,92:Y},ec={[-5]:Q,[-4]:Q,[-3]:Q,33:ee,38:$,42:en,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,f.CW)(t)?(e.consume(t),l):64===t?n(t):a(t)}function l(t){return 43===t||45===t||46===t||(0,f.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||(0,f.lV)(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,f.JQ)(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),u):(0,f.cx)(t)?(e.consume(t),a):n(t)}function u(i){return(0,f.lV)(i)?function i(l){return 46===l?(e.consume(l),r=0,u):62===l?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(l),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(l){if((45===l||(0,f.lV)(l))&&r++<63){let n=45===l?t:i;return e.consume(l),n}return n(l)}(l)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,l,o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),u):47===t?(e.consume(t),w):63===t?(e.consume(t),x):(0,f.CW)(t)?(e.consume(t),C):n(t)}function u(t){return 45===t?(e.consume(t),s):91===t?(e.consume(t),i=0,m):(0,f.CW)(t)?(e.consume(t),v):n(t)}function s(t){return 45===t?(e.consume(t),h):n(t)}function p(t){return null===t?n(t):45===t?(e.consume(t),d):(0,f.HP)(t)?(l=p,L(t)):(e.consume(t),p)}function d(t){return 45===t?(e.consume(t),h):p(t)}function h(e){return 62===e?O(e):45===e?d(e):p(e)}function m(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?g:m):n(t)}function g(t){return null===t?n(t):93===t?(e.consume(t),y):(0,f.HP)(t)?(l=g,L(t)):(e.consume(t),g)}function y(t){return 93===t?(e.consume(t),k):g(t)}function k(t){return 62===t?O(t):93===t?(e.consume(t),k):g(t)}function v(t){return null===t||62===t?O(t):(0,f.HP)(t)?(l=v,L(t)):(e.consume(t),v)}function x(t){return null===t?n(t):63===t?(e.consume(t),b):(0,f.HP)(t)?(l=x,L(t)):(e.consume(t),x)}function b(e){return 62===e?O(e):x(e)}function w(t){return(0,f.CW)(t)?(e.consume(t),S):n(t)}function S(t){return 45===t||(0,f.lV)(t)?(e.consume(t),S):function t(n){return(0,f.HP)(n)?(l=t,L(n)):(0,f.On)(n)?(e.consume(n),t):O(n)}(t)}function C(t){return 45===t||(0,f.lV)(t)?(e.consume(t),C):47===t||62===t||(0,f.Ee)(t)?E(t):n(t)}function E(t){return 47===t?(e.consume(t),O):58===t||95===t||(0,f.CW)(t)?(e.consume(t),P):(0,f.HP)(t)?(l=E,L(t)):(0,f.On)(t)?(e.consume(t),E):O(t)}function P(t){return 45===t||46===t||58===t||95===t||(0,f.lV)(t)?(e.consume(t),P):function t(n){return 61===n?(e.consume(n),I):(0,f.HP)(n)?(l=t,L(n)):(0,f.On)(n)?(e.consume(n),t):E(n)}(t)}function I(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,T):(0,f.HP)(t)?(l=I,L(t)):(0,f.On)(t)?(e.consume(t),I):(e.consume(t),A)}function T(t){return t===r?(e.consume(t),r=void 0,D):null===t?n(t):(0,f.HP)(t)?(l=T,L(t)):(e.consume(t),T)}function A(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,f.Ee)(t)?E(t):(e.consume(t),A)}function D(e){return 47===e||62===e||(0,f.Ee)(e)?E(e):n(e)}function O(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function L(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),F}function F(t){return(0,f.On)(t)?(0,c.N)(e,M,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):M(t)}function M(t){return e.enter("htmlTextData"),l(t)}}}],91:ei,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,f.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},Y],93:J,95:en,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,l=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),l++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(u){return null===u?n(u):32===u?(e.enter("space"),e.consume(u),e.exit("space"),o):96===u?(i=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===l?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(i.type="codeTextData",a(o))}(u)):(0,f.HP)(u)?(e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(u))}function a(t){return null===t||32===t||96===t||(0,f.HP)(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},ef={null:[en,v]},ep={null:[42,95]},ed={null:[]},eh=/[\0\t\n\r]/g;var em=n(55292),eg=n(55592),ey=n(16277);let ek={}.hasOwnProperty;function ev(e,t,n){let o,a,c,h;return"string"!=typeof t&&(n=t,t=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(k),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(m),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(h),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:r(h,l),codeText:r(function(){return{type:"inlineCode",value:""}},l),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(g),hardBreakTrailing:r(g),htmlFlow:r(y,l),htmlFlowData:c,htmlText:r(y,l),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:l,link:r(k),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(v,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(v),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:r(m),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:a(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:a(),autolinkEmail:function(e){f.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){f.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:a(),characterEscapeValue:f,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=(0,em.C)(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=(0,W.s)(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=ex(e.end)},codeFenced:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:f,codeIndented:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:f,data:f,definition:a(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,M.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:a(),hardBreakEscape:a(p),hardBreakTrailing:a(p),htmlFlow:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:f,htmlText:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:f,image:a(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=(0,eg.s)(t),n.identifier=(0,M.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=ex(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),f.call(this,e))},link:a(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,M.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:a(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:a(),thematicBreak:a()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(ek.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},i={stack:[r],tokenStack:[],config:t,enter:o,exit:u,buffer:l,resume:s,data:n},a=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?a.push(c):c=function(e,t,n){let r,i,l,o,a=t-1,u=-1,s=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?u++:u--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||u||l||(l=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!u&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===u&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(i=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",s=!0),t[1].type="lineEnding",i=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!i||l<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,l=void 0,o=!0}}}return e[t][1]._spread=s,n}(e,a.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];ek.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},i),e[c][1])}if(i.tokenStack.length>0){let e=i.tokenStack[i.tokenStack.length-1];(e[1]||eb).call(i,void 0,e[0])}for(r.position={start:ex(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:ex(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){o.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function o(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:ex(t.start),end:void 0}}function a(e){return function(t){e&&e.call(this,t),u.call(this,t)}}function u(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||eb).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+(0,ey.L)({start:e.start,end:e.end})+"): it’s not open");n.position.end=ex(e.end)}function s(){return(0,i.d)(this.stack.pop())}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:ex(e.start),end:void 0},t.push(n)),this.stack.push(n)}function f(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=ex(e.end)}function p(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function h(){return{type:"code",lang:null,meta:null,value:""}}function m(){return{type:"heading",depth:0,children:[]}}function g(){return{type:"break"}}function y(){return{type:"html",value:""}}function k(){return{type:"link",title:null,url:"",children:[]}}function v(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(n)(function(e){for(;!u(e););return e}((function(e){let t={constructs:(0,s.y)([r,...(e||{}).extensions||[]]),content:n(p),defined:[],document:n(d),flow:n(k),lazy:{},string:n(x),text:n(b)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],a=[],u=[],s={attempt:g(function(e,t){y(e,t.from)}),check:g(m),consume:function(e){(0,f.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,k()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=h(),c.events.push(["enter",n,c]),u.push(n),n},exit:function(e){let t=u.pop();return t.end=h(),c.events.push(["exit",t,c]),t},interrupt:g(m,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,k()},events:[],now:h,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let l,o=e[r];if("string"==typeof o)l=o;else switch(o){case -5:l="\r";break;case -4:l="\n";break;case -3:l="\r\n";break;case -2:l=t?" ":"	";break;case -1:if(!t&&n)continue;l=" ";break;default:l=String.fromCharCode(o)}n=-2===o,i.push(l)}return i.join("")}(d(e),t)},sliceStream:d,write:function(e){return(a=(0,l.V)(a,e),function(){let e;for(;r._index<a.length;){let n=a[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),p=p(t)}else p=p(n)}}(),null!==a[a.length-1])?[]:(y(t,0),c.events=(0,K.W)(o,c.events,c),c.events)}},p=t.tokenize.call(c,s);return t.resolveAll&&o.push(t),c;function d(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,l=t.end._index,o=t.end._bufferIndex;if(r===l)n=[e[r].slice(i,o)];else{if(n=e.slice(r,l),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}o>0&&n.push(e[l].slice(0,o))}return n}(a,e)}function h(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:l}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:l}}function m(e,t){t.restore()}function g(e,t){return function(n,i,l){var o;let a,f,p,d;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(o=n,function(e){let t=null!==e&&o[e],n=null!==e&&o.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(a=e,f=0,0===e.length)?l:g(e[f])}function g(e){return function(n){return(d=function(){let e=h(),t=c.previous,n=c.currentConstruct,i=c.events.length,l=Array.from(u);return{from:i,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,u=l,k()}}}(),p=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?v(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,s,y,v)(n)}}function y(t){return e(p,d),i}function v(e){return(d.restore(),++f<a.length)?g(a[f]):l}}}function y(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&(0,l.m)(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function k(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(n).document().write((a=1,c="",h=!0,function(e,t,n){let r,i,l,u,s,f=[];for(e=c+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),l=0,c="",h&&(65279===e.charCodeAt(0)&&l++,h=void 0);l<e.length;){if(eh.lastIndex=l,u=(r=eh.exec(e))&&void 0!==r.index?r.index:e.length,s=e.charCodeAt(u),!r){c=e.slice(l);break}if(10===s&&l===u&&o)f.push(-3),o=void 0;else switch(o&&(f.push(-5),o=void 0),l<u&&(f.push(e.slice(l,u)),a+=u-l),s){case 0:f.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),f.push(-2);a++<i;)f.push(-1);break;case 10:f.push(-4),a=1;break;default:o=!0,a=1}l=u+1}return n&&(o&&f.push(-5),c&&f.push(c),f.push(null)),f})(e,t,!0))))}function ex(e){return{line:e.line,column:e.column,offset:e.offset}}function eb(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+(0,ey.L)({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+(0,ey.L)({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+(0,ey.L)({start:t.start,end:t.end})+") is still open")}},99027:(e,t,n)=>{"use strict";n.d(t,{x:()=>o});var r=n(79168),i=n(3407);class l{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function o(){return{flow:{null:{name:"table",tokenize:a,resolveAll:u}}}}function a(e,t,n){let l,o=this,a=0,u=0;return function(e){let t=o.events.length-1;for(;t>-1;){let e=o.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?o.events[t][1].type:null,i="tableHead"===r||"tableRow"===r?x:s;return i===x&&o.parser.lazy[o.now().line]?n(e):i(e)};function s(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(l=!0,u+=1),c(n)}function c(t){return null===t?n(t):(0,i.HP)(t)?u>1?(u=0,o.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d):n(t):(0,i.On)(t)?(0,r.N)(e,c,"whitespace")(t):(u+=1,l&&(l=!1,a+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),l=!0,c):(e.enter("data"),f(t))}function f(t){return null===t||124===t||(0,i.Ee)(t)?(e.exit("data"),c(t)):(e.consume(t),92===t?p:f)}function p(t){return 92===t||124===t?(e.consume(t),f):f(t)}function d(t){return(o.interrupt=!1,o.parser.lazy[o.now().line])?n(t):(e.enter("tableDelimiterRow"),l=!1,(0,i.On)(t))?(0,r.N)(e,h,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):h(t)}function h(t){return 45===t||58===t?g(t):124===t?(l=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),m):n(t)}function m(t){return(0,i.On)(t)?(0,r.N)(e,g,"whitespace")(t):g(t)}function g(t){return 58===t?(u+=1,l=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),y):45===t?(u+=1,y(t)):null===t||(0,i.HP)(t)?v(t):n(t)}function y(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(l=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),k):(e.exit("tableDelimiterFiller"),k(n))}(t)):n(t)}function k(t){return(0,i.On)(t)?(0,r.N)(e,v,"whitespace")(t):v(t)}function v(r){if(124===r)return h(r);if(null===r||(0,i.HP)(r))return l&&a===u?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(r)):n(r);return n(r)}function x(t){return e.enter("tableRow"),b(t)}function b(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),b):null===n||(0,i.HP)(n)?(e.exit("tableRow"),t(n)):(0,i.On)(n)?(0,r.N)(e,b,"whitespace")(n):(e.enter("data"),w(n))}function w(t){return null===t||124===t||(0,i.Ee)(t)?(e.exit("data"),b(t)):(e.consume(t),92===t?S:w)}function S(t){return 92===t||124===t?(e.consume(t),w):w(t)}}function u(e,t){let n,r,i,o=-1,a=!0,u=0,f=[0,0,0,0],p=[0,0,0,0],d=!1,h=0,m=new l;for(;++o<e.length;){let l=e[o],g=l[1];"enter"===l[0]?"tableHead"===g.type?(d=!1,0!==h&&(c(m,t,h,n,r),r=void 0,h=0),n={type:"table",start:Object.assign({},g.start),end:Object.assign({},g.end)},m.add(o,0,[["enter",n,t]])):"tableRow"===g.type||"tableDelimiterRow"===g.type?(a=!0,i=void 0,f=[0,0,0,0],p=[0,o+1,0,0],d&&(d=!1,r={type:"tableBody",start:Object.assign({},g.start),end:Object.assign({},g.end)},m.add(o,0,[["enter",r,t]])),u="tableDelimiterRow"===g.type?2:r?3:1):u&&("data"===g.type||"tableDelimiterMarker"===g.type||"tableDelimiterFiller"===g.type)?(a=!1,0===p[2]&&(0!==f[1]&&(p[0]=p[1],i=s(m,t,f,u,void 0,i),f=[0,0,0,0]),p[2]=o)):"tableCellDivider"===g.type&&(a?a=!1:(0!==f[1]&&(p[0]=p[1],i=s(m,t,f,u,void 0,i)),p=[(f=p)[1],o,0,0])):"tableHead"===g.type?(d=!0,h=o):"tableRow"===g.type||"tableDelimiterRow"===g.type?(h=o,0!==f[1]?(p[0]=p[1],i=s(m,t,f,u,o,i)):0!==p[1]&&(i=s(m,t,p,u,o,i)),u=0):u&&("data"===g.type||"tableDelimiterMarker"===g.type||"tableDelimiterFiller"===g.type)&&(p[3]=o)}for(0!==h&&c(m,t,h,n,r),m.consume(t.events),o=-1;++o<t.events.length;){let e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,o))}return e}function s(e,t,n,r,i,l){0!==n[0]&&(l.end=Object.assign({},f(t.events,n[0])),e.add(n[0],0,[["exit",l,t]]));let o=f(t.events,n[1]);if(l={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},o),end:Object.assign({},o)},e.add(n[1],0,[["enter",l,t]]),0!==n[2]){let i=f(t.events,n[2]),l=f(t.events,n[3]),o={type:"tableContent",start:Object.assign({},i),end:Object.assign({},l)};if(e.add(n[2],0,[["enter",o,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",o,t]])}return void 0!==i&&(l.end=Object.assign({},f(t.events,i)),e.add(i,0,[["exit",l,t]]),l=void 0),l}function c(e,t,n,r,i){let l=[],o=f(t.events,n);i&&(i.end=Object.assign({},o),l.push(["exit",i,t])),r.end=Object.assign({},o),l.push(["exit",r,t]),e.add(n+1,0,l)}function f(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}}}]);