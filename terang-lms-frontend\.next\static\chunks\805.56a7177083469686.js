try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="822a7541-48b1-4f89-8227-837cae918e82",e._sentryDebugIdIdentifier="sentry-dbid-822a7541-48b1-4f89-8227-837cae918e82")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[805],{80805:(e,t,n)=>{n.r(t),n.d(t,{commonmarkLanguage:()=>ez,deleteMarkupBackward:()=>eK,insertNewlineContinueMarkup:()=>eQ,markdown:()=>e1,markdownKeymap:()=>eJ,markdownLanguage:()=>eF});var r,s=n(45395),i=n(88082),o=n(14563),l=n(3685),a=n(38348),h=n(21769);class f{static create(e,t,n,r,s){return new f(e,t,n,r+(r<<8)+e+(t<<4)|0,s,[],[])}constructor(e,t,n,r,s,i,o){this.type=e,this.value=t,this.from=n,this.hash=r,this.end=s,this.children=i,this.positions=o,this.hashProp=[[a.uY.contextHash,r]]}addChild(e,t){e.prop(a.uY.contextHash)!=this.hash&&(e=new a.PH(e.type,e.children,e.positions,e.length,this.hashProp)),this.children.push(e),this.positions.push(t)}toTree(e,t=this.end){let n=this.children.length-1;return n>=0&&(t=Math.max(t,this.positions[n]+this.children[n].length+this.from)),new a.PH(e.types[this.type],this.children,this.positions,t-this.from).balance({makeTree:(e,t,n)=>new a.PH(a.Z6.none,e,t,n,this.hashProp)})}}!function(e){e[e.Document=1]="Document",e[e.CodeBlock=2]="CodeBlock",e[e.FencedCode=3]="FencedCode",e[e.Blockquote=4]="Blockquote",e[e.HorizontalRule=5]="HorizontalRule",e[e.BulletList=6]="BulletList",e[e.OrderedList=7]="OrderedList",e[e.ListItem=8]="ListItem",e[e.ATXHeading1=9]="ATXHeading1",e[e.ATXHeading2=10]="ATXHeading2",e[e.ATXHeading3=11]="ATXHeading3",e[e.ATXHeading4=12]="ATXHeading4",e[e.ATXHeading5=13]="ATXHeading5",e[e.ATXHeading6=14]="ATXHeading6",e[e.SetextHeading1=15]="SetextHeading1",e[e.SetextHeading2=16]="SetextHeading2",e[e.HTMLBlock=17]="HTMLBlock",e[e.LinkReference=18]="LinkReference",e[e.Paragraph=19]="Paragraph",e[e.CommentBlock=20]="CommentBlock",e[e.ProcessingInstructionBlock=21]="ProcessingInstructionBlock",e[e.Escape=22]="Escape",e[e.Entity=23]="Entity",e[e.HardBreak=24]="HardBreak",e[e.Emphasis=25]="Emphasis",e[e.StrongEmphasis=26]="StrongEmphasis",e[e.Link=27]="Link",e[e.Image=28]="Image",e[e.InlineCode=29]="InlineCode",e[e.HTMLTag=30]="HTMLTag",e[e.Comment=31]="Comment",e[e.ProcessingInstruction=32]="ProcessingInstruction",e[e.Autolink=33]="Autolink",e[e.HeaderMark=34]="HeaderMark",e[e.QuoteMark=35]="QuoteMark",e[e.ListMark=36]="ListMark",e[e.LinkMark=37]="LinkMark",e[e.EmphasisMark=38]="EmphasisMark",e[e.CodeMark=39]="CodeMark",e[e.CodeText=40]="CodeText",e[e.CodeInfo=41]="CodeInfo",e[e.LinkTitle=42]="LinkTitle",e[e.LinkLabel=43]="LinkLabel",e[e.URL=44]="URL"}(r||(r={}));class u{constructor(e,t){this.start=e,this.content=t,this.marks=[],this.parsers=[]}}class d{constructor(){this.text="",this.baseIndent=0,this.basePos=0,this.depth=0,this.markers=[],this.pos=0,this.indent=0,this.next=-1}forward(){this.basePos>this.pos&&this.forwardInner()}forwardInner(){let e=this.skipSpace(this.basePos);this.indent=this.countIndent(e,this.pos,this.indent),this.pos=e,this.next=e==this.text.length?-1:this.text.charCodeAt(e)}skipSpace(e){return g(this.text,e)}reset(e){for(this.text=e,this.baseIndent=this.basePos=this.pos=this.indent=0,this.forwardInner(),this.depth=1;this.markers.length;)this.markers.pop()}moveBase(e){this.basePos=e,this.baseIndent=this.countIndent(e,this.pos,this.indent)}moveBaseColumn(e){this.baseIndent=e,this.basePos=this.findColumn(e)}addMarker(e){this.markers.push(e)}countIndent(e,t=0,n=0){for(let r=t;r<e;r++)n+=9==this.text.charCodeAt(r)?4-n%4:1;return n}findColumn(e){let t=0;for(let n=0;t<this.text.length&&n<e;t++)n+=9==this.text.charCodeAt(t)?4-n%4:1;return t}scrub(){if(!this.baseIndent)return this.text;let e="";for(let t=0;t<this.basePos;t++)e+=" ";return e+this.text.slice(this.basePos)}}function c(e,t,n){if(n.pos==n.text.length||e!=t.block&&n.indent>=t.stack[n.depth+1].value+n.baseIndent)return!0;if(n.indent>=n.baseIndent+4)return!1;let s=(e.type==r.OrderedList?y:S)(n,t,!1);return s>0&&(e.type!=r.BulletList||0>L(n,t,!1))&&n.text.charCodeAt(n.pos+s-1)==e.value}let p={[r.Blockquote]:(e,t,n)=>62==n.next&&(n.markers.push(Q(r.QuoteMark,t.lineStart+n.pos,t.lineStart+n.pos+1)),n.moveBase(n.pos+(m(n.text.charCodeAt(n.pos+1))?2:1)),e.end=t.lineStart+n.text.length,!0),[r.ListItem]:(e,t,n)=>(!(n.indent<n.baseIndent+e.value)||!(n.next>-1))&&(n.moveBaseColumn(n.baseIndent+e.value),!0),[r.OrderedList]:c,[r.BulletList]:c,[r.Document]:()=>!0};function m(e){return 32==e||9==e||10==e||13==e}function g(e,t=0){for(;t<e.length&&m(e.charCodeAt(t));)t++;return t}function k(e,t,n){for(;t>n&&m(e.charCodeAt(t-1));)t--;return t}function x(e){if(96!=e.next&&126!=e.next)return -1;let t=e.pos+1;for(;t<e.text.length&&e.text.charCodeAt(t)==e.next;)t++;if(t<e.pos+3)return -1;if(96==e.next){for(let n=t;n<e.text.length;n++)if(96==e.text.charCodeAt(n))return -1}return t}function b(e){return 62!=e.next?-1:32==e.text.charCodeAt(e.pos+1)?2:1}function L(e,t,n){if(42!=e.next&&45!=e.next&&95!=e.next)return -1;let r=1;for(let t=e.pos+1;t<e.text.length;t++){let n=e.text.charCodeAt(t);if(n==e.next)r++;else if(!m(n))return -1}return n&&45==e.next&&w(e)>-1&&e.depth==t.stack.length&&t.parser.leafBlockParsers.indexOf($.SetextHeading)>-1||r<3?-1:1}function A(e,t){for(let n=e.stack.length-1;n>=0;n--)if(e.stack[n].type==t)return!0;return!1}function S(e,t,n){return(45==e.next||43==e.next||42==e.next)&&(e.pos==e.text.length-1||m(e.text.charCodeAt(e.pos+1)))&&(!n||A(t,r.BulletList)||e.skipSpace(e.pos+2)<e.text.length)?1:-1}function y(e,t,n){let s=e.pos,i=e.next;for(;i>=48&&i<=57;){if(++s==e.text.length)return -1;i=e.text.charCodeAt(s)}return s==e.pos||s>e.pos+9||46!=i&&41!=i||s<e.text.length-1&&!m(e.text.charCodeAt(s+1))||n&&!A(t,r.OrderedList)&&(e.skipSpace(s+1)==e.text.length||s>e.pos+1||49!=e.next)?-1:s+1-e.pos}function C(e){if(35!=e.next)return -1;let t=e.pos+1;for(;t<e.text.length&&35==e.text.charCodeAt(t);)t++;if(t<e.text.length&&32!=e.text.charCodeAt(t))return -1;let n=t-e.pos;return n>6?-1:n}function w(e){if(45!=e.next&&61!=e.next||e.indent>=e.baseIndent+4)return -1;let t=e.pos+1;for(;t<e.text.length&&e.text.charCodeAt(t)==e.next;)t++;let n=t;for(;t<e.text.length&&m(e.text.charCodeAt(t));)t++;return t==e.text.length?n:-1}let I=/^[ \t]*$/,B=/-->/,T=/\?>/,E=[[/^<(?:script|pre|style)(?:\s|>|$)/i,/<\/(?:script|pre|style)>/i],[/^\s*<!--/,B],[/^\s*<\?/,T],[/^\s*<![A-Z]/,/>/],[/^\s*<!\[CDATA\[/,/\]\]>/],[/^\s*<\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\s|\/?>|$)/i,I],[/^\s*(?:<\/[a-z][\w-]*\s*>|<[a-z][\w-]*(\s+[a-z:_][\w-.]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*>)\s*$/i,I]];function H(e,t,n){if(60!=e.next)return -1;let r=e.text.slice(e.pos);for(let e=0,t=E.length-!!n;e<t;e++)if(E[e][0].test(r))return e;return -1}function v(e,t){let n=e.countIndent(t,e.pos,e.indent),r=e.countIndent(e.skipSpace(t),t,n);return r>=n+5?n+1:r}function M(e,t,n){let s=e.length-1;s>=0&&e[s].to==t&&e[s].type==r.CodeText?e[s].to=n:e.push(Q(r.CodeText,t,n))}let P={LinkReference:void 0,IndentedCode(e,t){let n=t.baseIndent+4;if(t.indent<n)return!1;let s=t.findColumn(n),i=e.lineStart+s,o=e.lineStart+t.text.length,l=[],a=[];for(M(l,i,o);e.nextLine()&&t.depth>=e.stack.length;)if(t.pos==t.text.length)for(let n of(M(a,e.lineStart-1,e.lineStart),t.markers))a.push(n);else if(t.indent<n)break;else{if(a.length){for(let e of a)e.type==r.CodeText?M(l,e.from,e.to):l.push(e);a=[]}for(let n of(M(l,e.lineStart-1,e.lineStart),t.markers))l.push(n);o=e.lineStart+t.text.length;let n=e.lineStart+t.findColumn(t.baseIndent+4);n<o&&M(l,n,o)}return a.length&&(a=a.filter(e=>e.type!=r.CodeText)).length&&(t.markers=a.concat(t.markers)),e.addNode(e.buffer.writeElements(l,-i).finish(r.CodeBlock,o-i),i),!0},FencedCode(e,t){let n=x(t);if(n<0)return!1;let s=e.lineStart+t.pos,i=t.next,o=n-t.pos,l=t.skipSpace(n),a=k(t.text,t.text.length,l),h=[Q(r.CodeMark,s,s+o)];l<a&&h.push(Q(r.CodeInfo,e.lineStart+l,e.lineStart+a));for(let n=!0;e.nextLine()&&t.depth>=e.stack.length;n=!1){let s=t.pos;if(t.indent-t.baseIndent<4)for(;s<t.text.length&&t.text.charCodeAt(s)==i;)s++;if(s-t.pos>=o&&t.skipSpace(s)==t.text.length){for(let e of t.markers)h.push(e);h.push(Q(r.CodeMark,e.lineStart+t.pos,e.lineStart+s)),e.nextLine();break}{for(let r of(n||M(h,e.lineStart-1,e.lineStart),t.markers))h.push(r);let r=e.lineStart+t.basePos,s=e.lineStart+t.text.length;r<s&&M(h,r,s)}}return e.addNode(e.buffer.writeElements(h,-s).finish(r.FencedCode,e.prevLineEnd()-s),s),!0},Blockquote(e,t){let n=b(t);return!(n<0)&&(e.startContext(r.Blockquote,t.pos),e.addNode(r.QuoteMark,e.lineStart+t.pos,e.lineStart+t.pos+1),t.moveBase(t.pos+n),null)},HorizontalRule(e,t){if(0>L(t,e,!1))return!1;let n=e.lineStart+t.pos;return e.nextLine(),e.addNode(r.HorizontalRule,n),!0},BulletList(e,t){let n=S(t,e,!1);if(n<0)return!1;e.block.type!=r.BulletList&&e.startContext(r.BulletList,t.basePos,t.next);let s=v(t,t.pos+1);return e.startContext(r.ListItem,t.basePos,s-t.baseIndent),e.addNode(r.ListMark,e.lineStart+t.pos,e.lineStart+t.pos+n),t.moveBaseColumn(s),null},OrderedList(e,t){let n=y(t,e,!1);if(n<0)return!1;e.block.type!=r.OrderedList&&e.startContext(r.OrderedList,t.basePos,t.text.charCodeAt(t.pos+n-1));let s=v(t,t.pos+n);return e.startContext(r.ListItem,t.basePos,s-t.baseIndent),e.addNode(r.ListMark,e.lineStart+t.pos,e.lineStart+t.pos+n),t.moveBaseColumn(s),null},ATXHeading(e,t){let n=C(t);if(n<0)return!1;let s=t.pos,i=e.lineStart+s,o=k(t.text,t.text.length,s),l=o;for(;l>s&&t.text.charCodeAt(l-1)==t.next;)l--;l!=o&&l!=s&&m(t.text.charCodeAt(l-1))||(l=t.text.length);let a=e.buffer.write(r.HeaderMark,0,n).writeElements(e.parser.parseInline(t.text.slice(s+n+1,l),i+n+1),-i);l<t.text.length&&a.write(r.HeaderMark,l-s,o-s);let h=a.finish(r.ATXHeading1-1+n,t.text.length-s);return e.nextLine(),e.addNode(h,i),!0},HTMLBlock(e,t){let n=H(t,e,!1);if(n<0)return!1;let s=e.lineStart+t.pos,i=E[n][1],o=[],l=i!=I;for(;!i.test(t.text)&&e.nextLine();){if(t.depth<e.stack.length){l=!1;break}for(let e of t.markers)o.push(e)}l&&e.nextLine();let a=i==B?r.CommentBlock:i==T?r.ProcessingInstructionBlock:r.HTMLBlock,h=e.prevLineEnd();return e.addNode(e.buffer.writeElements(o,-s).finish(a,h-s),s),!0},SetextHeading:void 0};class _{constructor(e){this.stage=0,this.elts=[],this.pos=0,this.start=e.start,this.advance(e.content)}nextLine(e,t,n){if(-1==this.stage)return!1;let r=n.content+"\n"+t.scrub(),s=this.advance(r);return s>-1&&s<r.length&&this.complete(e,n,s)}finish(e,t){return(2==this.stage||3==this.stage)&&g(t.content,this.pos)==t.content.length&&this.complete(e,t,t.content.length)}complete(e,t,n){return e.addLeafElement(t,Q(r.LinkReference,this.start,this.start+n,this.elts)),!0}nextStage(e){return e?(this.pos=e.to-this.start,this.elts.push(e),this.stage++,!0):(!1===e&&(this.stage=-1),!1)}advance(e){for(;;)if(-1==this.stage)return -1;else if(0==this.stage){if(!this.nextStage(ei(e,this.pos,this.start,!0)))return -1;if(58!=e.charCodeAt(this.pos))return this.stage=-1;this.elts.push(Q(r.LinkMark,this.pos+this.start,this.pos+this.start+1)),this.pos++}else if(1==this.stage){if(!this.nextStage(er(e,g(e,this.pos),this.start)))return -1}else{if(2!=this.stage)return O(e,this.pos);let t=g(e,this.pos),n=0;if(t>this.pos){let r=es(e,t,this.start);if(r){let t=O(e,r.to-this.start);t>0&&(this.nextStage(r),n=t)}}return n||(n=O(e,this.pos)),n>0&&n<e.length?n:-1}}}function O(e,t){for(;t<e.length;t++){let n=e.charCodeAt(t);if(10==n)break;if(!m(n))return -1}return t}class N{nextLine(e,t,n){let s=t.depth<e.stack.length?-1:w(t),i=t.next;if(s<0)return!1;let o=Q(r.HeaderMark,e.lineStart+t.pos,e.lineStart+s);return e.nextLine(),e.addLeafElement(n,Q(61==i?r.SetextHeading1:r.SetextHeading2,n.start,e.prevLineEnd(),[...e.parser.parseInline(n.content,n.start),o])),!0}finish(){return!1}}let $={LinkReference:(e,t)=>91==t.content.charCodeAt(0)?new _(t):null,SetextHeading:()=>new N},R={text:"",end:0};class X{constructor(e,t,n,s){this.parser=e,this.input=t,this.ranges=s,this.line=new d,this.atEnd=!1,this.reusePlaceholders=new Map,this.stoppedAt=null,this.rangeI=0,this.to=s[s.length-1].to,this.lineStart=this.absoluteLineStart=this.absoluteLineEnd=s[0].from,this.block=f.create(r.Document,0,this.lineStart,0,0),this.stack=[this.block],this.fragments=n.length?new eh(n,t):null,this.readLine()}get parsedPos(){return this.absoluteLineStart}advance(){if(null!=this.stoppedAt&&this.absoluteLineStart>this.stoppedAt)return this.finish();let{line:e}=this;for(;;){for(let t=0;;){let n=e.depth<this.stack.length?this.stack[this.stack.length-1]:null;for(;t<e.markers.length&&(!n||e.markers[t].from<n.end);){let n=e.markers[t++];this.addNode(n.type,n.from,n.to)}if(!n)break;this.finishContext()}if(e.pos<e.text.length)break;if(!this.nextLine())return this.finish()}if(this.fragments&&this.reuseFragment(e.basePos))return null;e:for(;;){for(let t of this.parser.blockParsers)if(t){let n=t(this,e);if(!1!=n){if(!0==n)return null;e.forward();continue e}}break}let t=new u(this.lineStart+e.pos,e.text.slice(e.pos));for(let e of this.parser.leafBlockParsers)if(e){let n=e(this,t);n&&t.parsers.push(n)}t:for(;this.nextLine()&&e.pos!=e.text.length;){if(e.indent<e.baseIndent+4){for(let n of this.parser.endLeafBlock)if(n(this,e,t))break t}for(let n of t.parsers)if(n.nextLine(this,e,t))return null;for(let n of(t.content+="\n"+e.scrub(),e.markers))t.marks.push(n)}return this.finishLeaf(t),null}stopAt(e){if(null!=this.stoppedAt&&this.stoppedAt<e)throw RangeError("Can't move stoppedAt forward");this.stoppedAt=e}reuseFragment(e){if(!this.fragments.moveTo(this.absoluteLineStart+e,this.absoluteLineStart)||!this.fragments.matches(this.block.hash))return!1;let t=this.fragments.takeNodes(this);return!!t&&(this.absoluteLineStart+=t,this.lineStart=ef(this.absoluteLineStart,this.ranges),this.moveRangeI(),this.absoluteLineStart<this.to?(this.lineStart++,this.absoluteLineStart++):this.atEnd=!0,this.readLine(),!0)}get depth(){return this.stack.length}parentType(e=this.depth-1){return this.parser.nodeSet.types[this.stack[e].type]}nextLine(){return(this.lineStart+=this.line.text.length,this.absoluteLineEnd>=this.to)?(this.absoluteLineStart=this.absoluteLineEnd,this.atEnd=!0,this.readLine(),!1):(this.lineStart++,this.absoluteLineStart=this.absoluteLineEnd+1,this.moveRangeI(),this.readLine(),!0)}peekLine(){return this.scanLine(this.absoluteLineEnd+1).text}moveRangeI(){for(;this.rangeI<this.ranges.length-1&&this.absoluteLineStart>=this.ranges[this.rangeI].to;)this.rangeI++,this.absoluteLineStart=Math.max(this.absoluteLineStart,this.ranges[this.rangeI].from)}scanLine(e){if(R.end=e,e>=this.to)R.text="";else if(R.text=this.lineChunkAt(e),R.end+=R.text.length,this.ranges.length>1){let e=this.absoluteLineStart,t=this.rangeI;for(;this.ranges[t].to<R.end;){t++;let n=this.ranges[t].from,r=this.lineChunkAt(n);R.end=n+r.length,R.text=R.text.slice(0,this.ranges[t-1].to-e)+r,e=R.end-R.text.length}}return R}readLine(){let{line:e}=this,{text:t,end:n}=this.scanLine(this.absoluteLineStart);for(this.absoluteLineEnd=n,e.reset(t);e.depth<this.stack.length;e.depth++){let t=this.stack[e.depth],n=this.parser.skipContextMarkup[t.type];if(!n)throw Error("Unhandled block context "+r[t.type]);if(!n(t,this,e))break;e.forward()}}lineChunkAt(e){let t=this.input.chunk(e),n;if(this.input.lineChunks)n="\n"==t?"":t;else{let e=t.indexOf("\n");n=e<0?t:t.slice(0,e)}return e+n.length>this.to?n.slice(0,this.to-e):n}prevLineEnd(){return this.atEnd?this.lineStart:this.lineStart-1}startContext(e,t,n=0){this.block=f.create(e,n,this.lineStart+t,this.block.hash,this.lineStart+this.line.text.length),this.stack.push(this.block)}startComposite(e,t,n=0){this.startContext(this.parser.getNodeType(e),t,n)}addNode(e,t,n){"number"==typeof e&&(e=new a.PH(this.parser.nodeSet.types[e],j,j,(null!=n?n:this.prevLineEnd())-t)),this.block.addChild(e,t-this.block.from)}addElement(e){this.block.addChild(e.toTree(this.parser.nodeSet),e.from-this.block.from)}addLeafElement(e,t){this.addNode(this.buffer.writeElements(el(t.children,e.marks),-t.from).finish(t.type,t.to-t.from),t.from)}finishContext(){let e=this.stack.pop(),t=this.stack[this.stack.length-1];t.addChild(e.toTree(this.parser.nodeSet),e.from-t.from),this.block=t}finish(){for(;this.stack.length>1;)this.finishContext();return this.addGaps(this.block.toTree(this.parser.nodeSet,this.lineStart))}addGaps(e){return this.ranges.length>1?function e(t,n,r,s,i){let o=t[n].to,l=[],h=[],f=r.from+s;function u(e,r){for(;r?e>=o:e>o;){let r=t[n+1].from-o;s+=r,e+=r,o=t[++n].to}}for(let a=r.firstChild;a;a=a.nextSibling){u(a.from+s,!0);let r=a.from+s,d,c=i.get(a.tree);c?d=c:a.to+s>o?(d=e(t,n,a,s,i),u(a.to+s,!1)):d=a.toTree(),l.push(d),h.push(r-f)}return u(r.to+s,!1),new a.PH(r.type,l,h,r.to+s-f,r.tree?r.tree.propValues:void 0)}(this.ranges,0,e.topNode,this.ranges[0].from,this.reusePlaceholders):e}finishLeaf(e){for(let t of e.parsers)if(t.finish(this,e))return;let t=el(this.parser.parseInline(e.content,e.start),e.marks);this.addNode(this.buffer.writeElements(t,-e.start).finish(r.Paragraph,e.content.length),e.start)}elt(e,t,n,r){return"string"==typeof e?Q(this.parser.getNodeType(e),t,n,r):new Y(e,t)}get buffer(){return new Z(this.parser.nodeSet)}}class D extends a.iX{constructor(e,t,n,r,s,i,o,l,a){for(let h of(super(),this.nodeSet=e,this.blockParsers=t,this.leafBlockParsers=n,this.blockNames=r,this.endLeafBlock=s,this.skipContextMarkup=i,this.inlineParsers=o,this.inlineNames=l,this.wrappers=a,this.nodeTypes=Object.create(null),e.types))this.nodeTypes[h.name]=h.id}createParse(e,t,n){let r=new X(this,e,t,n);for(let s of this.wrappers)r=s(r,e,t,n);return r}configure(e){let t=function e(t){if(!Array.isArray(t))return t;if(0==t.length)return null;let n=e(t[0]);if(1==t.length)return n;let r=e(t.slice(1));if(!r||!n)return n||r;let s=(e,t)=>(e||j).concat(t||j),i=n.wrap,o=r.wrap;return{props:s(n.props,r.props),defineNodes:s(n.defineNodes,r.defineNodes),parseBlock:s(n.parseBlock,r.parseBlock),parseInline:s(n.parseInline,r.parseInline),remove:s(n.remove,r.remove),wrap:i?o?(e,t,n,r)=>i(o(e,t,n,r),t,n,r):i:o}}(e);if(!t)return this;let{nodeSet:n,skipContextMarkup:s}=this,i=this.blockParsers.slice(),o=this.leafBlockParsers.slice(),l=this.blockNames.slice(),f=this.inlineParsers.slice(),u=this.inlineNames.slice(),d=this.endLeafBlock.slice(),c=this.wrappers;if(z(t.defineNodes)){s=Object.assign({},s);let e=n.types.slice(),i;for(let n of t.defineNodes){let{name:t,block:o,composite:l,style:f}="string"==typeof n?{name:n}:n;if(e.some(e=>e.name==t))continue;l&&(s[e.length]=(e,t,n)=>l(t,n,e.value));let u=e.length,d=l?["Block","BlockContext"]:o?u>=r.ATXHeading1&&u<=r.SetextHeading2?["Block","LeafBlock","Heading"]:["Block","LeafBlock"]:void 0;e.push(a.Z6.define({id:u,name:t,props:d&&[[a.uY.group,d]]})),f&&(i||(i={}),Array.isArray(f)||f instanceof h.vw?i[t]=f:Object.assign(i,f))}n=new a.fI(e),i&&(n=n.extend((0,h.pn)(i)))}if(z(t.props)&&(n=n.extend(...t.props)),z(t.remove))for(let e of t.remove){let t=this.blockNames.indexOf(e),n=this.inlineNames.indexOf(e);t>-1&&(i[t]=o[t]=void 0),n>-1&&(f[n]=void 0)}if(z(t.parseBlock))for(let e of t.parseBlock){let t=l.indexOf(e.name);if(t>-1)i[t]=e.parse,o[t]=e.leaf;else{let t=e.before?F(l,e.before):e.after?F(l,e.after)+1:l.length-1;i.splice(t,0,e.parse),o.splice(t,0,e.leaf),l.splice(t,0,e.name)}e.endLeaf&&d.push(e.endLeaf)}if(z(t.parseInline))for(let e of t.parseInline){let t=u.indexOf(e.name);if(t>-1)f[t]=e.parse;else{let t=e.before?F(u,e.before):e.after?F(u,e.after)+1:u.length-1;f.splice(t,0,e.parse),u.splice(t,0,e.name)}}return t.wrap&&(c=c.concat(t.wrap)),new D(n,i,o,l,d,s,f,u,c)}getNodeType(e){let t=this.nodeTypes[e];if(null==t)throw RangeError(`Unknown node type '${e}'`);return t}parseInline(e,t){let n=new eo(this,e,t);n:for(let e=t;e<n.end;){let t=n.char(e);for(let r of this.inlineParsers)if(r){let s=r(n,t,e);if(s>=0){e=s;continue n}}e++}return n.resolveMarkers(0)}}function z(e){return null!=e&&e.length>0}function F(e,t){let n=e.indexOf(t);if(n<0)throw RangeError(`Position specified relative to unknown parser ${t}`);return n}let q=[a.Z6.none];for(let e=1,t;t=r[e];e++)q[e]=a.Z6.define({id:e,name:t,props:e>=r.Escape?[]:[[a.uY.group,e in p?["Block","BlockContext"]:["Block","LeafBlock"]]],top:"Document"==t});let j=[];class Z{constructor(e){this.nodeSet=e,this.content=[],this.nodes=[]}write(e,t,n,r=0){return this.content.push(e,t,n,4+4*r),this}writeElements(e,t=0){for(let n of e)n.writeTo(this,t);return this}finish(e,t){return a.PH.build({buffer:this.content,nodeSet:this.nodeSet,reused:this.nodes,topID:e,length:t})}}class U{constructor(e,t,n,r=j){this.type=e,this.from=t,this.to=n,this.children=r}writeTo(e,t){let n=e.content.length;e.writeElements(this.children,t),e.content.push(this.type,this.from+t,this.to+t,e.content.length+4-n)}toTree(e){return new Z(e).writeElements(this.children,-this.from).finish(this.type,this.to-this.from)}}class Y{constructor(e,t){this.tree=e,this.from=t}get to(){return this.from+this.tree.length}get type(){return this.tree.type.id}get children(){return j}writeTo(e,t){e.nodes.push(this.tree),e.content.push(e.nodes.length-1,this.from+t,this.to+t,-1)}toTree(){return this.tree}}function Q(e,t,n,r){return new U(e,t,n,r)}let V={resolve:"Emphasis",mark:"EmphasisMark"},G={resolve:"Emphasis",mark:"EmphasisMark"},K={},J={};class W{constructor(e,t,n,r){this.type=e,this.from=t,this.to=n,this.side=r}}let ee="!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~",et=/[!"#$%&'()*+,\-.\/:;<=>?@\[\\\]^_`{|}~\xA1\u2010-\u2027]/;try{et=RegExp("[\\p{S}|\\p{P}]","u")}catch(e){}let en={Escape(e,t,n){if(92!=t||n==e.end-1)return -1;let s=e.char(n+1);for(let t=0;t<ee.length;t++)if(ee.charCodeAt(t)==s)return e.append(Q(r.Escape,n,n+2));return -1},Entity(e,t,n){if(38!=t)return -1;let s=/^(?:#\d+|#x[a-f\d]+|\w+);/i.exec(e.slice(n+1,n+31));return s?e.append(Q(r.Entity,n,n+1+s[0].length)):-1},InlineCode(e,t,n){if(96!=t||n&&96==e.char(n-1))return -1;let s=n+1;for(;s<e.end&&96==e.char(s);)s++;let i=s-n,o=0;for(;s<e.end;s++)if(96==e.char(s)){if(++o==i&&96!=e.char(s+1))return e.append(Q(r.InlineCode,n,s+1,[Q(r.CodeMark,n,n+i),Q(r.CodeMark,s+1-i,s+1)]))}else o=0;return -1},HTMLTag(e,t,n){if(60!=t||n==e.end-1)return -1;let s=e.slice(n+1,e.end),i=/^(?:[a-z][-\w+.]+:[^\s>]+|[a-z\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?(?:\.[a-z\d](?:[a-z\d-]{0,61}[a-z\d])?)*)>/i.exec(s);if(i)return e.append(Q(r.Autolink,n,n+1+i[0].length,[Q(r.LinkMark,n,n+1),Q(r.URL,n+1,n+i[0].length),Q(r.LinkMark,n+i[0].length,n+1+i[0].length)]));let o=/^!--[^>](?:-[^-]|[^-])*?-->/i.exec(s);if(o)return e.append(Q(r.Comment,n,n+1+o[0].length));let l=/^\?[^]*?\?>/.exec(s);if(l)return e.append(Q(r.ProcessingInstruction,n,n+1+l[0].length));let a=/^(?:![A-Z][^]*?>|!\[CDATA\[[^]*?\]\]>|\/\s*[a-zA-Z][\w-]*\s*>|\s*[a-zA-Z][\w-]*(\s+[a-zA-Z:_][\w-.:]*(?:\s*=\s*(?:[^\s"'=<>`]+|'[^']*'|"[^"]*"))?)*\s*(\/\s*)?>)/.exec(s);return a?e.append(Q(r.HTMLTag,n,n+1+a[0].length)):-1},Emphasis(e,t,n){if(95!=t&&42!=t)return -1;let r=n+1;for(;e.char(r)==t;)r++;let s=e.slice(n-1,n),i=e.slice(r,r+1),o=et.test(s),l=et.test(i),a=/\s|^$/.test(s),h=/\s|^$/.test(i),f=!h&&(!l||a||o),u=!a&&(!o||h||l);return e.append(new W(95==t?V:G,n,r,!!(f&&(42==t||!u||o))|2*!!(u&&(42==t||!f||l))))},HardBreak(e,t,n){if(92==t&&10==e.char(n+1))return e.append(Q(r.HardBreak,n,n+2));if(32==t){let t=n+1;for(;32==e.char(t);)t++;if(10==e.char(t)&&t>=n+2)return e.append(Q(r.HardBreak,n,t+1))}return -1},Link:(e,t,n)=>91==t?e.append(new W(K,n,n+1,1)):-1,Image:(e,t,n)=>33==t&&91==e.char(n+1)?e.append(new W(J,n,n+2,1)):-1,LinkEnd(e,t,n){if(93!=t)return -1;for(let t=e.parts.length-1;t>=0;t--){let s=e.parts[t];if(s instanceof W&&(s.type==K||s.type==J)){if(!s.side||e.skipSpace(s.to)==n&&!/[(\[]/.test(e.slice(n+1,n+2)))return e.parts[t]=null,-1;let i=e.takeContent(t),o=e.parts[t]=function(e,t,n,s,i){let{text:o}=e,l=e.char(i),a=i;if(t.unshift(Q(r.LinkMark,s,s+(n==r.Image?2:1))),t.push(Q(r.LinkMark,i-1,i)),40==l){let n=e.skipSpace(i+1),s=er(o,n-e.offset,e.offset),l;s&&(n=e.skipSpace(s.to))!=s.to&&(l=es(o,n-e.offset,e.offset))&&(n=e.skipSpace(l.to)),41==e.char(n)&&(t.push(Q(r.LinkMark,i,i+1)),a=n+1,s&&t.push(s),l&&t.push(l),t.push(Q(r.LinkMark,n,a)))}else if(91==l){let n=ei(o,i-e.offset,e.offset,!1);n&&(t.push(n),a=n.to)}return Q(n,s,a,t)}(e,i,s.type==K?r.Link:r.Image,s.from,n+1);if(s.type==K)for(let n=0;n<t;n++){let t=e.parts[n];t instanceof W&&t.type==K&&(t.side=0)}return o.to}}return -1}};function er(e,t,n){if(60==e.charCodeAt(t)){for(let s=t+1;s<e.length;s++){let i=e.charCodeAt(s);if(62==i)return Q(r.URL,t+n,s+1+n);if(60==i||10==i)return!1}return null}{let s=0,i=t;for(let t=!1;i<e.length;i++){let n=e.charCodeAt(i);if(m(n))break;if(t)t=!1;else if(40==n)s++;else if(41==n){if(!s)break;s--}else 92==n&&(t=!0)}return i>t?Q(r.URL,t+n,i+n):i==e.length&&null}}function es(e,t,n){let s=e.charCodeAt(t);if(39!=s&&34!=s&&40!=s)return!1;let i=40==s?41:s;for(let s=t+1,o=!1;s<e.length;s++){let l=e.charCodeAt(s);if(o)o=!1;else{if(l==i)return Q(r.LinkTitle,t+n,s+1+n);92==l&&(o=!0)}}return null}function ei(e,t,n,s){for(let i=!1,o=t+1,l=Math.min(e.length,o+999);o<l;o++){let l=e.charCodeAt(o);if(i)i=!1;else{if(93==l)return!s&&Q(r.LinkLabel,t+n,o+1+n);if(s&&!m(l)&&(s=!1),91==l)return!1;92==l&&(i=!0)}}return null}class eo{constructor(e,t,n){this.parser=e,this.text=t,this.offset=n,this.parts=[]}char(e){return e>=this.end?-1:this.text.charCodeAt(e-this.offset)}get end(){return this.offset+this.text.length}slice(e,t){return this.text.slice(e-this.offset,t-this.offset)}append(e){return this.parts.push(e),e.to}addDelimiter(e,t,n,r,s){return this.append(new W(e,t,n,!!r|2*!!s))}get hasOpenLink(){for(let e=this.parts.length-1;e>=0;e--){let t=this.parts[e];if(t instanceof W&&(t.type==K||t.type==J))return!0}return!1}addElement(e){return this.append(e)}resolveMarkers(e){for(let t=e;t<this.parts.length;t++){let n=this.parts[t];if(!(n instanceof W&&n.type.resolve&&2&n.side))continue;let r=n.type==V||n.type==G,s=n.to-n.from,i,o=t-1;for(;o>=e;o--){let e=this.parts[o];if(e instanceof W&&1&e.side&&e.type==n.type&&!(r&&(1&n.side||2&e.side)&&(e.to-e.from+s)%3==0&&((e.to-e.from)%3||s%3))){i=e;break}}if(!i)continue;let l=n.type.resolve,a=[],h=i.from,f=n.to;if(r){let e=Math.min(2,i.to-i.from,s);h=i.to-e,f=n.from+e,l=1==e?"Emphasis":"StrongEmphasis"}i.type.mark&&a.push(this.elt(i.type.mark,h,i.to));for(let e=o+1;e<t;e++)this.parts[e]instanceof U&&a.push(this.parts[e]),this.parts[e]=null;n.type.mark&&a.push(this.elt(n.type.mark,n.from,f));let u=this.elt(l,h,f,a);this.parts[o]=r&&i.from!=h?new W(i.type,i.from,h,i.side):null,(this.parts[t]=r&&n.to!=f?new W(n.type,f,n.to,n.side):null)?this.parts.splice(t,0,u):this.parts[t]=u}let t=[];for(let n=e;n<this.parts.length;n++){let e=this.parts[n];e instanceof U&&t.push(e)}return t}findOpeningDelimiter(e){for(let t=this.parts.length-1;t>=0;t--){let n=this.parts[t];if(n instanceof W&&n.type==e)return t}return null}takeContent(e){let t=this.resolveMarkers(e);return this.parts.length=e,t}skipSpace(e){return g(this.text,e-this.offset)+this.offset}elt(e,t,n,r){return"string"==typeof e?Q(this.parser.getNodeType(e),t,n,r):new Y(e,t)}}function el(e,t){if(!t.length)return e;if(!e.length)return t;let n=e.slice(),r=0;for(let e of t){for(;r<n.length&&n[r].to<e.to;)r++;if(r<n.length&&n[r].from<e.from){let t=n[r];t instanceof U&&(n[r]=new U(t.type,t.from,t.to,el(t.children,[e])))}else n.splice(r++,0,e)}return n}let ea=[r.CodeBlock,r.ListItem,r.OrderedList,r.BulletList];class eh{constructor(e,t){this.fragments=e,this.input=t,this.i=0,this.fragment=null,this.fragmentEnd=-1,this.cursor=null,e.length&&(this.fragment=e[this.i++])}nextFragment(){this.fragment=this.i<this.fragments.length?this.fragments[this.i++]:null,this.cursor=null,this.fragmentEnd=-1}moveTo(e,t){for(;this.fragment&&this.fragment.to<=e;)this.nextFragment();if(!this.fragment||this.fragment.from>(e?e-1:0))return!1;if(this.fragmentEnd<0){let e=this.fragment.to;for(;e>0&&"\n"!=this.input.read(e-1,e);)e--;this.fragmentEnd=e?e-1:0}let n=this.cursor;n||(n=this.cursor=this.fragment.tree.cursor()).firstChild();let r=e+this.fragment.offset;for(;n.to<=r;)if(!n.parent())return!1;for(;;){if(n.from>=r)return this.fragment.from<=t;if(!n.childAfter(r))return!1}}matches(e){let t=this.cursor.tree;return t&&t.prop(a.uY.contextHash)==e}takeNodes(e){let t=this.cursor,n=this.fragment.offset,s=this.fragmentEnd-!!this.fragment.openEnd,i=e.absoluteLineStart,o=i,l=e.block.children.length,h=o,f=l;for(;;){if(t.to-n>s){if(t.type.isAnonymous&&t.firstChild())continue;break}let i=ef(t.from-n,e.ranges);if(t.to-n<=e.ranges[e.rangeI].to)e.addNode(t.tree,i);else{let n=new a.PH(e.parser.nodeSet.types[r.Paragraph],[],[],0,e.block.hashProp);e.reusePlaceholders.set(n,t.tree),e.addNode(n,i)}if(t.type.is("Block")&&(0>ea.indexOf(t.type.id)?(o=t.to-n,l=e.block.children.length):(o=h,l=f,h=t.to-n,f=e.block.children.length)),!t.nextSibling())break}for(;e.block.children.length>l;)e.block.children.pop(),e.block.positions.pop();return o-i}}function ef(e,t){let n=e;for(let r=1;r<t.length;r++){let s=t[r-1].to,i=t[r].from;s<e&&(n-=i-s)}return n}let eu=(0,h.pn)({"Blockquote/...":h._A.quote,HorizontalRule:h._A.contentSeparator,"ATXHeading1/... SetextHeading1/...":h._A.heading1,"ATXHeading2/... SetextHeading2/...":h._A.heading2,"ATXHeading3/...":h._A.heading3,"ATXHeading4/...":h._A.heading4,"ATXHeading5/...":h._A.heading5,"ATXHeading6/...":h._A.heading6,"Comment CommentBlock":h._A.comment,Escape:h._A.escape,Entity:h._A.character,"Emphasis/...":h._A.emphasis,"StrongEmphasis/...":h._A.strong,"Link/... Image/...":h._A.link,"OrderedList/... BulletList/...":h._A.list,"BlockQuote/...":h._A.quote,"InlineCode CodeText":h._A.monospace,"URL Autolink":h._A.url,"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark":h._A.processingInstruction,"CodeInfo LinkLabel":h._A.labelName,LinkTitle:h._A.string,Paragraph:h._A.content}),ed=new D(new a.fI(q).extend(eu),Object.keys(P).map(e=>P[e]),Object.keys(P).map(e=>$[e]),Object.keys(P),[(e,t)=>C(t)>=0,(e,t)=>x(t)>=0,(e,t)=>b(t)>=0,(e,t)=>S(t,e,!0)>=0,(e,t)=>y(t,e,!0)>=0,(e,t)=>L(t,e,!0)>=0,(e,t)=>H(t,e,!0)>=0],p,Object.keys(en).map(e=>en[e]),Object.keys(en),[]),ec={resolve:"Strikethrough",mark:"StrikethroughMark"},ep={defineNodes:[{name:"Strikethrough",style:{"Strikethrough/...":h._A.strikethrough}},{name:"StrikethroughMark",style:h._A.processingInstruction}],parseInline:[{name:"Strikethrough",parse(e,t,n){if(126!=t||126!=e.char(n+1)||126==e.char(n+2))return -1;let r=e.slice(n-1,n),s=e.slice(n+2,n+3),i=/\s|^$/.test(r),o=/\s|^$/.test(s),l=et.test(r),a=et.test(s);return e.addDelimiter(ec,n,n+2,!o&&(!a||i||l),!i&&(!l||o||a))},after:"Emphasis"}]};function em(e,t,n=0,r,s=0){let i=0,o=!0,l=-1,a=-1,h=!1,f=()=>{r.push(e.elt("TableCell",s+l,s+a,e.parser.parseInline(t.slice(l,a),s+l)))};for(let u=n;u<t.length;u++){let n=t.charCodeAt(u);124!=n||h?(h||32!=n&&9!=n)&&(l<0&&(l=u),a=u+1):((!o||l>-1)&&i++,o=!1,r&&(l>-1&&f(),r.push(e.elt("TableDelimiter",u+s,u+s+1))),l=a=-1),h=!h&&92==n}return l>-1&&(i++,r&&f()),i}function eg(e,t){for(let n=t;n<e.length;n++){let t=e.charCodeAt(n);if(124==t)return!0;92==t&&n++}return!1}let ek=/^\|?(\s*:?-+:?\s*\|)+(\s*:?-+:?\s*)?$/;class ex{constructor(){this.rows=null}nextLine(e,t,n){if(null==this.rows){let r;if(this.rows=!1,(45==t.next||58==t.next||124==t.next)&&ek.test(r=t.text.slice(t.pos))){let s=[];em(e,n.content,0,s,n.start)==em(e,r,t.pos)&&(this.rows=[e.elt("TableHeader",n.start,n.start+n.content.length,s),e.elt("TableDelimiter",e.lineStart+t.pos,e.lineStart+t.text.length)])}}else if(this.rows){let n=[];em(e,t.text,t.pos,n,e.lineStart),this.rows.push(e.elt("TableRow",e.lineStart+t.pos,e.lineStart+t.text.length,n))}return!1}finish(e,t){return!!this.rows&&(e.addLeafElement(t,e.elt("Table",t.start,t.start+t.content.length,this.rows)),!0)}}let eb={defineNodes:[{name:"Table",block:!0},{name:"TableHeader",style:{"TableHeader/...":h._A.heading}},"TableRow",{name:"TableCell",style:h._A.content},{name:"TableDelimiter",style:h._A.processingInstruction}],parseBlock:[{name:"Table",leaf:(e,t)=>eg(t.content,0)?new ex:null,endLeaf(e,t,n){if(n.parsers.some(e=>e instanceof ex)||!eg(t.text,t.basePos))return!1;let r=e.peekLine();return ek.test(r)&&em(e,t.text,t.basePos)==em(e,r,t.basePos)},before:"SetextHeading"}]};class eL{nextLine(){return!1}finish(e,t){return e.addLeafElement(t,e.elt("Task",t.start,t.start+t.content.length,[e.elt("TaskMarker",t.start,t.start+3),...e.parser.parseInline(t.content.slice(3),t.start+3)])),!0}}let eA={defineNodes:[{name:"Task",block:!0,style:h._A.list},{name:"TaskMarker",style:h._A.atom}],parseBlock:[{name:"TaskList",leaf:(e,t)=>/^\[[ xX]\][ \t]/.test(t.content)&&"ListItem"==e.parentType().name?new eL:null,after:"SetextHeading"}]},eS=/(www\.)|(https?:\/\/)|([\w.+-]{1,100}@)|(mailto:|xmpp:)/gy,ey=/[\w-]+(\.[\w-]+)+(\/[^\s<]*)?/gy,eC=/[\w-]+\.[\w-]+($|\/)/,ew=/[\w.+-]+@[\w-]+(\.[\w.-]+)+/gy,eI=/\/[a-zA-Z\d@.]+/gy;function eB(e,t,n,r){let s=0;for(let i=t;i<n;i++)e[i]==r&&s++;return s}function eT(e,t){ew.lastIndex=t;let n=ew.exec(e);if(!n)return -1;let r=n[0][n[0].length-1];return"_"==r||"-"==r?-1:t+n[0].length-("."==r)}let eE=[eb,eA,ep,{parseInline:[{name:"Autolink",parse(e,t,n){let r=n-e.offset;if(r&&/\w/.test(e.text[r-1]))return -1;eS.lastIndex=r;let s=eS.exec(e.text),i=-1;if(!s)return -1;if(s[1]||s[2]){if((i=function(e,t){ey.lastIndex=t;let n=ey.exec(e);if(!n||eC.exec(n[0])[0].indexOf("_")>-1)return -1;let r=t+n[0].length;for(;;){let n=e[r-1],s;if(/[?!.,:*_~]/.test(n)||")"==n&&eB(e,t,r,")")>eB(e,t,r,"("))r--;else if(";"==n&&(s=/&(?:#\d+|#x[a-f\d]+|\w+);$/.exec(e.slice(t,r))))r=t+s.index;else break}return r}(e.text,r+s[0].length))>-1&&e.hasOpenLink){let t=/([^\[\]]|\[[^\]]*\])*/.exec(e.text.slice(r,i));i=r+t[0].length}}else s[3]?i=eT(e.text,r):(i=eT(e.text,r+s[0].length))>-1&&"xmpp:"==s[0]&&(eI.lastIndex=i,(s=eI.exec(e.text))&&(i=s.index+s[0].length));return i<0?-1:(e.addElement(e.elt("URL",n,i+e.offset)),i+e.offset)}}]}];function eH(e,t,n){return(r,s,i)=>{if(s!=e||r.char(i+1)==e)return -1;let o=[r.elt(n,i,i+1)];for(let s=i+1;s<r.end;s++){let l=r.char(s);if(l==e)return r.addElement(r.elt(t,i,s+1,o.concat(r.elt(n,s,s+1))));if(92==l&&o.push(r.elt("Escape",s,s+++2)),m(l))break}return -1}}let ev={defineNodes:[{name:"Superscript",style:h._A.special(h._A.content)},{name:"SuperscriptMark",style:h._A.processingInstruction}],parseInline:[{name:"Superscript",parse:eH(94,"Superscript","SuperscriptMark")}]},eM={defineNodes:[{name:"Subscript",style:h._A.special(h._A.content)},{name:"SubscriptMark",style:h._A.processingInstruction}],parseInline:[{name:"Subscript",parse:eH(126,"Subscript","SubscriptMark")}]},eP={defineNodes:[{name:"Emoji",style:h._A.character}],parseInline:[{name:"Emoji",parse(e,t,n){let r;return 58==t&&(r=/^[a-zA-Z_0-9]+:/.exec(e.slice(n+1,e.end)))?e.addElement(e.elt("Emoji",n,n+1+r[0].length)):-1}}]};var e_=n(32158);let eO=(0,o.p9)({commentTokens:{block:{open:"\x3c!--",close:"--\x3e"}}}),eN=new a.uY,e$=ed.configure({props:[o.b_.add(e=>!e.is("Block")||e.is("Document")||null!=eR(e)||function(e){return"OrderedList"==e.name||"BulletList"==e.name}(e)?void 0:(e,t)=>({from:t.doc.lineAt(e.from).to,to:e.to})),eN.add(eR),o.Oh.add({Document:()=>null}),o.iB.add({Document:eO})]});function eR(e){let t=/^(?:ATX|Setext)Heading(\d)$/.exec(e.name);return t?+t[1]:void 0}let eX=o.t.of((e,t,n)=>{for(let r=(0,o.mv)(e).resolveInner(n,-1);r&&!(r.from<t);r=r.parent){let e=r.type.prop(eN);if(null==e)continue;let t=function(e,t){let n=e;for(;;){let e=n.nextSibling,r;if(!e||null!=(r=eR(e.type))&&r<=t)break;n=e}return n.to}(r,e);if(t>n)return{from:n,to:t}}return null});function eD(e){return new o.TM(eO,e,[],"markdown")}let ez=eD(e$),eF=eD(e$.configure([eE,eM,ev,eP,{props:[o.b_.add({Table:(e,t)=>({from:t.doc.lineAt(e.from).to,to:e.to})})]}]));class eq{constructor(e,t,n,r,s,i,o){this.node=e,this.from=t,this.to=n,this.spaceBefore=r,this.spaceAfter=s,this.type=i,this.item=o}blank(e,t=!0){let n=this.spaceBefore+("Blockquote"==this.node.name?">":"");if(null!=e){for(;n.length<e;)n+=" ";return n}for(let e=this.to-this.from-n.length-this.spaceAfter.length;e>0;e--)n+=" ";return n+(t?this.spaceAfter:"")}marker(e,t){let n="OrderedList"==this.node.name?String(+eZ(this.item,e)[2]+t):"";return this.spaceBefore+n+this.type+this.spaceAfter}}function ej(e,t){let n=[],r=[];for(let t=e;t;t=t.parent){if("FencedCode"==t.name)return r;("ListItem"==t.name||"Blockquote"==t.name)&&n.push(t)}for(let e=n.length-1;e>=0;e--){let s=n[e],i,o=t.lineAt(s.from),l=s.from-o.from;if("Blockquote"==s.name&&(i=/^ *>( ?)/.exec(o.text.slice(l))))r.push(new eq(s,l,l+i[0].length,"",i[1],">",null));else if("ListItem"==s.name&&"OrderedList"==s.parent.name&&(i=/^( *)\d+([.)])( *)/.exec(o.text.slice(l)))){let e=i[3],t=i[0].length;e.length>=4&&(e=e.slice(0,e.length-4),t-=4),r.push(new eq(s.parent,l,l+t,i[1],e,i[2],s))}else if("ListItem"==s.name&&"BulletList"==s.parent.name&&(i=/^( *)([-+*])( {1,4}\[[ xX]\])?( +)/.exec(o.text.slice(l)))){let e=i[4],t=i[0].length;e.length>4&&(e=e.slice(0,e.length-4),t-=4);let n=i[2];i[3]&&(n+=i[3].replace(/[xX]/," ")),r.push(new eq(s.parent,l,l+t,i[1],e,n,s))}}return r}function eZ(e,t){return/^(\s*)(\d+)(?=[.)])/.exec(t.sliceString(e.from,e.from+10))}function eU(e,t,n,r=0){for(let s=-1,i=e;;){if("ListItem"==i.name){let e=eZ(i,t),o=+e[2];if(s>=0){if(o!=s+1)return;n.push({from:i.from+e[1].length,to:i.from+e[0].length,insert:String(s+2+r)})}s=o}let e=i.nextSibling;if(!e)break;i=e}}function eY(e,t){let n=/^[ \t]*/.exec(e)[0].length;if(!n||"	"!=t.facet(o.Xt))return e;let r=(0,s.y$)(e,4,n),i="";for(let e=r;e>0;)e>=4?(i+="	",e-=4):(i+=" ",e--);return i+e.slice(n)}let eQ=({state:e,dispatch:t})=>{let n=(0,o.mv)(e),{doc:r}=e,i=null,l=e.changeByRange(t=>{if(!t.empty||!eF.isActiveAt(e,t.from,-1)&&!eF.isActiveAt(e,t.from,1))return i={range:t};let o=t.from,l=r.lineAt(o),a=ej(n.resolveInner(o,-1),r);for(;a.length&&a[a.length-1].from>o-l.from;)a.pop();if(!a.length)return i={range:t};let h=a[a.length-1];if(h.to-h.spaceAfter.length>o-l.from)return i={range:t};let f=o>=h.to-h.spaceAfter.length&&!/\S/.test(l.text.slice(h.to));if(h.item&&f){let t=h.node.firstChild,n=h.node.getChild("ListItem","ListItem");if(t.to>=o||n&&n.to<o||l.from>0&&!/[^\s>]/.test(r.lineAt(l.from-1).text)){let e=a.length>1?a[a.length-2]:null,t,n="";e&&e.item?(t=l.from+e.from,n=e.marker(r,1)):t=l.from+(e?e.to:0);let i=[{from:t,to:o,insert:n}];return"OrderedList"==h.node.name&&eU(h.item,r,i,-2),e&&"OrderedList"==e.node.name&&eU(e.item,r,i),{range:s.OF.cursor(t+n.length),changes:i}}{let t=eG(a,e,l);return{range:s.OF.cursor(o+t.length+1),changes:{from:l.from,insert:t+e.lineBreak}}}}if("Blockquote"==h.node.name&&f&&l.from){let n=r.lineAt(l.from-1),s=/>\s*$/.exec(n.text);if(s&&s.index==h.from){let r=e.changes([{from:n.from+s.index,to:n.to},{from:l.from+h.from,to:l.to}]);return{range:t.map(r),changes:r}}}let u=[];"OrderedList"==h.node.name&&eU(h.item,r,u);let d=h.item&&h.item.from<l.from,c="";if(!d||/^[\s\d.)\-+*>]*/.exec(l.text)[0].length>=h.to)for(let e=0,t=a.length-1;e<=t;e++)c+=e!=t||d?a[e].blank(e<t?(0,s.y$)(l.text,4,a[e+1].from)-c.length:null):a[e].marker(r,1);let p=o;for(;p>l.from&&/\s/.test(l.text.charAt(p-l.from-1));)p--;return c=eY(c,e),function(e,t){if("OrderedList"!=e.name&&"BulletList"!=e.name)return!1;let n=e.firstChild,r=e.getChild("ListItem","ListItem");if(!r)return!1;let s=t.lineAt(n.to),i=t.lineAt(r.from),o=/^[\s>]*$/.test(s.text);return s.number+ +!o<i.number}(h.node,e.doc)&&(c=eG(a,e,l)+e.lineBreak+c),u.push({from:p,to:o,insert:e.lineBreak+c}),{range:s.OF.cursor(p+c.length+1),changes:u}});return!i&&(t(e.update(l,{scrollIntoView:!0,userEvent:"input"})),!0)};function eV(e){return"QuoteMark"==e.name||"ListMark"==e.name}function eG(e,t,n){let r="";for(let t=0,i=e.length-2;t<=i;t++)r+=e[t].blank(t<i?(0,s.y$)(n.text,4,e[t+1].from)-r.length:null,t<i);return eY(r,t)}let eK=({state:e,dispatch:t})=>{let n=(0,o.mv)(e),r=null,i=e.changeByRange(t=>{let i=t.from,{doc:o}=e;if(t.empty&&eF.isActiveAt(e,t.from)){let t=o.lineAt(i),r=ej(function(e,t){let n=e.resolveInner(t,-1),r=t;eV(n)&&(r=n.from,n=n.parent);for(let e;e=n.childBefore(r);)if(eV(e))r=e.from;else if("OrderedList"==e.name||"BulletList"==e.name)r=(n=e.lastChild).to;else break;return n}(n,i),o);if(r.length){let n=r[r.length-1],o=n.to-n.spaceAfter.length+ +!!n.spaceAfter;if(i-t.from>o&&!/\S/.test(t.text.slice(o,i-t.from)))return{range:s.OF.cursor(t.from+o),changes:{from:t.from+o,to:i}};if(i-t.from==o&&(!n.item||t.from<=n.item.from||!/\S/.test(t.text.slice(0,n.to)))){let r=t.from+n.from;if(n.item&&n.node.from<n.item.from&&/\S/.test(t.text.slice(n.from,n.to))){let i=n.blank((0,s.y$)(t.text,4,n.to)-(0,s.y$)(t.text,4,n.from));return r==t.from&&(i=eY(i,e)),{range:s.OF.cursor(r+i.length),changes:{from:r,to:t.from+n.to,insert:i}}}if(r<i)return{range:s.OF.cursor(r),changes:{from:r,to:i}}}}}return r={range:t}});return!r&&(t(e.update(i,{scrollIntoView:!0,userEvent:"delete"})),!0)},eJ=[{key:"Enter",run:eQ},{key:"Backspace",run:eK}],eW=(0,e_.html)({matchClosingTags:!1});function e1(e={}){var t;let{codeLanguages:n,defaultCodeLanguage:l,addKeymap:h=!0,base:{parser:f}=ez,completeHTMLTags:u=!0,htmlTagLanguage:d=eW}=e;if(!(f instanceof D))throw RangeError("Base parser provided to `markdown` should be a Markdown parser");let c=e.extensions?[e.extensions]:[],p=[d.support,eX],m;l instanceof o.Yy?(p.push(l.support),m=l.language):l&&(m=l);let g=n||m?(t=m,e=>{if(e&&n){let t=null;if(e=/\S*/.exec(e)[0],(t="function"==typeof n?n(e):o.t$.matchLanguageName(n,e,!0))instanceof o.t$)return t.support?t.support.language.parser:o.nq.getSkippingParser(t.load());if(t)return t.parser}return t?t.parser:null}):void 0;c.push(function(e){let{codeParser:t,htmlParser:n}=e;return{wrap:(0,a.$g)((e,s)=>{let i=e.type.id;if(t&&(i==r.CodeBlock||i==r.FencedCode)){let n="";if(i==r.FencedCode){let t=e.node.getChild(r.CodeInfo);t&&(n=s.read(t.from,t.to))}let o=t(n);if(o)return{parser:o,overlay:e=>e.type.id==r.CodeText}}else if(n&&(i==r.HTMLBlock||i==r.HTMLTag||i==r.CommentBlock))return{parser:n,overlay:function(e,t,n){let r=[];for(let s=e.firstChild,i=t;;s=s.nextSibling){let e=s?s.from:n;if(e>i&&r.push({from:i,to:e}),!s)break;i=s.to}return r}(e.node,e.from,e.to)};return null})}}({codeParser:g,htmlParser:d.language.parser})),h&&p.push(s.Nb.high(i.w4.of(eJ)));let k=eD(f.configure(c));return u&&p.push(k.data.of({autocomplete:e0})),new o.Yy(k,p)}function e0(e){let{state:t,pos:n}=e,r=/<[:\-\.\w\u00b7-\uffff]*$/.exec(t.sliceDoc(n-25,n));if(!r)return null;let i=(0,o.mv)(t).resolveInner(n,-1);for(;i&&!i.type.isTop;){if("CodeBlock"==i.name||"FencedCode"==i.name||"ProcessingInstructionBlock"==i.name||"CommentBlock"==i.name||"Link"==i.name||"Image"==i.name)return null;i=i.parent}return{from:n-r[0].length,to:n,options:function(){if(e2)return e2;let e=(0,e_.htmlCompletionSource)(new l._5(s.$t.create({extensions:eW}),0,!0));return e2=e?e.options:[]}(),validFor:/^<[:\-\.\w\u00b7-\uffff]*$/}}let e2=null}}]);