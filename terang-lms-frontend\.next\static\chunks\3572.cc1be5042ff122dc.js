try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="31e3975c-e72a-49e4-aa2a-01601b21cb60",t._sentryDebugIdIdentifier="sentry-dbid-31e3975c-e72a-49e4-aa2a-01601b21cb60")}catch(t){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[360,3572],{3572:(t,e,r)=>{r.r(e),r.d(e,{pug:()=>c});var n=r(87979),i={"{":"}","(":")","[":"]"};class a{constructor(t){this.indentUnit=t,this.javaScriptLine=!1,this.javaScriptLineExcludesColon=!1,this.javaScriptArguments=!1,this.javaScriptArgumentsDepth=0,this.isInterpolating=!1,this.interpolationNesting=0,this.jsState=n.javascript.startState(t),this.restOfLine="",this.isIncludeFiltered=!1,this.isEach=!1,this.lastTag="",this.isAttrs=!1,this.attrsNest=[],this.inAttributeName=!0,this.attributeIsType=!1,this.attrValue="",this.indentOf=1/0,this.indentToken=""}copy(){var t=new a(this.indentUnit);return t.javaScriptLine=this.javaScriptLine,t.javaScriptLineExcludesColon=this.javaScriptLineExcludesColon,t.javaScriptArguments=this.javaScriptArguments,t.javaScriptArgumentsDepth=this.javaScriptArgumentsDepth,t.isInterpolating=this.isInterpolating,t.interpolationNesting=this.interpolationNesting,t.jsState=(n.javascript.copyState||function(t){if("object"!=typeof t)return t;let e={};for(let r in t){let n=t[r];e[r]=n instanceof Array?n.slice():n}return e})(this.jsState),t.restOfLine=this.restOfLine,t.isIncludeFiltered=this.isIncludeFiltered,t.isEach=this.isEach,t.lastTag=this.lastTag,t.isAttrs=this.isAttrs,t.attrsNest=this.attrsNest.slice(),t.inAttributeName=this.inAttributeName,t.attributeIsType=this.attributeIsType,t.attrValue=this.attrValue,t.indentOf=this.indentOf,t.indentToken=this.indentToken,t}}function o(t,e){if(t.match("#{"))return e.isInterpolating=!0,e.interpolationNesting=0,"punctuation"}function s(t,e){if(t.match(/^:([\w\-]+)/))return u(t,e),"atom"}function u(t,e){e.indentOf=t.indentation(),e.indentToken="string"}let c={startState:function(t){return new a(t)},copyState:function(t){return t.copy()},token:function(t,e){var r=function(t,e){if(t.sol()&&(e.restOfLine=""),e.restOfLine){t.skipToEnd();var r=e.restOfLine;return e.restOfLine="",r}}(t,e)||function(t,e){if(e.isInterpolating){if("}"===t.peek()){if(e.interpolationNesting--,e.interpolationNesting<0)return t.next(),e.isInterpolating=!1,"punctuation"}else"{"===t.peek()&&e.interpolationNesting++;return n.javascript.token(t,e.jsState)||!0}}(t,e)||function(t,e){if(e.isIncludeFiltered){var r=s(t,e);return e.isIncludeFiltered=!1,e.restOfLine="string",r}}(t,e)||function(t,e){if(e.isEach){if(t.match(/^ in\b/))return e.javaScriptLine=!0,e.isEach=!1,"keyword";else if(t.sol()||t.eol())e.isEach=!1;else if(t.next()){for(;!t.match(/^ in\b/,!1)&&t.next(););return"variable"}}}(t,e)||function t(e,r){if(r.isAttrs){if(i[e.peek()]&&r.attrsNest.push(i[e.peek()]),r.attrsNest[r.attrsNest.length-1]===e.peek())r.attrsNest.pop();else if(e.eat(")"))return r.isAttrs=!1,"punctuation";if(r.inAttributeName&&e.match(/^[^=,\)!]+/))return("="===e.peek()||"!"===e.peek())&&(r.inAttributeName=!1,r.jsState=n.javascript.startState(2),"script"===r.lastTag&&"type"===e.current().trim().toLowerCase()?r.attributeIsType=!0:r.attributeIsType=!1),"attribute";var a=n.javascript.token(e,r.jsState);if(0===r.attrsNest.length&&("string"===a||"variable"===a||"keyword"===a))try{return Function("","var x "+r.attrValue.replace(/,\s*$/,"").replace(/^!/,"")),r.inAttributeName=!0,r.attrValue="",e.backUp(e.current().length),t(e,r)}catch(t){}return r.attrValue+=e.current(),a||!0}}(t,e)||function(t,e){if(t.sol()&&(e.javaScriptLine=!1,e.javaScriptLineExcludesColon=!1),e.javaScriptLine){if(e.javaScriptLineExcludesColon&&":"===t.peek()){e.javaScriptLine=!1,e.javaScriptLineExcludesColon=!1;return}var r=n.javascript.token(t,e.jsState);return t.eol()&&(e.javaScriptLine=!1),r||!0}}(t,e)||function(t,e){if(e.javaScriptArguments){if(0===e.javaScriptArgumentsDepth&&"("!==t.peek()||("("===t.peek()?e.javaScriptArgumentsDepth++:")"===t.peek()&&e.javaScriptArgumentsDepth--,0===e.javaScriptArgumentsDepth)){e.javaScriptArguments=!1;return}return n.javascript.token(t,e.jsState)||!0}}(t,e)||function(t,e){if(e.mixinCallAfter)return e.mixinCallAfter=!1,t.match(/^\( *[-\w]+ *=/,!1)||(e.javaScriptArguments=!0,e.javaScriptArgumentsDepth=0),!0}(t,e)||function(t){if(t.match(/^yield\b/))return"keyword"}(t)||function(t){if(t.match(/^(?:doctype) *([^\n]+)?/))return"meta"}(t)||o(t,e)||function(t,e){if(t.match(/^case\b/))return e.javaScriptLine=!0,"keyword"}(t,e)||function(t,e){if(t.match(/^when\b/))return e.javaScriptLine=!0,e.javaScriptLineExcludesColon=!0,"keyword"}(t,e)||function(t){if(t.match(/^default\b/))return"keyword"}(t)||function(t,e){if(t.match(/^extends?\b/))return e.restOfLine="string","keyword"}(t,e)||function(t,e){if(t.match(/^append\b/))return e.restOfLine="variable","keyword"}(t,e)||function(t,e){if(t.match(/^prepend\b/))return e.restOfLine="variable","keyword"}(t,e)||function(t,e){if(t.match(/^block\b *(?:(prepend|append)\b)?/))return e.restOfLine="variable","keyword"}(t,e)||function(t,e){if(t.match(/^include\b/))return e.restOfLine="string","keyword"}(t,e)||function(t,e){if(t.match(/^include:([a-zA-Z0-9\-]+)/,!1)&&t.match("include"))return e.isIncludeFiltered=!0,"keyword"}(t,e)||function(t,e){if(t.match(/^mixin\b/))return e.javaScriptLine=!0,"keyword"}(t,e)||(t.match(/^\+([-\w]+)/)?(t.match(/^\( *[-\w]+ *=/,!1)||(e.javaScriptArguments=!0,e.javaScriptArgumentsDepth=0),"variable"):t.match("+#{",!1)?(t.next(),e.mixinCallAfter=!0,o(t,e)):void 0)||function(t,e){if(t.match(/^(if|unless|else if|else)\b/))return e.javaScriptLine=!0,"keyword"}(t,e)||function(t,e){if(t.match(/^(- *)?(each|for)\b/))return e.isEach=!0,"keyword"}(t,e)||function(t,e){if(t.match(/^while\b/))return e.javaScriptLine=!0,"keyword"}(t,e)||function(t,e){var r;if(r=t.match(/^(\w(?:[-:\w]*\w)?)\/?/))return e.lastTag=r[1].toLowerCase(),"tag"}(t,e)||s(t,e)||function(t,e){if(t.match(/^(!?=|-)/))return e.javaScriptLine=!0,"punctuation"}(t,e)||function(t){if(t.match(/^#([\w-]+)/))return"builtin"}(t)||function(t){if(t.match(/^\.([\w-]+)/))return"className"}(t)||function(t,e){if("("==t.peek())return t.next(),e.isAttrs=!0,e.attrsNest=[],e.inAttributeName=!0,e.attrValue="",e.attributeIsType=!1,"punctuation"}(t,e)||function(t,e){if(t.match(/^&attributes\b/))return e.javaScriptArguments=!0,e.javaScriptArgumentsDepth=0,"keyword"}(t,e)||function(t){if(t.sol()&&t.eatSpace())return"indent"}(t)||(t.match(/^(?:\| ?| )([^\n]+)/)?"string":t.match(/^(<[^\n]*)/,!1)?(u(t,e),t.skipToEnd(),e.indentToken):void 0)||function(t,e){if(t.match(/^ *\/\/(-)?([^\n]*)/))return e.indentOf=t.indentation(),e.indentToken="comment","comment"}(t,e)||function(t){if(t.match(/^: */))return"colon"}(t)||function(t,e){if(t.eat("."))return u(t,e),"dot"}(t,e)||(t.next(),null);return!0===r?null:r}}},87979:(t,e,r)=>{function n(t){var e,r,n=t.statementIndent,i=t.jsonld,a=t.json||i,o=t.typescript,s=t.wordCharacters||/[\w$\xa1-\uffff]/,u=function(){function t(t){return{type:t,style:"keyword"}}var e=t("keyword a"),r=t("keyword b"),n=t("keyword c"),i=t("keyword d"),a=t("operator"),o={type:"atom",style:"atom"};return{if:t("if"),while:e,with:e,else:r,do:r,try:r,finally:r,return:i,break:i,continue:i,new:t("new"),delete:n,void:n,throw:n,debugger:t("debugger"),var:t("var"),const:t("var"),let:t("var"),function:t("function"),catch:t("catch"),for:t("for"),switch:t("switch"),case:t("case"),default:t("default"),in:a,typeof:a,instanceof:a,true:o,false:o,null:o,undefined:o,NaN:o,Infinity:o,this:t("this"),class:t("class"),super:t("atom"),yield:n,export:t("export"),import:t("import"),extends:n,await:n}}(),c=/[+\-*&%=<>!?|~^@]/,f=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function l(t,n,i){return e=t,r=i,n}function d(t,e){var r,n,a,o=t.next();if('"'==o||"'"==o){return r=o,e.tokenize=function(t,e){var n,a=!1;if(i&&"@"==t.peek()&&t.match(f))return e.tokenize=d,l("jsonld-keyword","meta");for(;null!=(n=t.next())&&(n!=r||a);)a=!a&&"\\"==n;return a||(e.tokenize=d),l("string","string")},e.tokenize(t,e)}if("."==o&&t.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return l("number","number");if("."==o&&t.match(".."))return l("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(o))return l(o);if("="==o&&t.eat(">"))return l("=>","operator");else if("0"==o&&t.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return l("number","number");else if(/\d/.test(o))return t.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),l("number","number");else if("/"==o)if(t.eat("*"))return e.tokenize=p,p(t,e);else{if(t.eat("/"))return t.skipToEnd(),l("comment","comment");if(n=t,(a=e).tokenize==d&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(a.lastType)||"quasi"==a.lastType&&/\{\s*$/.test(n.string.slice(0,n.pos-1)))return!function(t){for(var e,r=!1,n=!1;null!=(e=t.next());){if(!r){if("/"==e&&!n)return;"["==e?n=!0:n&&"]"==e&&(n=!1)}r=!r&&"\\"==e}}(t),t.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),l("regexp","string.special");else return t.eat("="),l("operator","operator",t.current())}else if("`"==o)return e.tokenize=m,m(t,e);else if("#"==o&&"!"==t.peek())return t.skipToEnd(),l("meta","meta");else if("#"==o&&t.eatWhile(s))return l("variable","property");else if("<"==o&&t.match("!--")||"-"==o&&t.match("->")&&!/\S/.test(t.string.slice(0,t.start)))return t.skipToEnd(),l("comment","comment");else if(c.test(o))return((">"!=o||!e.lexical||">"!=e.lexical.type)&&(t.eat("=")?("!"==o||"="==o)&&t.eat("="):/[<>*+\-|&?]/.test(o)&&(t.eat(o),">"==o&&t.eat(o))),"?"==o&&t.eat("."))?l("."):l("operator","operator",t.current());else if(s.test(o)){t.eatWhile(s);var k=t.current();if("."!=e.lastType){if(u.propertyIsEnumerable(k)){var v=u[k];return l(v.type,v.style,k)}if("async"==k&&t.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return l("async","keyword",k)}return l("variable","variable",k)}}function p(t,e){for(var r,n=!1;r=t.next();){if("/"==r&&n){e.tokenize=d;break}n="*"==r}return l("comment","comment")}function m(t,e){for(var r,n=!1;null!=(r=t.next());){if(!n&&("`"==r||"$"==r&&t.eat("{"))){e.tokenize=d;break}n=!n&&"\\"==r}return l("quasi","string.special",t.current())}function k(t,e){e.fatArrowAt&&(e.fatArrowAt=null);var r=t.string.indexOf("=>",t.start);if(!(r<0)){if(o){var n=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(t.string.slice(t.start,r));n&&(r=n.index)}for(var i=0,a=!1,u=r-1;u>=0;--u){var c=t.string.charAt(u),f="([{}])".indexOf(c);if(f>=0&&f<3){if(!i){++u;break}if(0==--i){"("==c&&(a=!0);break}}else if(f>=3&&f<6)++i;else if(s.test(c))a=!0;else if(/["'\/`]/.test(c))for(;;--u){if(0==u)return;if(t.string.charAt(u-1)==c&&"\\"!=t.string.charAt(u-2)){u--;break}}else if(a&&!i){++u;break}}a&&!i&&(e.fatArrowAt=u)}}var v={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,import:!0,"jsonld-keyword":!0};function h(t,e,r,n,i,a){this.indented=t,this.column=e,this.type=r,this.prev=i,this.info=a,null!=n&&(this.align=n)}var y={state:null,column:null,marked:null,cc:null};function b(){for(var t=arguments.length-1;t>=0;t--)y.cc.push(arguments[t])}function w(){return b.apply(null,arguments),!0}function g(t,e){for(var r=e;r;r=r.next)if(r.name==t)return!0;return!1}function x(e){var r=y.state;if(y.marked="def",r.context){if("var"==r.lexical.info&&r.context&&r.context.block){var n=function t(e,r){if(!r)return null;if(r.block){var n=t(e,r.prev);return n?n==r.prev?r:new S(n,r.vars,!0):null}return g(e,r.vars)?r:new S(r.prev,new A(e,r.vars),!1)}(e,r.context);if(null!=n){r.context=n;return}}else if(!g(e,r.localVars)){r.localVars=new A(e,r.localVars);return}}t.globalVars&&!g(e,r.globalVars)&&(r.globalVars=new A(e,r.globalVars))}function j(t){return"public"==t||"private"==t||"protected"==t||"abstract"==t||"readonly"==t}function S(t,e,r){this.prev=t,this.vars=e,this.block=r}function A(t,e){this.name=t,this.next=e}var L=new A("this",new A("arguments",null));function T(){y.state.context=new S(y.state.context,y.state.localVars,!1),y.state.localVars=L}function N(){y.state.context=new S(y.state.context,y.state.localVars,!0),y.state.localVars=null}function I(){y.state.localVars=y.state.context.vars,y.state.context=y.state.context.prev}function V(t,e){var r=function(){var r=y.state,n=r.indented;if("stat"==r.lexical.type)n=r.lexical.indented;else for(var i=r.lexical;i&&")"==i.type&&i.align;i=i.prev)n=i.indented;r.lexical=new h(n,y.stream.column(),t,null,r.lexical,e)};return r.lex=!0,r}function O(){var t=y.state;t.lexical.prev&&(")"==t.lexical.type&&(t.indented=t.lexical.indented),t.lexical=t.lexical.prev)}function E(t){return function e(r){return r==t?w():";"==t||"}"==r||")"==r||"]"==r?b():w(e)}}function z(t,e){if("var"==t)return w(V("vardef",e),th,E(";"),O);if("keyword a"==t)return w(V("form"),$,z,O);if("keyword b"==t)return w(V("form"),z,O);if("keyword d"==t)return y.stream.match(/^\s*$/,!1)?w():w(V("stat"),q,E(";"),O);if("debugger"==t)return w(E(";"));if("{"==t)return w(V("}"),N,te,O,I);if(";"==t)return w();if("if"==t)return"else"==y.state.lexical.info&&y.state.cc[y.state.cc.length-1]==O&&y.state.cc.pop()(),w(V("form"),$,z,O,tj);if("function"==t)return w(tT);if("for"==t)return w(V("form"),N,tS,z,I,O);if("class"==t||o&&"interface"==e)return y.marked="keyword",w(V("form","class"==t?t:e),tE,O);if("variable"==t)if(o&&"declare"==e)return y.marked="keyword",w(z);else if(o&&("module"==e||"enum"==e||"type"==e)&&y.stream.match(/^\s*\w/,!1))return(y.marked="keyword","enum"==e)?w(tZ):"type"==e?w(tI,E("operator"),to,E(";")):w(V("form"),ty,E("{"),V("}"),te,O,O);else if(o&&"namespace"==e)return y.marked="keyword",w(V("form"),_,z,O);else if(o&&"abstract"==e)return y.marked="keyword",w(z);else return w(V("stat"),K);return"switch"==t?w(V("form"),$,E("{"),V("}","switch"),N,te,O,O,I):"case"==t?w(_,E(":")):"default"==t?w(E(":")):"catch"==t?w(V("form"),T,C,z,O,I):"export"==t?w(V("stat"),tD,O):"import"==t?w(V("stat"),tF,O):"async"==t?w(z):"@"==e?w(_,z):b(V("stat"),_,E(";"),O)}function C(t){if("("==t)return w(tV,E(")"))}function _(t,e){return F(t,e,!1)}function D(t,e){return F(t,e,!0)}function $(t){return"("!=t?b():w(V(")"),q,E(")"),O)}function F(t,e,r){if(y.state.fatArrowAt==y.stream.start){var n,i=r?G:Z;if("("==t)return w(T,V(")"),Y(tV,")"),O,E("=>"),i,I);if("variable"==t)return b(T,ty,E("=>"),i,I)}var a=r?P:U;return v.hasOwnProperty(t)?w(a):"function"==t?w(tT,a):"class"==t||o&&"interface"==e?(y.marked="keyword",w(V("form"),tO,O)):"keyword c"==t||"async"==t?w(r?D:_):"("==t?w(V(")"),q,E(")"),O,a):"operator"==t||"spread"==t?w(r?D:_):"["==t?w(V("]"),tB,O,a):"{"==t?tt(Q,"}",null,a):"quasi"==t?b(W,a):"new"==t?w((n=r,function(t){return"."==t?w(n?J:H):"variable"==t&&o?w(tm,n?P:U):b(n?D:_)})):w()}function q(t){return t.match(/[;\}\)\],]/)?b():b(_)}function U(t,e){return","==t?w(q):P(t,e,!1)}function P(t,e,r){var n=!1==r?U:P,i=!1==r?_:D;if("=>"==t)return w(T,r?G:Z,I);if("operator"==t)return/\+\+|--/.test(e)||o&&"!"==e?w(n):o&&"<"==e&&y.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?w(V(">"),Y(to,">"),O,n):"?"==e?w(_,E(":"),i):w(i);if("quasi"==t)return b(W,n);if(";"!=t){if("("==t)return tt(D,")","call",n);if("."==t)return w(M,n);if("["==t)return w(V("]"),q,E("]"),O,n);if(o&&"as"==e)return y.marked="keyword",w(to,n);if("regexp"==t)return y.state.lastType=y.marked="operator",y.stream.backUp(y.stream.pos-y.stream.start-1),w(i)}}function W(t,e){return"quasi"!=t?b():"${"!=e.slice(e.length-2)?w(W):w(q,B)}function B(t){if("}"==t)return y.marked="string.special",y.state.tokenize=m,w(W)}function Z(t){return k(y.stream,y.state),b("{"==t?z:_)}function G(t){return k(y.stream,y.state),b("{"==t?z:D)}function H(t,e){if("target"==e)return y.marked="keyword",w(U)}function J(t,e){if("target"==e)return y.marked="keyword",w(P)}function K(t){return":"==t?w(O,z):b(U,E(";"),O)}function M(t){if("variable"==t)return y.marked="property",w()}function Q(t,e){if("async"==t)return y.marked="property",w(Q);if("variable"==t||"keyword"==y.style){var r;return(y.marked="property","get"==e||"set"==e)?w(R):(o&&y.state.fatArrowAt==y.stream.start&&(r=y.stream.match(/^\s*:\s*/,!1))&&(y.state.fatArrowAt=y.stream.pos+r[0].length),w(X))}if("number"==t||"string"==t)return y.marked=i?"property":y.style+" property",w(X);if("jsonld-keyword"==t)return w(X);if(o&&j(e))return y.marked="keyword",w(Q);else if("["==t)return w(_,tr,E("]"),X);else if("spread"==t)return w(D,X);else if("*"==e)return y.marked="keyword",w(Q);else if(":"==t)return b(X)}function R(t){return"variable"!=t?b(X):(y.marked="property",w(tT))}function X(t){return":"==t?w(D):"("==t?b(tT):void 0}function Y(t,e,r){function n(i,a){if(r?r.indexOf(i)>-1:","==i){var o=y.state.lexical;return"call"==o.info&&(o.pos=(o.pos||0)+1),w(function(r,n){return r==e||n==e?b():b(t)},n)}return i==e||a==e?w():r&&r.indexOf(";")>-1?b(t):w(E(e))}return function(r,i){return r==e||i==e?w():b(t,n)}}function tt(t,e,r){for(var n=3;n<arguments.length;n++)y.cc.push(arguments[n]);return w(V(e,r),Y(t,e),O)}function te(t){return"}"==t?w():b(z,te)}function tr(t,e){if(o){if(":"==t)return w(to);if("?"==e)return w(tr)}}function tn(t,e){if(o&&(":"==t||"in"==e))return w(to)}function ti(t){if(o&&":"==t)if(y.stream.match(/^\s*\w+\s+is\b/,!1))return w(_,ta,to);else return w(to)}function ta(t,e){if("is"==e)return y.marked="keyword",w()}function to(t,e){return"keyof"==e||"typeof"==e||"infer"==e||"readonly"==e?(y.marked="keyword",w("typeof"==e?D:to)):"variable"==t||"void"==e?(y.marked="type",w(tp)):"|"==e||"&"==e?w(to):"string"==t||"number"==t||"atom"==t?w(tp):"["==t?w(V("]"),Y(to,"]",","),O,tp):"{"==t?w(V("}"),tu,O,tp):"("==t?w(Y(td,")"),ts,tp):"<"==t?w(Y(to,">"),to):"quasi"==t?b(tf,tp):void 0}function ts(t){if("=>"==t)return w(to)}function tu(t){return t.match(/[\}\)\]]/)?w():","==t||";"==t?w(tu):b(tc,tu)}function tc(t,e){if("variable"==t||"keyword"==y.style)return y.marked="property",w(tc);if("?"==e||"number"==t||"string"==t)return w(tc);if(":"==t)return w(to);if("["==t)return w(E("variable"),tn,E("]"),tc);if("("==t)return b(tN,tc);else if(!t.match(/[;\}\)\],]/))return w()}function tf(t,e){return"quasi"!=t?b():"${"!=e.slice(e.length-2)?w(tf):w(to,tl)}function tl(t){if("}"==t)return y.marked="string.special",y.state.tokenize=m,w(tf)}function td(t,e){return"variable"==t&&y.stream.match(/^\s*[?:]/,!1)||"?"==e?w(td):":"==t?w(to):"spread"==t?w(td):b(to)}function tp(t,e){return"<"==e?w(V(">"),Y(to,">"),O,tp):"|"==e||"."==t||"&"==e?w(to):"["==t?w(to,E("]"),tp):"extends"==e||"implements"==e?(y.marked="keyword",w(to)):"?"==e?w(to,E(":"),to):void 0}function tm(t,e){if("<"==e)return w(V(">"),Y(to,">"),O,tp)}function tk(){return b(to,tv)}function tv(t,e){if("="==e)return w(to)}function th(t,e){return"enum"==e?(y.marked="keyword",w(tZ)):b(ty,tr,tg,tx)}function ty(t,e){return o&&j(e)?(y.marked="keyword",w(ty)):"variable"==t?(x(e),w()):"spread"==t?w(ty):"["==t?tt(tw,"]"):"{"==t?tt(tb,"}"):void 0}function tb(t,e){return"variable"!=t||y.stream.match(/^\s*:/,!1)?("variable"==t&&(y.marked="property"),"spread"==t)?w(ty):"}"==t?b():"["==t?w(_,E("]"),E(":"),tb):w(E(":"),ty,tg):(x(e),w(tg))}function tw(){return b(ty,tg)}function tg(t,e){if("="==e)return w(D)}function tx(t){if(","==t)return w(th)}function tj(t,e){if("keyword b"==t&&"else"==e)return w(V("form","else"),z,O)}function tS(t,e){return"await"==e?w(tS):"("==t?w(V(")"),tA,O):void 0}function tA(t){return"var"==t?w(th,tL):"variable"==t?w(tL):b(tL)}function tL(t,e){return")"==t?w():";"==t?w(tL):"in"==e||"of"==e?(y.marked="keyword",w(_,tL)):b(_,tL)}function tT(t,e){return"*"==e?(y.marked="keyword",w(tT)):"variable"==t?(x(e),w(tT)):"("==t?w(T,V(")"),Y(tV,")"),O,ti,z,I):o&&"<"==e?w(V(">"),Y(tk,">"),O,tT):void 0}function tN(t,e){return"*"==e?(y.marked="keyword",w(tN)):"variable"==t?(x(e),w(tN)):"("==t?w(T,V(")"),Y(tV,")"),O,ti,I):o&&"<"==e?w(V(">"),Y(tk,">"),O,tN):void 0}function tI(t,e){return"keyword"==t||"variable"==t?(y.marked="type",w(tI)):"<"==e?w(V(">"),Y(tk,">"),O):void 0}function tV(t,e){return("@"==e&&w(_,tV),"spread"==t)?w(tV):o&&j(e)?(y.marked="keyword",w(tV)):o&&"this"==t?w(tr,tg):b(ty,tr,tg)}function tO(t,e){return"variable"==t?tE(t,e):tz(t,e)}function tE(t,e){if("variable"==t)return x(e),w(tz)}function tz(t,e){return"<"==e?w(V(">"),Y(tk,">"),O,tz):"extends"==e||"implements"==e||o&&","==t?("implements"==e&&(y.marked="keyword"),w(o?to:_,tz)):"{"==t?w(V("}"),tC,O):void 0}function tC(t,e){return"async"==t||"variable"==t&&("static"==e||"get"==e||"set"==e||o&&j(e))&&y.stream.match(/^\s+#?[\w$\xa1-\uffff]/,!1)?(y.marked="keyword",w(tC)):"variable"==t||"keyword"==y.style?(y.marked="property",w(t_,tC)):"number"==t||"string"==t?w(t_,tC):"["==t?w(_,tr,E("]"),t_,tC):"*"==e?(y.marked="keyword",w(tC)):o&&"("==t?b(tN,tC):";"==t||","==t?w(tC):"}"==t?w():"@"==e?w(_,tC):void 0}function t_(t,e){if("!"==e||"?"==e)return w(t_);if(":"==t)return w(to,tg);if("="==e)return w(D);var r=y.state.lexical.prev;return b(r&&"interface"==r.info?tN:tT)}function tD(t,e){return"*"==e?(y.marked="keyword",w(tW,E(";"))):"default"==e?(y.marked="keyword",w(_,E(";"))):"{"==t?w(Y(t$,"}"),tW,E(";")):b(z)}function t$(t,e){return"as"==e?(y.marked="keyword",w(E("variable"))):"variable"==t?b(D,t$):void 0}function tF(t){return"string"==t?w():"("==t?b(_):"."==t?b(U):b(tq,tU,tW)}function tq(t,e){return"{"==t?tt(tq,"}"):("variable"==t&&x(e),"*"==e&&(y.marked="keyword"),w(tP))}function tU(t){if(","==t)return w(tq,tU)}function tP(t,e){if("as"==e)return y.marked="keyword",w(tq)}function tW(t,e){if("from"==e)return y.marked="keyword",w(_)}function tB(t){return"]"==t?w():b(Y(D,"]"))}function tZ(){return b(V("form"),ty,E("{"),V("}"),Y(tG,"}"),O,O)}function tG(){return b(ty,tg)}return T.lex=N.lex=!0,I.lex=!0,O.lex=!0,{name:t.name,startState:function(e){var r={tokenize:d,lastType:"sof",cc:[],lexical:new h(-e,0,"block",!1),localVars:t.localVars,context:t.localVars&&new S(null,null,!1),indented:0};return t.globalVars&&"object"==typeof t.globalVars&&(r.globalVars=t.globalVars),r},token:function(t,n){if(t.sol()&&(n.lexical.hasOwnProperty("align")||(n.lexical.align=!1),n.indented=t.indentation(),k(t,n)),n.tokenize!=p&&t.eatSpace())return null;var i=n.tokenize(t,n);if("comment"==e)return i;n.lastType="operator"==e&&("++"==r||"--"==r)?"incdec":e;var o=e,s=r,u=n.cc;for(y.state=n,y.stream=t,y.marked=null,y.cc=u,y.style=i,n.lexical.hasOwnProperty("align")||(n.lexical.align=!0);;)if((u.length?u.pop():a?_:z)(o,s)){for(;u.length&&u[u.length-1].lex;)u.pop()();if(y.marked)return y.marked;if("variable"==o&&function(t,e){for(var r=t.localVars;r;r=r.next)if(r.name==e)return!0;for(var n=t.context;n;n=n.prev)for(var r=n.vars;r;r=r.next)if(r.name==e)return!0}(n,s))return"variableName.local";return i}},indent:function(e,r,i){if(e.tokenize==p||e.tokenize==m)return null;if(e.tokenize!=d)return 0;var a,o=r&&r.charAt(0),s=e.lexical;if(!/^\s*else\b/.test(r))for(var u=e.cc.length-1;u>=0;--u){var f=e.cc[u];if(f==O)s=s.prev;else if(f!=tj&&f!=I)break}for(;("stat"==s.type||"form"==s.type)&&("}"==o||(a=e.cc[e.cc.length-1])&&(a==U||a==P)&&!/^[,\.=+\-*:?[\(]/.test(r));)s=s.prev;n&&")"==s.type&&"stat"==s.prev.type&&(s=s.prev);var l=s.type,k=o==l;if("vardef"==l)return s.indented+("operator"==e.lastType||","==e.lastType?s.info.length+1:0);if("form"==l&&"{"==o)return s.indented;if("form"==l)return s.indented+i.unit;if("stat"==l)return s.indented+("operator"==e.lastType||","==e.lastType||c.test(r.charAt(0))||/[,.]/.test(r.charAt(0))?n||i.unit:0);if("switch"==s.info&&!k&&!1!=t.doubleIndentSwitch)return s.indented+(/^(?:case|default)\b/.test(r)?i.unit:2*i.unit);else if(s.align)return s.column+ +!k;else return s.indented+(k?0:i.unit)},languageData:{indentOnInput:/^\s*(?:case .*?:|default:|\{|\})$/,commentTokens:a?void 0:{line:"//",block:{open:"/*",close:"*/"}},closeBrackets:{brackets:["(","[","{","'",'"',"`"]},wordChars:"$"}}}r.r(e),r.d(e,{javascript:()=>i,json:()=>a,jsonld:()=>o,typescript:()=>s});let i=n({name:"javascript"}),a=n({name:"json",json:!0}),o=n({name:"json",jsonld:!0}),s=n({name:"typescript",typescript:!0})}}]);