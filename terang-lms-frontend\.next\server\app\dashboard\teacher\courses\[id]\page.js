try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},i=(new e.Error).stack;i&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[i]="801aeb83-66b4-4130-8731-e6fe2e368aa3",e._sentryDebugIdIdentifier="sentry-dbid-801aeb83-66b4-4130-8731-e6fe2e368aa3")}catch(e){}(()=>{var e={};e.id=2533,e.ids=[2533],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6637:(e,i,t)=>{"use strict";t.r(i),t.d(i,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>u,routeModule:()=>l,tree:()=>d});var r=t(95500),s=t(56947),o=t(26052),n=t(13636),a={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>n[e]);t.d(i,a);let d={children:["",{children:["dashboard",{children:["teacher",{children:["courses",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35787)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,64755)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\[id]\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},l=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/teacher/courses/[id]/page",pathname:"/dashboard/teacher/courses/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},35787:(e,i,t)=>{"use strict";let r;t.r(i),t.d(i,{default:()=>m,generateImageMetadata:()=>l,generateMetadata:()=>c,generateViewport:()=>p});var s=t(63033),o=t(1472),n=t(7688),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\[id]\\page.tsx","default");let d={...s},u="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof a?new Proxy(a,{apply:(e,i,t)=>{let r,s,o;try{let e=u?.getStore();r=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,o=e?.headers}catch{}return n.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher/courses/[id]",componentType:"Page",sentryTraceHeader:r,baggageHeader:s,headers:o}).apply(i,t)}}):a;let c=void 0,l=void 0,p=void 0,m=r},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53879:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>g});var r=t(91754),s=t(93491),o=t(21372),n=t(9260),a=t(56682),d=t(70067),u=t(4978),c=t(14908),l=t(16041),p=t.n(l),m=t(76328),h=t(81012),f=t(49742);function x(e){return{id:e.id.toString(),type:e.type,question:e.question||[{type:"text",value:""}],options:e.options||void 0,essayAnswer:e.essayAnswer,explanation:e.explanation||void 0,points:e.points,orderIndex:e.orderIndex}}function q(e){return{id:e.id.toString(),name:e.name,description:e.description,questions:e.questions?e.questions.map(x):[],timeLimit:e.timeLimit,minimumScore:parseInt(e.minimumScore)}}function y(e){let i=e.quizzes?.find(e=>"chapter"===e.quizType),t=[];if(e.content)if("string"==typeof e.content)try{t=JSON.parse(e.content)}catch(e){console.error("Error parsing chapter content:",e),t=[]}else t=e.content;return{id:e.id.toString(),name:e.name,content:t,orderIndex:e.orderIndex,hasChapterQuiz:!!i,chapterQuiz:i?q(i):void 0}}function g({params:e}){let{id:i}=(0,s.use)(e),t=(0,o.useRouter)(),[l,x]=(0,s.useState)(null),[g,z]=(0,s.useState)(!0),[v,I]=(0,s.useState)(null),[E,b]=(0,s.useState)(!1),j=async()=>{try{z(!0),I(null);let e=m.qs.getUser();if(!e)return void I("Please log in to edit course");let t=await fetch(`/api/courses/${i}?teacherId=${e.id}`),r=await t.json();if(!t.ok)throw Error(r.error||"Failed to fetch course data");if(!r.success||!r.course)throw Error("Course not found");let s=r.course;console.log("Fetched course data:",s);let o=s.moduleQuizzes||[],n=o.find(e=>"final"===e.quizType),a=o.filter(e=>"module"===e.quizType);console.log("Quiz breakdown:",{totalQuizzes:o.length,moduleQuizzes:a.length,finalExam:n?n.id:"none",quizzes:o.map(e=>({id:e.id,type:e.quizType,moduleId:e.moduleId,courseId:e.courseId}))});let d=function(e,i=[],t){console.log("Transforming API course data:",{courseId:e.id,moduleQuizzesCount:i.length,finalExam:t?t.id:"none",allQuizzes:i.map(e=>({id:e.id,type:e.quizType,moduleId:e.moduleId,courseId:e.courseId}))});let r=i.filter(e=>"module"===e.quizType),s=t||i.find(e=>"final"===e.quizType);return console.log("Filtered quizzes:",{moduleQuizzes:r.length,finalExam:s?s.id:"none"}),{name:e.name,description:e.description,instructor:e.instructor||"",courseCode:e.courseCode,type:e.type,enrollmentType:e.enrollmentType||"code",startDate:e.startDate?new Date(e.startDate):void 0,endDate:e.endDate?new Date(e.endDate):void 0,coverImagePreview:e.coverImage||e.coverPicture,isPurchasable:e.isPurchasable,price:e.price,currency:e.currency,previewMode:e.previewMode,modules:e.modules.map(e=>(function(e,i=[]){let t=i.find(i=>i.moduleId===e.id&&"module"===i.quizType);return console.log(`Module ${e.id}: Found module quiz:`,t),{id:e.id.toString(),name:e.name,description:e.description,orderIndex:e.orderIndex,chapters:e.chapters.map(y),hasModuleQuiz:!!t,moduleQuiz:t?q(t):void 0}})(e,i)),isPublished:!0,assignedClasses:[],finalExam:s?q(s):void 0,admissions:e.admissions?{requirements:e.admissions.requirements||[],applicationDeadline:e.admissions.applicationDeadline||"",prerequisites:e.admissions.prerequisites||[]}:void 0,academics:e.academics?{credits:e.academics.credits||0,workload:e.academics.workload||"",assessment:e.academics.assessment||[]}:void 0,tuitionAndFinancing:e.tuitionAndFinancing?{totalCost:e.tuitionAndFinancing.totalCost||0,paymentOptions:e.tuitionAndFinancing.paymentOptions||[],scholarships:e.tuitionAndFinancing.scholarships||[]}:void 0,careers:e.careers?{outcomes:e.careers.outcomes||[],industries:e.careers.industries||[],averageSalary:e.careers.averageSalary||""}:void 0,studentExperience:e.studentExperience?{testimonials:e.studentExperience.testimonials||[],facilities:e.studentExperience.facilities||[],support:e.studentExperience.support||[]}:void 0}}(s,o,n);console.log("Transformed data:",d),x(d)}catch(e){console.error("Error fetching course:",e),I(e instanceof Error?e.message:"Failed to fetch course data")}finally{z(!1)}},w=async e=>{try{b(!0),console.log("Starting course update with data:",e);let r=m.qs.getUser();if(!r)return void h.oR.error("Please log in to update course");let s=(console.log("Transforming wizard data to API update:",{courseId:i,modulesCount:e.modules.length,finalExamExists:!!e.finalExam,moduleQuizzes:e.modules.filter(e=>e.hasModuleQuiz).length}),{courseId:parseInt(i),name:e.name,description:e.description,courseCode:e.courseCode,type:e.type,enrollmentType:e.enrollmentType,price:e.price,currency:e.currency,isPurchasable:"purchase"===e.enrollmentType||"both"===e.enrollmentType,startDate:e.startDate?.toISOString(),endDate:e.endDate?.toISOString(),modules:e.modules.map(e=>(console.log(`Transforming module ${e.name}:`,{id:e.id,hasModuleQuiz:e.hasModuleQuiz,moduleQuizId:e.moduleQuiz?.id}),{id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,name:e.name,description:e.description,orderIndex:e.orderIndex,chapters:e.chapters.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,name:e.name,content:e.content,orderIndex:e.orderIndex,quiz:e.hasChapterQuiz&&e.chapterQuiz?{id:e.chapterQuiz.id&&!isNaN(parseInt(e.chapterQuiz.id))?parseInt(e.chapterQuiz.id):void 0,name:e.chapterQuiz.name,description:e.chapterQuiz.description,quizType:"chapter",timeLimit:e.chapterQuiz.timeLimit,minimumScore:e.chapterQuiz.minimumScore,questions:e.chapterQuiz.questions.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,type:e.type,question:e.question,options:e.options||null,essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation||null,points:e.points,orderIndex:e.orderIndex}))}:void 0})),quiz:e.hasModuleQuiz&&e.moduleQuiz?{id:e.moduleQuiz.id&&!isNaN(parseInt(e.moduleQuiz.id))?parseInt(e.moduleQuiz.id):void 0,name:e.moduleQuiz.name,description:e.moduleQuiz.description,quizType:"module",timeLimit:e.moduleQuiz.timeLimit,minimumScore:e.moduleQuiz.minimumScore,questions:e.moduleQuiz.questions.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,type:e.type,question:e.question,options:e.options||null,essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation||null,points:e.points,orderIndex:e.orderIndex}))}:void 0})),finalExam:e.finalExam?{id:e.finalExam.id&&!isNaN(parseInt(e.finalExam.id))?parseInt(e.finalExam.id):void 0,name:e.finalExam.name,description:e.finalExam.description,quizType:"final",timeLimit:e.finalExam.timeLimit,minimumScore:e.finalExam.minimumScore,questions:e.finalExam.questions.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,type:e.type,question:e.question,options:e.options||null,essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation||null,points:e.points,orderIndex:e.orderIndex}))}:void 0,admissions:e.admissions,academics:e.academics,tuitionAndFinancing:e.tuitionAndFinancing,careers:e.careers,studentExperience:e.studentExperience});console.log("Update data prepared:",s);let o=await fetch(`/api/courses/${i}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,description:s.description,courseCode:s.courseCode,type:s.type,enrollmentType:s.enrollmentType,price:s.price,currency:s.currency,isPurchasable:s.isPurchasable,startDate:s.startDate,endDate:s.endDate,teacherId:r.id,admissions:s.admissions,academics:s.academics,tuitionAndFinancing:s.tuitionAndFinancing,careers:s.careers,studentExperience:s.studentExperience})});if(!o.ok){let e=await o.json();throw Error(e.error||"Failed to update course")}for(let e of s.modules){console.log(`Processing module: ${e.name}`,{id:e.id,hasModuleQuiz:!!e.quiz,moduleQuizId:e.quiz?.id});let t=e.id;if(e.id)(await fetch(`/api/modules/${e.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,description:e.description,orderIndex:e.orderIndex,teacherId:r.id})})).ok||console.error("Failed to update module:",e.id);else{let s=await fetch("/api/modules",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,description:e.description,orderIndex:e.orderIndex,courseId:parseInt(i),teacherId:r.id})});s.ok&&(t=(await s.json()).module.id,console.log("Created new module with ID:",t))}for(let i of e.chapters){let e=i.id;if(i.id)(await fetch(`/api/chapters/${i.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,content:i.content,orderIndex:i.orderIndex,teacherId:r.id})})).ok||console.error("Failed to update chapter:",i.id);else{let s=await fetch("/api/chapters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,content:i.content,orderIndex:i.orderIndex,moduleId:t,teacherId:r.id})});s.ok&&(e=(await s.json()).chapter.id,console.log("Created new chapter with ID:",e))}if(i.quiz)if(console.log(`Processing chapter quiz for chapter ${e}:`,i.quiz.id),i.quiz.id){let e=await fetch(`/api/quizzes/${i.quiz.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.quiz.name,description:i.quiz.description,quizType:"chapter",timeLimit:i.quiz.timeLimit,minimumScore:i.quiz.minimumScore,teacherId:r.id,questions:i.quiz.questions})});if(!e.ok){console.error("Failed to update chapter quiz:",i.quiz.id);let t=await e.json();console.error("Chapter quiz update error:",t)}}else{let t=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.quiz.name,description:i.quiz.description,quizType:"chapter",timeLimit:i.quiz.timeLimit,minimumScore:i.quiz.minimumScore,chapterId:e,teacherId:r.id,questions:i.quiz.questions})});if(!t.ok){console.error("Failed to create chapter quiz");let e=await t.json();console.error("Chapter quiz creation error:",e)}}}if(e.quiz)if(console.log(`Processing module quiz for module ${t}:`,{quizId:e.quiz.id,quizName:e.quiz.name,questionsCount:e.quiz.questions.length}),e.quiz.id){let i=await fetch(`/api/quizzes/${e.quiz.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.quiz.name,description:e.quiz.description,quizType:"module",timeLimit:e.quiz.timeLimit,minimumScore:e.quiz.minimumScore,moduleId:t,teacherId:r.id,questions:e.quiz.questions})});if(i.ok)console.log("Successfully updated module quiz:",e.quiz.id);else{console.error("Failed to update module quiz:",e.quiz.id);let t=await i.json();console.error("Module quiz update error:",t)}}else{let i=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.quiz.name,description:e.quiz.description,quizType:"module",timeLimit:e.quiz.timeLimit,minimumScore:e.quiz.minimumScore,moduleId:t,teacherId:r.id,questions:e.quiz.questions})});if(i.ok){let e=await i.json();console.log("Successfully created module quiz:",e.quiz?.id)}else{console.error("Failed to create module quiz");let e=await i.json();console.error("Module quiz creation error:",e)}}}if(s.finalExam)if(console.log("Processing final exam:",{examId:s.finalExam.id,examName:s.finalExam.name,questionsCount:s.finalExam.questions.length}),s.finalExam.id){let e=await fetch(`/api/quizzes/${s.finalExam.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.finalExam.name,description:s.finalExam.description,quizType:"final",timeLimit:s.finalExam.timeLimit,minimumScore:s.finalExam.minimumScore,courseId:parseInt(i),teacherId:r.id,questions:s.finalExam.questions})});if(e.ok)console.log("Successfully updated final exam:",s.finalExam.id);else{console.error("Failed to update final exam:",s.finalExam.id);let i=await e.json();console.error("Final exam update error:",i)}}else{let e=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.finalExam.name,description:s.finalExam.description,quizType:"final",timeLimit:s.finalExam.timeLimit,minimumScore:s.finalExam.minimumScore,courseId:parseInt(i),teacherId:r.id,questions:s.finalExam.questions})});if(e.ok){let i=await e.json();console.log("Successfully created final exam:",i.quiz?.id)}else{console.error("Failed to create final exam");let i=await e.json();console.error("Final exam creation error:",i)}}h.oR.success("Course updated successfully!"),t.push("/dashboard/teacher/courses")}catch(e){console.error("Error updating course:",e),h.oR.error(e instanceof Error?e.message:"Failed to update course")}finally{b(!1)}};return g?(0,r.jsxs)("div",{className:"container mx-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(d.E,{className:"h-8 w-48 mb-2"}),(0,r.jsx)(d.E,{className:"h-4 w-96"})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(d.E,{className:"h-6 w-32"}),(0,r.jsx)(d.E,{className:"h-4 w-64"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(d.E,{className:"h-10 w-full"}),(0,r.jsx)(d.E,{className:"h-20 w-full"}),(0,r.jsx)(d.E,{className:"h-10 w-full"})]})})]})]}):v?(0,r.jsxs)("div",{className:"container mx-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(p(),{href:"/dashboard/teacher/courses",children:(0,r.jsxs)(a.$,{variant:"ghost",className:"mb-4",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Courses"]})}),(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Course"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update course information and content"})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{className:"flex items-center gap-2 text-destructive",children:[(0,r.jsx)(c.A,{className:"h-5 w-5"}),"Error Loading Course"]}),(0,r.jsx)(n.BT,{children:v})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(a.$,{onClick:j,variant:"outline",children:"Try Again"})})]})]}):l?(0,r.jsxs)("div",{className:"container mx-auto p-6","data-sentry-component":"CourseEditPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(p(),{href:"/dashboard/teacher/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(a.$,{variant:"ghost",className:"mb-4","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back to Courses"]})}),(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Course"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update course information and content"})]}),(0,r.jsx)(f.q,{initialData:l,onComplete:w,onCancel:()=>{t.push("/dashboard/teacher/courses")},"data-sentry-element":"CourseCreationWizard","data-sentry-source-file":"page.tsx"})]}):(0,r.jsxs)("div",{className:"container mx-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(p(),{href:"/dashboard/teacher/courses",children:(0,r.jsxs)(a.$,{variant:"ghost",className:"mb-4",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Courses"]})}),(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Course"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update course information and content"})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Course Not Found"}),(0,r.jsx)(n.BT,{children:"The requested course could not be found."})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(p(),{href:"/dashboard/teacher/courses",children:(0,r.jsx)(a.$,{children:"Back to Courses"})})})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69229:(e,i,t)=>{Promise.resolve().then(t.bind(t,35787))},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},93197:(e,i,t)=>{Promise.resolve().then(t.bind(t,53879))},94735:e=>{"use strict";e.exports=require("events")}};var i=require("../../../../../webpack-runtime.js");i.C(e);var t=e=>i(i.s=e),r=i.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8103,4463,1481,9882,8134,8634,5660,9646],()=>t(6637));module.exports=r})();
//# sourceMappingURL=page.js.map