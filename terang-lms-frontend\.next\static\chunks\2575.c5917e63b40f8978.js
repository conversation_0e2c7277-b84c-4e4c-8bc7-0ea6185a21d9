try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f0f6ea1f-e90d-4d06-961e-9959c5bfb6de",e._sentryDebugIdIdentifier="sentry-dbid-f0f6ea1f-e90d-4d06-961e-9959c5bfb6de")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2575],{52582:(e,t,n)=>{function r(e){a(e,"start");var t,n,r,o={},i=e.languageData||{},g=!1;for(var d in e)if(d!=i&&e.hasOwnProperty(d))for(var l=o[d]=[],u=e[d],k=0;k<u.length;k++){var f=u[k];l.push(new s(f,e)),(f.indent||f.dedent)&&(g=!0)}return{name:i.name,startState:function(){return{state:"start",pending:null,indent:g?[]:null}},copyState:function(e){var t={state:e.state,pending:e.pending,indent:e.indent&&e.indent.slice(0)};return e.stack&&(t.stack=e.stack.slice(0)),t},token:(t=o,function(e,n){if(n.pending){var r=n.pending.shift();return 0==n.pending.length&&(n.pending=null),e.pos+=r.text.length,r.token}for(var a=t[n.state],s=0;s<a.length;s++){var o=a[s],i=(!o.data.sol||e.sol())&&e.match(o.regex);if(i){o.data.next?n.state=o.data.next:o.data.push?((n.stack||(n.stack=[])).push(n.state),n.state=o.data.push):o.data.pop&&n.stack&&n.stack.length&&(n.state=n.stack.pop()),o.data.indent&&n.indent.push(e.indentation()+e.indentUnit),o.data.dedent&&n.indent.pop();var g=o.token;if(g&&g.apply&&(g=g(i)),i.length>2&&o.token&&"string"!=typeof o.token){n.pending=[];for(var d=2;d<i.length;d++)i[d]&&n.pending.push({text:i[d],token:o.token[d-1]});return e.backUp(i[0].length-(i[1]?i[1].length:0)),g[0]}if(g&&g.join)return g[0];return g}}return e.next(),null}),indent:(n=o,r=i,function(e,t){if(null==e.indent||r.dontIndentStates&&r.dontIndentStates.indexOf(e.state)>-1)return null;var a=e.indent.length-1,s=n[e.state];e:for(;;){for(var o=0;o<s.length;o++){var i=s[o];if(i.data.dedent&&!1!==i.data.dedentIfLineStart){var g=i.regex.exec(t);if(g&&g[0]){a--,(i.next||i.push)&&(s=n[i.next||i.push]),t=t.slice(g[0].length);continue e}}}break}return a<0?0:e.indent[a]}),mergeTokens:i.mergeTokens,languageData:i}}function a(e,t){if(!e.hasOwnProperty(t))throw Error("Undefined state "+t+" in simple mode")}function s(e,t){(e.next||e.push)&&a(t,e.next||e.push),this.regex=function(e,t){if(!e)return/(?:)/;var n="";return e instanceof RegExp?(e.ignoreCase&&(n="i"),e=e.source):e=String(e),RegExp("^(?:"+e+")",n)}(e.regex),this.token=function(e){if(!e)return null;if(e.apply)return e;if("string"==typeof e)return e.replace(/\./g," ");for(var t=[],n=0;n<e.length;n++)t.push(e[n]&&e[n].replace(/\./g," "));return t}(e.token),this.data=e}n.d(t,{I:()=>r})},72575:(e,t,n)=>{n.r(t),n.d(t,{factor:()=>r});let r=(0,n(52582).I)({start:[{regex:/#?!.*/,token:"comment"},{regex:/"""/,token:"string",next:"string3"},{regex:/(STRING:)(\s)/,token:["keyword",null],next:"string2"},{regex:/\S*?"/,token:"string",next:"string"},{regex:/(?:0x[\d,a-f]+)|(?:0o[0-7]+)|(?:0b[0,1]+)|(?:\-?\d+.?\d*)(?=\s)/,token:"number"},{regex:/((?:GENERIC)|\:?\:)(\s+)(\S+)(\s+)(\()/,token:["keyword",null,"def",null,"bracket"],next:"stack"},{regex:/(M\:)(\s+)(\S+)(\s+)(\S+)/,token:["keyword",null,"def",null,"tag"]},{regex:/USING\:/,token:"keyword",next:"vocabulary"},{regex:/(USE\:|IN\:)(\s+)(\S+)(?=\s|$)/,token:["keyword",null,"tag"]},{regex:/(\S+\:)(\s+)(\S+)(?=\s|$)/,token:["keyword",null,"def"]},{regex:/(?:;|\\|t|f|if|loop|while|until|do|PRIVATE>|<PRIVATE|\.|\S*\[|\]|\S*\{|\})(?=\s|$)/,token:"keyword"},{regex:/\S+[\)>\.\*\?]+(?=\s|$)/,token:"builtin"},{regex:/[\)><]+\S+(?=\s|$)/,token:"builtin"},{regex:/(?:[\+\-\=\/\*<>])(?=\s|$)/,token:"keyword"},{regex:/\S+/,token:"variable"},{regex:/\s+|./,token:null}],vocabulary:[{regex:/;/,token:"keyword",next:"start"},{regex:/\S+/,token:"tag"},{regex:/\s+|./,token:null}],string:[{regex:/(?:[^\\]|\\.)*?"/,token:"string",next:"start"},{regex:/.*/,token:"string"}],string2:[{regex:/^;/,token:"keyword",next:"start"},{regex:/.*/,token:"string"}],string3:[{regex:/(?:[^\\]|\\.)*?"""/,token:"string",next:"start"},{regex:/.*/,token:"string"}],stack:[{regex:/\)/,token:"bracket",next:"start"},{regex:/--/,token:"bracket"},{regex:/\S+/,token:"meta"},{regex:/\s+|./,token:null}],languageData:{name:"factor",dontIndentStates:["start","vocabulary","string","string3","stack"],commentTokens:{line:"!"}}})}}]);