try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="798124f9-daf2-45ea-8baf-223c8a9d258a",e._sentryDebugIdIdentifier="sentry-dbid-798124f9-daf2-45ea-8baf-223c8a9d258a")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1078],{71078:(e,n,t)=>{function r(e){for(var n={},t=e.split(" "),r=0;r<t.length;++r)n[t[r]]=!0;return n}t.r(n),t.d(n,{sieve:()=>f});var i=r("if elsif else stop require"),u=r("true false not");function a(e,n){var t,r=e.next();if("/"==r&&e.eat("*"))return n.tokenize=l,l(e,n);if("#"===r)return e.skipToEnd(),"comment";if('"'==r){return t=r,n.tokenize=function(e,n){for(var r,i=!1;null!=(r=e.next())&&(r!=t||i);)i=!i&&"\\"==r;return i||(n.tokenize=a),"string"},n.tokenize(e,n)}if("("==r)return n._indent.push("("),n._indent.push("{"),null;if("{"===r)return n._indent.push("{"),null;if(")"==r&&(n._indent.pop(),n._indent.pop()),"}"===r)return n._indent.pop(),null;if(","==r||";"==r||/[{}\(\),;]/.test(r))return null;if(/\d/.test(r))return e.eatWhile(/[\d]/),e.eat(/[KkMmGg]/),"number";if(":"==r)return e.eatWhile(/[a-zA-Z_]/),e.eatWhile(/[a-zA-Z0-9_]/),"operator";e.eatWhile(/\w/);var f=e.current();return"text"==f&&e.eat(":")?(n.tokenize=o,"string"):i.propertyIsEnumerable(f)?"keyword":u.propertyIsEnumerable(f)?"atom":null}function o(e,n){return(n._multiLineString=!0,e.sol())?("."==e.next()&&e.eol()&&(n._multiLineString=!1,n.tokenize=a),"string"):(e.eatSpace(),"#"==e.peek())?(e.skipToEnd(),"comment"):(e.skipToEnd(),"string")}function l(e,n){for(var t,r=!1;null!=(t=e.next());){if(r&&"/"==t){n.tokenize=a;break}r="*"==t}return"comment"}let f={name:"sieve",startState:function(e){return{tokenize:a,baseIndent:e||0,_indent:[]}},token:function(e,n){return e.eatSpace()?null:(n.tokenize||a)(e,n)},indent:function(e,n,t){var r=e._indent.length;return n&&"}"==n[0]&&r--,r<0&&(r=0),r*t.unit},languageData:{indentOnInput:/^\s*\}$/}}}}]);