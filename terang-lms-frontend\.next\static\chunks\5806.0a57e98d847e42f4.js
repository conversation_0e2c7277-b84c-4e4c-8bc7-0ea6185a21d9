try{let O="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new <PERSON><PERSON>Error).stack;e&&(O._sentryDebugIds=O._sentryDebugIds||{},O._sentryDebugIds[e]="5e8e6c67-5540-42b6-8430-6f5e60029cc8",O._sentryDebugIdIdentifier="sentry-dbid-5e8e6c67-5540-42b6-8430-6f5e60029cc8")}catch(O){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5806],{85806:(O,e,t)=>{t.r(e),t.d(e,{vue:()=>d,vueLanguage:()=>f});var r=t(14563),n=t(32158),a=t(31224),o=t(21769),i=t(38348),u=t(46485);let l=u.U1.deserialize({version:14,states:"%pOVOWOOObQPOOOpOSO'#C_OOOO'#Cp'#CpQVOWOOQxQPOOO!TQQOOQ!YQPOOOOOO,58y,58yO!_OSO,58yOOOO-E6n-E6nO!dQQO'#CqQ{QPOOO!iQPOOQ{QPOOO!qQPOOOOOO1G.e1G.eOOQO,59],59]OOQO-E6o-E6oO!yOpO'#CiO#RO`O'#CiQOQPOOO#ZO#tO'#CmO#fO!bO'#CmOOQO,59T,59TO#qOpO,59TO#vO`O,59TOOOO'#Cr'#CrO#{O#tO,59XOOQO,59X,59XOOOO'#Cs'#CsO$WO!bO,59XOOQO1G.o1G.oOOOO-E6p-E6pOOQO1G.s1G.sOOOO-E6q-E6q",stateData:"$g~OjOS~OQROUROkQO~OWTOXUOZUO`VO~OSXOTWO~OXUO[]OlZO~OY^O~O[_O~OT`O~OYaO~OmcOodO~OmfOogO~O^iOnhO~O_jOphO~ObkOqkOrmO~OcnOsnOtmO~OnpO~OppO~ObkOqkOrrO~OcnOsnOtrO~OWX`~",goto:"!^hPPPiPPPPPPPPPmPPPpPPsy!Q!WTROSRe]Re_QSORYSS[T^Rb[QlfRqlQogRso",nodeNames:"⚠ Content Text Interpolation InterpolationContent }} Entity Attribute VueAttributeName : Identifier @ Is ScriptAttributeValue AttributeScript AttributeScript AttributeName AttributeValue Entity Entity",maxTerm:36,nodeProps:[["isolate",-3,3,13,17,""]],skippedNodes:[0],repeatNodeCount:4,tokenData:"'y~RdXY!aYZ!a]^!apq!ars!rwx!w}!O!|!O!P#t!Q![#y![!]$s!_!`%g!b!c%l!c!}#y#R#S#y#T#j#y#j#k%q#k#o#y%W;'S#y;'S;:j$m<%lO#y~!fSj~XY!aYZ!a]^!apq!a~!wOm~~!|Oo~!b#RX`!b}!O!|!Q![!|![!]!|!c!}!|#R#S!|#T#o!|%W;'S!|;'S;:j#n<%lO!|!b#qP;=`<%l!|~#yOl~%W$QXY#t`!b}!O!|!Q![#y![!]!|!c!}#y#R#S#y#T#o#y%W;'S#y;'S;:j$m<%lO#y%W$pP;=`<%l#y~$zXX~`!b}!O!|!Q![!|![!]!|!c!}!|#R#S!|#T#o!|%W;'S!|;'S;:j#n<%lO!|~%lO[~~%qOZ~%W%xXY#t`!b}!O&e!Q![#y![!]!|!c!}#y#R#S#y#T#o#y%W;'S#y;'S;:j$m<%lO#y!b&jX`!b}!O!|!Q![!|![!]!|!c!}'V#R#S!|#T#o'V%W;'S!|;'S;:j#n<%lO!|!b'^XW!b`!b}!O!|!Q![!|![!]!|!c!}'V#R#S!|#T#o'V%W;'S!|;'S;:j#n<%lO!|",tokenizers:[6,7,new u.uC("b~RP#q#rU~XP#q#r[~aOT~~",17,4),new u.uC("!k~RQvwX#o#p!_~^TU~Opmq!]m!^;'Sm;'S;=`!X<%lOm~pUOpmq!]m!]!^!S!^;'Sm;'S;=`!X<%lOm~!XOU~~![P;=`<%lm~!bP#o#p!e~!jOk~~",72,2),new u.uC("[~RPwxU~ZOp~~",11,15),new u.uC("[~RPrsU~ZOn~~",11,14),new u.uC("!e~RQvwXwx!_~^Tc~Opmq!]m!^;'Sm;'S;=`!X<%lOm~pUOpmq!]m!]!^!S!^;'Sm;'S;=`!X<%lOm~!XOc~~![P;=`<%lm~!dOt~~",66,35),new u.uC("!e~RQrsXvw^~^Or~~cTb~Oprq!]r!^;'Sr;'S;=`!^<%lOr~uUOprq!]r!]!^!X!^;'Sr;'S;=`!^<%lOr~!^Ob~~!aP;=`<%lr~",66,33)],topRules:{Content:[0,1],Attribute:[1,7]},tokenPrec:157}),s=a.javascriptLanguage.parser.configure({top:"SingleExpression"}),p=l.configure({props:[(0,o.pn)({Text:o._A.content,Is:o._A.definitionOperator,AttributeName:o._A.attributeName,VueAttributeName:o._A.keyword,Identifier:o._A.variableName,"AttributeValue ScriptAttributeValue":o._A.attributeValue,Entity:o._A.character,"{{ }}":o._A.brace,"@ :":o._A.punctuation})]}),b={parser:s},c=p.configure({wrap:(0,i.$g)((O,e)=>"InterpolationContent"==O.name?b:null)}),S=p.configure({wrap:(0,i.$g)((O,e)=>"AttributeScript"==O.name?b:null),top:"Attribute"}),m={parser:c},g={parser:S},y=(0,n.html)();function Q(O){return O.configure({dialect:"selfClosing",wrap:(0,i.$g)(P)},"vue")}let f=Q(y.language);function P(O,e){switch(O.name){case"Attribute":return/^(@|:|v-)/.test(e.read(O.from,O.from+2))?g:null;case"Text":return m}return null}function d(O={}){let e=y;if(O.base){if("html"!=O.base.language.name||!(O.base.language instanceof r.bj))throw RangeError("The base option must be the result of calling html(...)");e=O.base}return new r.Yy(e.language==y.language?f:Q(e.language),[e.support,e.language.data.of({closeBrackets:{brackets:["{",'"']}})])}}}]);