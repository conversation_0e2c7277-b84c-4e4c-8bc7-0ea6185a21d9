try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="9769a842-f024-466d-972d-31a4f7f5df11",e._sentryDebugIdIdentifier="sentry-dbid-9769a842-f024-466d-972d-31a4f7f5df11")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6059],{3468:(e,t,s)=>{"use strict";s.d(t,{A:()=>i,q:()=>n});var r=s(12115),a=s(95155);function n(e,t){let s=r.createContext(t),n=e=>{let{children:t,...n}=e,i=r.useMemo(()=>n,Object.values(n));return(0,a.jsx)(s.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(a){let n=r.useContext(s);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function i(e,t=[]){let s=[],n=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return n.scopeName=e,[function(t,n){let i=r.createContext(n),l=s.length;s=[...s,n];let o=t=>{let{scope:s,children:n,...o}=t,d=s?.[e]?.[l]||i,c=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:c,children:n})};return o.displayName=t+"Provider",[o,function(s,a){let o=a?.[e]?.[l]||i,d=r.useContext(o);if(d)return d;if(void 0!==n)return n;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(n,...t)]}},4129:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});var r=s(12115),a=globalThis?.document?r.useLayoutEffect:()=>{}},5917:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},20764:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>l});var r=s(95155);s(12115);var a=s(32467),n=s(83101),i=s(64269);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...d}=e,c=o?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},23558:(e,t,s)=>{"use strict";s.d(t,{i:()=>l});var r,a=s(12115),n=s(4129),i=(r||(r=s.t(a,2)))[" useInsertionEffect ".trim().toString()]||n.N;function l({prop:e,defaultProp:t,onChange:s=()=>{},caller:r}){let[n,l,o]=function({defaultProp:e,onChange:t}){let[s,r]=a.useState(e),n=a.useRef(s),l=a.useRef(t);return i(()=>{l.current=t},[t]),a.useEffect(()=>{n.current!==s&&(l.current?.(s),n.current=s)},[s,n]),[s,r,l]}({defaultProp:t,onChange:s}),d=void 0!==e,c=d?e:n;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==d){let t=d?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=d},[d,r])}return[c,a.useCallback(t=>{if(d){let s="function"==typeof t?t(e):t;s!==e&&o.current?.(s)}else l(t)},[d,e,l,o])]}Symbol("RADIX:SYNC_STATE")},31936:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(95155);s(12115);var a=s(64269);function n(e){let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},42526:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(95155);s(12115);var a=s(10489),n=s(64269);function i(e){let{className:t,...s}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},47886:(e,t,s)=>{"use strict";s.d(t,{WG:()=>a,cl:()=>i,qs:()=>r});let r={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==r.getUser(),hasRole:e=>{let t=r.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>r.hasRole("super_admin"),isTeacher:()=>r.hasRole("teacher"),isStudent:()=>r.hasRole("student")},a=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=r.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let t=n();return t?t.role!==e?(window.location.href=a(t),null):t:null}},61362:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},64269:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n,z:()=>i});var r=s(2821),a=s(75889);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function i(e){var t,s;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:n="normal"}=r;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(a)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(s=["Bytes","KB","MB","GB","TB"][i])?s:"Bytes")}},66094:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>c});var r=s(95155);s(12115);var a=s(64269);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},69264:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(96063).A)("BookOpen01Icon",[["path",{d:"M12 6L12 20",stroke:"currentColor",key:"k0"}],["path",{d:"M5.98056 3.28544C9.32175 3.9216 11.3131 5.25231 12 6.01628C12.6869 5.25231 14.6782 3.9216 18.0194 3.28544C19.7121 2.96315 20.5584 2.80201 21.2792 3.41964C22 4.03727 22 5.04022 22 7.04612V14.255C22 16.0891 22 17.0061 21.5374 17.5787C21.0748 18.1512 20.0564 18.3451 18.0194 18.733C16.2037 19.0787 14.7866 19.6295 13.7608 20.1831C12.7516 20.7277 12.247 21 12 21C11.753 21 11.2484 20.7277 10.2392 20.1831C9.21344 19.6295 7.79633 19.0787 5.98056 18.733C3.94365 18.3451 2.9252 18.1512 2.4626 17.5787C2 17.0061 2 16.0891 2 14.255V7.04612C2 5.04022 2 4.03727 2.72078 3.41964C3.44157 2.80201 4.2879 2.96315 5.98056 3.28544Z",stroke:"currentColor",key:"k1"}]])},78108:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(12115);function a(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},84288:(e,t,s)=>{"use strict";s.d(t,{X:()=>n});var r=s(12115),a=s(4129);function n(e){let[t,s]=r.useState(void 0);return(0,a.N)(()=>{if(e){s({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,a=t.blockSize}else r=e.offsetWidth,a=e.offsetHeight;s({width:r,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}s(void 0)},[e]),t}},86268:(e,t,s)=>{"use strict";s.d(t,{default:()=>A});var r=s(95155),a=s(12115),n=s(18720),i=s(20764),l=s(47886),o=s(31936),d=s(61362),c=s(42526),u=s(66094),m=s(97655),h=s(69264);let x=(0,s(96063).A)("Task01Icon",[["path",{d:"M7.99805 16H11.998M7.99805 11H15.998",stroke:"currentColor",key:"k0"}],["path",{d:"M7.5 3.5C5.9442 3.54667 5.01661 3.71984 4.37477 4.36227C3.49609 5.24177 3.49609 6.6573 3.49609 9.48836L3.49609 15.9944C3.49609 18.8255 3.49609 20.241 4.37477 21.1205C5.25345 22 6.66767 22 9.49609 22L14.4961 22C17.3245 22 18.7387 22 19.6174 21.1205C20.4961 20.241 20.4961 18.8255 20.4961 15.9944V9.48836C20.4961 6.6573 20.4961 5.24177 19.6174 4.36228C18.9756 3.71984 18.048 3.54667 16.4922 3.5",stroke:"currentColor",key:"k1"}],["path",{d:"M7.49609 3.75C7.49609 2.7835 8.2796 2 9.24609 2H14.7461C15.7126 2 16.4961 2.7835 16.4961 3.75C16.4961 4.7165 15.7126 5.5 14.7461 5.5H9.24609C8.2796 5.5 7.49609 4.7165 7.49609 3.75Z",stroke:"currentColor",key:"k2"}]]);var f=s(15215),g=s(64610),p=s(25057),v=s(84265),y=s(26345),b=s(58565),w=s(1343),j=s(52619),k=s.n(j),N=s(15239);let C=()=>{let[e,t]=(0,a.useState)(0),s=[{icon:h.A,title:"Pembelajaran Interaktif",description:"Akses kursus video berkualitas tinggi dengan materi yang disusun oleh para ahli arsitektur Indonesia",highlight:"50+ Kursus Tersedia"},{icon:x,title:"Ujian Bersertifikat",description:"Uji kompetensi Anda dengan sistem ujian online yang ketat dan dapatkan sertifikat resmi IAI",highlight:"Sertifikat Diakui Nasional"},{icon:f.A,title:"Pengembangan Karir",description:"Tingkatkan kredibilitas profesional dengan sertifikasi yang diakui industri arsitektur Indonesia",highlight:"95% Peserta Berhasil"}];(0,a.useEffect)(()=>{let e=setInterval(()=>{t(e=>(e+1)%s.length)},4e3);return()=>clearInterval(e)},[s.length]);let n=s[e],i=n.icon;return(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]","data-sentry-component":"FeatureCarousel","data-sentry-source-file":"sign-in-view.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("button",{onClick:()=>{t(e=>(e-1+s.length)%s.length)},className:"p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors",children:(0,r.jsx)(g.A,{className:"w-4 h-4 text-white","data-sentry-element":"ChevronLeftIcon","data-sentry-source-file":"sign-in-view.tsx"})}),(0,r.jsx)("div",{className:"flex space-x-2",children:s.map((s,a)=>(0,r.jsx)("button",{onClick:()=>t(a),className:"w-2 h-2 rounded-full transition-colors ".concat(a===e?"bg-white":"bg-white/40")},a))}),(0,r.jsx)("button",{onClick:()=>{t(e=>(e+1)%s.length)},className:"p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-white","data-sentry-element":"ChevronRightIcon","data-sentry-source-file":"sign-in-view.tsx"})})]}),(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center",children:(0,r.jsx)(i,{className:"w-8 h-8 text-white","data-sentry-element":"Icon","data-sentry-source-file":"sign-in-view.tsx"})})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-white",children:n.title}),(0,r.jsx)("p",{className:"text-white/90 text-sm leading-relaxed",children:n.description}),(0,r.jsx)("div",{className:"inline-block bg-white/20 px-3 py-1 rounded-full",children:(0,r.jsx)("span",{className:"text-xs font-medium text-white",children:n.highlight})})]})]})]})};function A(){let[e,t]=(0,a.useState)(!1),[s,h]=(0,a.useState)(!1),[x,f]=(0,a.useState)(!1),[g,p]=(0,a.useState)(""),[j,A]=(0,a.useState)({email:"",password:"",rememberMe:!1}),I=e=>{let{name:t,value:s,type:r,checked:a}=e.target;A(e=>({...e,[t]:"checkbox"===r?a:s}))},S=async e=>{e.preventDefault(),h(!0),p("");try{let e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:j.email,password:j.password})}),t=await e.json();if(!e.ok)throw Error(t.error||"Login failed");l.qs.setUser(t.user),f(!0),n.oR.success("Login berhasil!",{description:"Selamat datang kembali, ".concat(t.user.name),duration:3e3}),setTimeout(()=>{let e=(0,l.WG)(t.user);console.log("User role:",t.user.role,"Redirecting to:",e),window.location.href=e},1500)}catch(e){p(e.message||"Terjadi kesalahan saat login")}finally{h(!1)}};return(0,r.jsx)("div",{className:"h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden","data-sentry-component":"SignInViewPage","data-sentry-source-file":"sign-in-view.tsx",children:(0,r.jsxs)("div",{className:"flex h-screen",children:[(0,r.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,r.jsxs)("svg",{width:"100%",height:"100%",xmlns:"http://www.w3.org/2000/svg","data-sentry-element":"svg","data-sentry-source-file":"sign-in-view.tsx",children:[(0,r.jsx)("defs",{"data-sentry-element":"defs","data-sentry-source-file":"sign-in-view.tsx",children:(0,r.jsx)("pattern",{id:"grid",width:"60",height:"60",patternUnits:"userSpaceOnUse","data-sentry-element":"pattern","data-sentry-source-file":"sign-in-view.tsx",children:(0,r.jsx)("path",{d:"M 60 0 L 0 0 0 60",fill:"none",stroke:"currentColor",strokeWidth:"1","data-sentry-element":"path","data-sentry-source-file":"sign-in-view.tsx"})})}),(0,r.jsx)("rect",{width:"100%",height:"100%",fill:"url(#grid)","data-sentry-element":"rect","data-sentry-source-file":"sign-in-view.tsx"})]})}),(0,r.jsx)("div",{className:"relative z-10 flex flex-col justify-between p-12 w-full",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(N.default,{src:"/assets/logo-iai-putih.png",alt:"IAI Logo",width:200,height:200,className:"object-contain","data-sentry-element":"Image","data-sentry-source-file":"sign-in-view.tsx"})}),(0,r.jsxs)("div",{className:"space-y-6 mt-12",children:[(0,r.jsxs)("h2",{className:"text-3xl font-bold leading-tight",children:["Selamat Datang di",(0,r.jsx)("br",{}),"Sistem Pembelajaran",(0,r.jsx)("br",{}),"Profesional"]}),(0,r.jsx)("p",{className:"text-lg text-white/90 leading-relaxed",children:"Platform pembelajaran online khusus untuk para arsitek profesional. Tingkatkan keahlian Anda dengan kursus berkualitas tinggi."})]}),(0,r.jsx)("div",{className:"relative mt-8",children:(0,r.jsx)(C,{"data-sentry-element":"FeatureCarousel","data-sentry-source-file":"sign-in-view.tsx"})})]})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 sm:p-8",children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-4 sm:space-y-6 py-4",children:[(0,r.jsx)("div",{className:"lg:hidden text-center mb-8",children:(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)(N.default,{src:"/assets/logo-iai.png",alt:"IAI Logo",width:200,height:200,className:"object-contain","data-sentry-element":"Image","data-sentry-source-file":"sign-in-view.tsx"})})}),(0,r.jsxs)(u.Zp,{className:"shadow-xl border-0","data-sentry-element":"Card","data-sentry-source-file":"sign-in-view.tsx",children:[(0,r.jsxs)(u.aR,{className:"text-center space-y-1 pb-6","data-sentry-element":"CardHeader","data-sentry-source-file":"sign-in-view.tsx",children:[(0,r.jsx)(u.ZB,{className:"text-2xl font-bold text-gray-900","data-sentry-element":"CardTitle","data-sentry-source-file":"sign-in-view.tsx",children:"Masuk ke Akun"}),(0,r.jsx)(u.BT,{className:"text-gray-600","data-sentry-element":"CardDescription","data-sentry-source-file":"sign-in-view.tsx",children:"Masukkan email dan password untuk mengakses kursus Anda"})]}),(0,r.jsxs)(u.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"sign-in-view.tsx",children:[g&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-red-600",children:g})}),(0,r.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{htmlFor:"email",className:"text-sm font-medium text-gray-700","data-sentry-element":"Label","data-sentry-source-file":"sign-in-view.tsx",children:"Email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400","data-sentry-element":"MailIcon","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)(o.p,{id:"email",name:"email",type:"email",placeholder:"<EMAIL>",value:j.email,onChange:I,className:"pl-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"sign-in-view.tsx"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(c.J,{htmlFor:"password",className:"text-sm font-medium text-gray-700","data-sentry-element":"Label","data-sentry-source-file":"sign-in-view.tsx",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400","data-sentry-element":"LockIcon","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)(o.p,{id:"password",name:"password",type:e?"text":"password",placeholder:"Masukkan password",value:j.password,onChange:I,className:"pl-10 pr-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)("button",{type:"button",onClick:()=>t(!e),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:e?(0,r.jsx)(b.A,{className:"w-4 h-4"}):(0,r.jsx)(w.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.S,{id:"rememberMe",name:"rememberMe",checked:j.rememberMe,onCheckedChange:e=>A(t=>({...t,rememberMe:e})),className:"data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)]","data-sentry-element":"Checkbox","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)(c.J,{htmlFor:"rememberMe",className:"text-sm text-gray-600 cursor-pointer","data-sentry-element":"Label","data-sentry-source-file":"sign-in-view.tsx",children:"Ingat saya"})]}),(0,r.jsx)(k(),{href:"/auth/forgot-password",className:"text-sm text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Lupa password?"})]}),(0,r.jsx)(i.$,{type:"submit",variant:"iai",className:"w-full h-12 text-base font-medium mt-6",disabled:s||x,"data-sentry-element":"Button","data-sentry-source-file":"sign-in-view.tsx",children:s?"Memproses...":x?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(d.A,{className:"mr-2 h-5 w-5"})," Berhasil!"]}):"Masuk ke Dashboard"})]}),(0,r.jsxs)("div",{className:"relative my-6",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t border-gray-200"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"atau"})})]}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)(i.$,{variant:"outline",className:"w-full h-11 font-medium border-gray-200 hover:bg-gray-50","data-sentry-element":"Button","data-sentry-source-file":"sign-in-view.tsx",children:[(0,r.jsxs)("svg",{className:"w-4 h-4 mr-2",viewBox:"0 0 24 24","data-sentry-element":"svg","data-sentry-source-file":"sign-in-view.tsx",children:[(0,r.jsx)("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z","data-sentry-element":"path","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z","data-sentry-element":"path","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z","data-sentry-element":"path","data-sentry-source-file":"sign-in-view.tsx"}),(0,r.jsx)("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z","data-sentry-element":"path","data-sentry-source-file":"sign-in-view.tsx"})]}),"Masuk dengan Google"]})}),(0,r.jsxs)("p",{className:"text-center text-sm text-gray-600 mt-6",children:["Belum punya akun?"," ",(0,r.jsx)(k(),{href:"/auth/sign-up",className:"text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Daftar sekarang"})]})]})]}),(0,r.jsxs)("div",{className:"text-center text-xs text-gray-500 space-y-1",children:[(0,r.jsxs)("p",{children:["Dengan masuk, Anda menyetujui"," ",(0,r.jsx)(k(),{href:"/terms",className:"underline hover:text-gray-700","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Syarat & Ketentuan"})," ","dan"," ",(0,r.jsx)(k(),{href:"/privacy",className:"underline hover:text-gray-700","data-sentry-element":"Link","data-sentry-source-file":"sign-in-view.tsx",children:"Kebijakan Privasi"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-400",children:[(0,r.jsx)("span",{children:"\xa9 2024 IAI LMS - Powered by"}),(0,r.jsx)("img",{src:"https://cdn.terang.ai/images/logo/logo-terang-ai.svg",alt:"Terang AI",className:"h-4 inline-block"})]})]})]})})})]})})}},92556:(e,t,s)=>{"use strict";function r(e,t,{checkForDefaultPrevented:s=!0}={}){return function(r){if(e?.(r),!1===s||!r.defaultPrevented)return t?.(r)}}s.d(t,{m:()=>r})},97602:(e,t,s)=>{"use strict";s.d(t,{hO:()=>o,sG:()=>l});var r=s(12115),a=s(47650),n=s(32467),i=s(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.TL)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?s:t,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function o(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},97655:(e,t,s)=>{"use strict";s.d(t,{S:()=>l});var r=s(95155);s(12115);var a=s(38162),n=s(5917),i=s(64269);function l(e){let{className:t,...s}=e;return(0,r.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,r.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,r.jsx)(n.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},97814:(e,t,s)=>{Promise.resolve().then(s.bind(s,86268))}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,8720,5239,6217,4850,8441,3840,7358],()=>t(97814)),_N_E=e.O()}]);