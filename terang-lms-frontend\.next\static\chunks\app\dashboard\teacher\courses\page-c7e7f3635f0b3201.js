try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="68a30cd6-b5e3-42c8-934c-c62779a12e56",e._sentryDebugIdIdentifier="sentry-dbid-68a30cd6-b5e3-42c8-934c-c62779a12e56")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[561],{5917:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6191:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6812:(e,t,s)=>{Promise.resolve().then(s.bind(s,74616))},11010:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},12833:(e,t,s)=>{"use strict";s.d(t,{I:()=>l,SQ:()=>c,_2:()=>u,hO:()=>m,lp:()=>h,mB:()=>x,rI:()=>o,ty:()=>i});var r=s(95155);s(12115);var a=s(47971),n=s(5917),d=s(64269);function o(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function i(e){let{...t}=e;return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function c(e){let{className:t,sideOffset:s=4,...n}=e;return(0,r.jsx)(a.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function l(e){let{...t}=e;return(0,r.jsx)(a.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:s,variant:n="default",...o}=e;return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":n,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:s,checked:o,...i}=e;return(0,r.jsxs)(a.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,r.jsx)(n.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),s]})}function h(e){let{className:t,inset:s,...n}=e;return(0,r.jsx)(a.JU,{"data-slot":"dropdown-menu-label","data-inset":s,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function x(e){let{className:t,...s}=e;return(0,r.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...s,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},20764:(e,t,s)=>{"use strict";s.d(t,{$:()=>i,r:()=>o});var r=s(95155);s(12115);var a=s(32467),n=s(83101),d=s(64269);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,size:n,asChild:i=!1,...c}=e,l=i?a.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,d.cn)(o({variant:s,size:n,className:t})),...c,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},31936:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(95155);s(12115);var a=s(64269);function n(e){let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},37772:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},47886:(e,t,s)=>{"use strict";s.d(t,{WG:()=>a,cl:()=>d,qs:()=>r});let r={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==r.getUser(),hasRole:e=>{let t=r.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>r.hasRole("super_admin"),isTeacher:()=>r.hasRole("teacher"),isStudent:()=>r.hasRole("student")},a=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=r.getUser();return e||(window.location.href="/auth/sign-in",null)},d=e=>{let t=n();return t?t.role!==e?(window.location.href=a(t),null):t:null}},47937:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},64269:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n,z:()=>d});var r=s(2821),a=s(75889);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function d(e){var t,s;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:a=0,sizeType:n="normal"}=r;if(0===e)return"0 Byte";let d=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,d)).toFixed(a)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][d])?t:"Bytest":null!=(s=["Bytes","KB","MB","GB","TB"][d])?s:"Bytes")}},66094:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>d,wL:()=>l});var r=s(95155);s(12115);var a=s(64269);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},71360:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},74616:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var r=s(95155),a=s(12115),n=s(66094),d=s(20764),o=s(31936),i=s(88021),c=s(88941),l=s(12833),u=s(6191),m=s(78192),h=s(86651),x=s(47937);let p=(0,s(71847).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);var f=s(91169),v=s(11010),g=s(37772),y=s(71360),b=s(52619),w=s.n(b),j=s(47886),N=s(18720);function k(){let[e,t]=(0,a.useState)(""),[s,b]=(0,a.useState)([]),[k,C]=(0,a.useState)(!0),[M,A]=(0,a.useState)(null);(0,a.useEffect)(()=>{_()},[]);let _=async()=>{try{let e=j.qs.getUser();if(!e)return void N.oR.error("Please log in to view courses");let t=await fetch("/api/courses?teacherId=".concat(e.id)),s=await t.json();s.success?b(s.courses||[]):N.oR.error(s.error||"Failed to fetch courses")}catch(e){console.error("Error fetching courses:",e),N.oR.error("Failed to fetch courses")}finally{C(!1)}},z=async e=>{if(confirm("Are you sure you want to delete this course? This action cannot be undone.")){A(e);try{let t=j.qs.getUser();if(!t)return void N.oR.error("Please log in to delete courses");let s=await fetch("/api/courses/".concat(e,"?teacherId=").concat(t.id),{method:"DELETE"}),r=await s.json();r.success?(N.oR.success("Course deleted successfully!"),_()):N.oR.error(r.error||"Failed to delete course")}catch(e){console.error("Error deleting course:",e),N.oR.error("Failed to delete course")}finally{A(null)}}},D=s.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase())||t.courseCode.toLowerCase().includes(e.toLowerCase())),I=e=>{navigator.clipboard.writeText(e),N.oR.success("Course code copied to clipboard!")};return k?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Courses"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Create and manage your educational courses"})]}),(0,r.jsx)("div",{className:"flex space-x-2",children:(0,r.jsx)(w(),{href:"/dashboard/teacher/courses/new",children:(0,r.jsxs)(d.$,{children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Create Course"]})})})]}),(0,r.jsx)(()=>(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6","data-sentry-component":"LoadingSkeleton","data-sentry-source-file":"page.tsx",children:[...Array(6)].map((e,t)=>(0,r.jsxs)(n.Zp,{className:"overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-video bg-muted",children:(0,r.jsx)(c.E,{className:"w-full h-full"})}),(0,r.jsxs)(n.Wu,{className:"p-4",children:[(0,r.jsx)(c.E,{className:"h-6 w-3/4 mb-2"}),(0,r.jsx)(c.E,{className:"h-4 w-full mb-2"}),(0,r.jsx)(c.E,{className:"h-4 w-2/3 mb-4"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)(c.E,{className:"h-4 w-16"}),(0,r.jsx)(c.E,{className:"h-4 w-16"})]}),(0,r.jsx)(c.E,{className:"h-8 w-8 rounded-full"})]})]})]},t))}),{})]}):(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"CoursesPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Courses"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Create and manage your educational courses"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(w(),{href:"/dashboard/teacher/courses/generate","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(d.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Bot","data-sentry-source-file":"page.tsx"}),"AI Generator"]})}),(0,r.jsx)(w(),{href:"/dashboard/teacher/courses/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(d.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Create Course"]})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(h.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,r.jsx)(o.p,{placeholder:"Search courses...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:D.map(e=>(0,r.jsxs)(n.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[(0,r.jsx)(n.aR,{className:"p-0",children:(0,r.jsx)("div",{className:"p-6 pb-0",children:(0,r.jsxs)("div",{className:"h-48 w-full overflow-hidden rounded-lg relative",children:[e.coverPicture?(0,r.jsx)("img",{src:e.coverPicture,alt:e.name,loading:"lazy",className:"h-full w-full object-cover"}):(0,r.jsx)("div",{className:"h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"h-12 w-12 text-gray-400"})}),(0,r.jsx)("div",{className:"absolute top-2 right-2",children:(0,r.jsx)(i.E,{variant:"published"===e.status?"default":"outline",children:e.status})})]})})}),(0,r.jsx)(n.Wu,{className:"p-6 pt-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg line-clamp-1",children:e.name}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm line-clamp-2 mt-1",children:e.description})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("code",{className:"bg-muted rounded px-2 py-1 text-xs",children:e.courseCode}),(0,r.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>I(e.courseCode),className:"h-6 w-6 p-0",children:(0,r.jsx)(p,{className:"h-3 w-3"})})]}),(0,r.jsx)(i.E,{variant:"verified"===e.type?"default":"secondary",children:e.type})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.moduleCount," modules"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.studentCount," students"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,r.jsx)("div",{className:"flex space-x-1",children:(0,r.jsx)(w(),{href:"/dashboard/teacher/courses/".concat(e.id),children:(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Edit"]})})}),(0,r.jsxs)(l.rI,{children:[(0,r.jsx)(l.ty,{asChild:!0,children:(0,r.jsx)(d.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(l.SQ,{align:"end",children:[(0,r.jsx)(l._2,{asChild:!0,children:(0,r.jsxs)(w(),{href:"/dashboard/teacher/courses/".concat(e.id,"/students"),children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"View Students"]})}),(0,r.jsxs)(l._2,{className:"text-red-600",onClick:()=>z(e.id),disabled:M===e.id,children:[M===e.id?(0,r.jsx)("div",{className:"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent"}):(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),M===e.id?"Deleting...":"Delete"]})]})]})]})]})})]},e.id))}),0===D.length&&(0,r.jsxs)("div",{className:"py-16 text-center",children:[(0,r.jsx)(x.A,{className:"text-muted-foreground mx-auto h-16 w-16"}),(0,r.jsx)("h3",{className:"mt-4 text-lg font-semibold",children:"No courses found"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2 text-sm max-w-sm mx-auto",children:e?"Try adjusting your search terms to find the courses you're looking for.":"Get started by creating your first course using our intuitive wizard or AI generator."}),!e&&(0,r.jsxs)("div",{className:"mt-8 flex justify-center space-x-3",children:[(0,r.jsx)(w(),{href:"/dashboard/teacher/courses/generate",children:(0,r.jsxs)(d.$,{variant:"outline",size:"lg",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"AI Generator"]})}),(0,r.jsx)(w(),{href:"/dashboard/teacher/courses/new",children:(0,r.jsxs)(d.$,{size:"lg",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Create Course"]})})]})]})]})]})}},78192:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},86651:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},88021:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(95155);s(12115);var a=s(32467),n=s(83101),d=s(64269);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,asChild:n=!1,...i}=e,c=n?a.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,d.cn)(o({variant:s}),t),...i,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},88941:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(95155),a=s(64269);function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",t),...s,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},91169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(71847).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,8720,6093,7971,4850,8441,3840,7358],()=>t(6812)),_N_E=e.O()}]);