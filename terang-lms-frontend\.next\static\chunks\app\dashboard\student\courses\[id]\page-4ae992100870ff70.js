try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="ab833651-7ee9-4d53-97e8-3dd5fa6bc543",e._sentryDebugIdIdentifier="sentry-dbid-ab833651-7ee9-4d53-97e8-3dd5fa6bc543")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[858],{184:(e,s,t)=>{Promise.resolve().then(t.bind(t,84583))},20764:(e,s,t)=>{"use strict";t.d(s,{$:()=>l,r:()=>d});var a=t(95155);t(12115);var r=t(32467),i=t(83101),n=t(64269);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:t,size:i,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:s})),...o,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},26737:(e,s,t)=>{"use strict";t.d(s,{k:()=>d});var a=t(95155),r=t(12115),i=t(9484),n=t(64269);let d=r.forwardRef((e,s)=>{let{className:t,value:r,...d}=e;return(0,a.jsx)(i.bL,{ref:s,className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...d,children:(0,a.jsx)(i.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});d.displayName=i.bL.displayName},64269:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i,z:()=>n});var a=t(2821),r=t(75889);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}function n(e){var s,t;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:r=0,sizeType:i="normal"}=a;if(0===e)return"0 Byte";let n=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,n)).toFixed(r)," ").concat("accurate"===i?null!=(s=["Bytes","KiB","MiB","GiB","TiB"][n])?s:"Bytest":null!=(t=["Bytes","KB","MB","GB","TB"][n])?t:"Bytes")}},66094:(e,s,t)=>{"use strict";t.d(s,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>c});var a=t(95155);t(12115);var r=t(64269);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",s),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},84583:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(95155),r=t(66094),i=t(20764),n=t(88021),d=t(26737),l=t(42529),o=t(99708),c=t(47937),u=t(6132),m=t(89715),x=t(52472),p=t(85921),g=t(12115);function h(e){let{params:s}=e,{id:t}=(0,g.use)(s),h={id:parseInt(t),name:"Introduction to Algebra",description:"Basic algebraic concepts and problem solving",type:"self_paced",courseCode:"MATH101",instructor:"Dr. Smith",progress:76,modules:[{id:1,name:"Introduction and Fundamentals",description:"Basic concepts and introduction to algebra",isUnlocked:!0,progress:100,chapters:[{id:1,name:"Chapter 1: Overview",description:"Introduction to algebraic thinking",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:1,quizCompleted:!0,quizScore:92},{id:2,name:"Chapter 2: Basic Concepts",description:"Fundamental algebraic principles",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:2,quizCompleted:!0,quizScore:88},{id:3,name:"Chapter 3: Variables and Expressions",description:"Working with variables and expressions",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:3,quizCompleted:!0,quizScore:95}],hasModuleQuiz:!0,moduleQuizId:4,moduleQuizCompleted:!0,moduleQuizScore:90},{id:2,name:"Core Algebraic Operations",description:"Essential operations and manipulations",isUnlocked:!0,progress:60,chapters:[{id:4,name:"Chapter 4: Addition and Subtraction",description:"Basic algebraic operations",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:5,quizCompleted:!0,quizScore:85},{id:5,name:"Chapter 5: Multiplication and Division",description:"Advanced algebraic operations",isCompleted:!1,isUnlocked:!0,hasQuiz:!0,quizId:6,quizCompleted:!1,quizScore:null}],hasModuleQuiz:!0,moduleQuizId:7,moduleQuizCompleted:!1,moduleQuizScore:null},{id:3,name:"Advanced Topics",description:"Complex algebraic concepts",isUnlocked:!1,progress:0,chapters:[{id:6,name:"Chapter 6: Equations",description:"Solving algebraic equations",isCompleted:!1,isUnlocked:!1,hasQuiz:!0,quizId:8,quizCompleted:!1,quizScore:null}],hasModuleQuiz:!0,moduleQuizId:9,moduleQuizCompleted:!1,moduleQuizScore:null}],hasFinalExam:!0,finalExamId:10,finalExamUnlocked:!1,finalExamCompleted:!1,finalExamScore:null},f=e=>e.isUnlocked?e.isCompleted&&e.quizCompleted?"completed":e.isCompleted&&!e.quizCompleted?"quiz-pending":"in-progress":"locked",v=e=>{if(!e.isUnlocked)return"locked";let s=e.chapters.every(e=>e.isCompleted&&e.quizCompleted);return s&&e.moduleQuizCompleted?"completed":s&&!e.moduleQuizCompleted?"quiz-pending":"in-progress"};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentCourseDetailPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(r.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(r.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:h.name}),(0,a.jsx)(n.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:h.type})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:h.description}),(0,a.jsxs)("div",{className:"text-muted-foreground flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("span",{children:["Code: ",h.courseCode]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:["Instructor: ",h.instructor]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-primary text-3xl font-bold",children:[h.progress,"%"]}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Complete"})]})]})}),(0,a.jsx)(r.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(d.k,{value:h.progress,className:"h-3","data-sentry-element":"Progress","data-sentry-source-file":"page.tsx"})})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[h.modules.map((e,s)=>{let t=v(e);return(0,a.jsxs)(r.Zp,{className:"".concat(e.isUnlocked?"":"opacity-60"),children:[(0,a.jsx)(r.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-full ".concat("completed"===t?"bg-green-100 text-green-600":"quiz-pending"===t?"bg-yellow-100 text-yellow-600":"locked"===t?"bg-gray-100 text-gray-400":"bg-blue-100 text-blue-600"),children:"completed"===t?(0,a.jsx)(l.A,{className:"h-5 w-5"}):"locked"===t?(0,a.jsx)(o.A,{className:"h-5 w-5"}):(0,a.jsx)(c.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(r.ZB,{className:"text-lg",children:["Module ",s+1,": ",e.name]}),(0,a.jsx)(r.BT,{children:e.description})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-xl font-bold",children:[e.progress,"%"]}),(0,a.jsx)(d.k,{value:e.progress,className:"h-2 w-20"})]})]})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[e.chapters.map((e,s)=>{let t=f(e);return(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-3 ".concat(e.isUnlocked?"":"bg-gray-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex h-8 w-8 items-center justify-center rounded-full ".concat("completed"===t?"bg-green-100 text-green-600":"quiz-pending"===t?"bg-yellow-100 text-yellow-600":"locked"===t?"bg-gray-100 text-gray-400":"bg-blue-100 text-blue-600"),children:"completed"===t?(0,a.jsx)(l.A,{className:"h-4 w-4"}):"quiz-pending"===t?(0,a.jsx)(u.A,{className:"h-4 w-4"}):"locked"===t?(0,a.jsx)(o.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description}),e.quizScore&&(0,a.jsxs)("p",{className:"text-xs text-green-600",children:["Quiz Score: ",e.quizScore,"%"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isUnlocked&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",disabled:!e.isUnlocked,children:[(0,a.jsx)(c.A,{className:"mr-1 h-3 w-3"}),e.isCompleted?"Review":"Study"]}),e.hasQuiz&&(0,a.jsxs)(i.$,{size:"sm",disabled:!e.isCompleted||e.quizCompleted,variant:e.quizCompleted?"outline":"default",children:[(0,a.jsx)(m.A,{className:"mr-1 h-3 w-3"}),e.quizCompleted?"Quiz: ".concat(e.quizScore,"%"):"Take Quiz"]})]}),!e.isUnlocked&&(0,a.jsxs)(n.E,{variant:"outline",children:[(0,a.jsx)(o.A,{className:"mr-1 h-3 w-3"}),"Locked"]})]})]},e.id)}),e.hasModuleQuiz&&(0,a.jsx)("div",{className:"mt-4 rounded-lg border-2 border-dashed p-3 ".concat("quiz-pending"===t?"border-yellow-300 bg-yellow-50":e.moduleQuizCompleted?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 ".concat(e.moduleQuizCompleted?"text-green-600":"quiz-pending"===t?"text-yellow-600":"text-gray-400")}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"font-medium",children:["Module Quiz: ",e.name]}),e.moduleQuizScore&&(0,a.jsxs)("span",{className:"ml-2 text-sm text-green-600",children:["Score: ",e.moduleQuizScore,"%"]})]})]}),(0,a.jsx)(i.$,{size:"sm",disabled:"quiz-pending"!==t,variant:e.moduleQuizCompleted?"outline":"default",children:e.moduleQuizCompleted?"Review Quiz":"Take Module Quiz"})]})})]})})]},e.id)}),h.hasFinalExam&&(0,a.jsx)(r.Zp,{className:"border-2 ".concat(h.finalExamUnlocked?"border-yellow-300 bg-yellow-50":"border-gray-300 bg-gray-50"),children:(0,a.jsx)(r.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-full ".concat(h.finalExamCompleted?"bg-green-100 text-green-600":h.finalExamUnlocked?"bg-yellow-100 text-yellow-600":"bg-gray-100 text-gray-400"),children:h.finalExamCompleted?(0,a.jsx)(l.A,{className:"h-6 w-6"}):h.finalExamUnlocked?(0,a.jsx)(x.A,{className:"h-6 w-6"}):(0,a.jsx)(o.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Final Examination"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm",children:"Comprehensive exam covering all course materials"}),!h.finalExamUnlocked&&(0,a.jsx)("p",{className:"mt-1 text-xs text-yellow-600",children:"Complete all modules to unlock"})]})]}),(0,a.jsx)(i.$,{size:"lg",disabled:!h.finalExamUnlocked,variant:h.finalExamCompleted?"outline":"default",children:h.finalExamCompleted?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"View Certificate"]}):h.finalExamUnlocked?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Take Final Exam"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Locked"]})})]})})})]})]})}},88021:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155);t(12115);var r=t(32467),i=t(83101),n=t(64269);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,asChild:i=!1,...l}=e,o=i?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),s),...l,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4909,1799,4850,8441,3840,7358],()=>s(184)),_N_E=e.O()}]);