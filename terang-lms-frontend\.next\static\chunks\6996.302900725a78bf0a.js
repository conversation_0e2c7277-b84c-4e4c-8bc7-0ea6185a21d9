try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="a020c200-07bc-4488-a083-fba62449f161",e._sentryDebugIdIdentifier="sentry-dbid-a020c200-07bc-4488-a083-fba62449f161")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6996],{26996:(e,n,t)=>{t.r(n),t.d(n,{mbox:()=>h});var a=["From","Sender","Reply-To","To","Cc","Bcc","Message-ID","In-Reply-To","References","Resent-From","Resent-Sender","Resent-To","Resent-Cc","Resent-Bcc","Resent-Message-ID","Return-Path","Received"],r=["Date","Subject","Comments","Keywords","Resent-Date"],i=/^[ \t]/,d=/^From /,o=RegExp("^("+a.join("|")+"): "),s=RegExp("^("+r.join("|")+"): "),l=/^[^:]+:/,c=/^[^ ]+@[^ ]+/,u=/^.*?(?=[^ ]+?@[^ ]+)/,m=/^<.*?>/,f=/^.*?(?=<.*>)/;let h={name:"mbox",startState:function(){return{inSeparator:!1,inHeader:!1,emailPermitted:!1,header:null,inHeaders:!1}},token:function(e,n){if(e.sol()){if(n.inSeparator=!1,n.inHeader&&e.match(i))return null;if(n.inHeader=!1,n.header=null,e.match(d))return n.inHeaders=!0,n.inSeparator=!0,"atom";var t,a=!1;return(t=e.match(s))||(a=!0,t=e.match(o))?(n.inHeaders=!0,n.inHeader=!0,n.emailPermitted=a,n.header=t[1],"atom"):n.inHeaders&&(t=e.match(l))?(n.inHeader=!0,n.emailPermitted=!0,n.header=t[1],"atom"):(n.inHeaders=!1,e.skipToEnd(),null)}if(n.inSeparator)return e.match(c)?"link":(e.match(u)||e.skipToEnd(),"atom");if(n.inHeader){var r="Subject"===n.header?"header":"string";if(n.emailPermitted){if(e.match(m))return r+" link";if(e.match(f))return r}return e.skipToEnd(),r}return e.skipToEnd(),null},blankLine:function(e){e.inHeaders=e.inSeparator=e.inHeader=!1},languageData:{autocomplete:a.concat(r)}}}}]);