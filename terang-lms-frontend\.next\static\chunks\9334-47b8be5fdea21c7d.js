try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="a5e4fb61-8467-480f-85c7-6ddc88de8360",t._sentryDebugIdIdentifier="sentry-dbid-a5e4fb61-8467-480f-85c7-6ddc88de8360")}catch(t){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9334],{47734:(t,e,r)=>{r.d(e,{W:()=>b});var n=r(12115),i=r(2821),o=r(39830),a=r(64396),c=r(24719);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(u=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function y(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d(t){var e=t.xAxisId,r=(0,o.yi)(),l=(0,o.rY)(),u=(0,o.AF)(e);return null==u?null:n.createElement(a.u,h({},u,{className:(0,i.A)("recharts-".concat(u.axisType," ").concat(u.axisType),u.className),viewBox:{x:0,y:0,width:r,height:l},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))}var b=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,u()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f(r,t),e=[{key:"render",value:function(){return n.createElement(d,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,p(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);y(b,"displayName","XAxis"),y(b,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},64396:(t,e,r)=>{r.d(e,{u:()=>T});var n=r(12115),i=r(71730),o=r.n(i),a=r(98134),c=r.n(a),l=r(2821),u=r(23999),s=r(87095),f=r(39346),y=r(63296),p=r(49580),h=r(84072),d=r(70543),b=r(96878),v=["viewBox"],m=["viewBox"],g=["ticks"];function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,N(n.key),n)}}function S(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(S=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function A(t,e){return(A=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function C(t,e,r){return(e=N(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var T=function(t){var e,r;function i(t){var e,r,n;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return r=i,n=[t],r=E(r),(e=function(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,S()?Reflect.construct(r,n||[],E(this).constructor):r.apply(this,n))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&A(i,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=x(t,v),i=this.props,o=i.viewBox,a=x(i,m);return!(0,u.b)(r,o)||!(0,u.b)(n,a)||!(0,u.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,l=c.x,u=c.y,s=c.width,f=c.height,y=c.orientation,h=c.tickSize,d=c.mirror,b=c.tickMargin,v=d?-1:1,m=t.tickSize||h,g=(0,p.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(y){case"top":e=r=t.coordinate,a=(n=(i=u+!d*f)-v*m)-v*b,o=g;break;case"left":n=i=t.coordinate,o=(e=(r=l+!d*s)-v*m)-v*b,a=g;break;case"right":n=i=t.coordinate,o=(e=(r=l+d*s)+v*m)+v*b,a=g;break;default:e=r=t.coordinate,a=(n=(i=u+d*f)+v*m)+v*b,o=g}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.orientation,u=t.mirror,s=t.axisLine,f=j(j(j({},(0,d.J9)(this.props,!1)),(0,d.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var y=+("top"===a&&!u||"bottom"===a&&u);f=j(j({},f),{},{x1:e,y1:r+y*o,x2:e+i,y2:r+y*o})}else{var p=+("left"===a&&!u||"right"===a&&u);f=j(j({},f),{},{x1:e+p*i,y1:r,x2:e+p*i,y2:r+o})}return n.createElement("line",w({},f,{className:(0,l.A)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var a=this,u=this.props,f=u.tickLine,y=u.stroke,p=u.tick,v=u.tickFormatter,m=u.unit,g=(0,b.f)(j(j({},this.props),{},{ticks:t}),e,r),O=this.getTickTextAnchor(),k=this.getTickVerticalAnchor(),x=(0,d.J9)(this.props,!1),P=(0,d.J9)(p,!1),S=j(j({},x),{},{fill:"none"},(0,d.J9)(f,!1)),E=g.map(function(t,e){var r=a.getTickLineCoord(t),u=r.line,d=r.tick,b=j(j(j(j({textAnchor:O,verticalAnchor:k},x),{},{stroke:"none",fill:y},P),d),{},{index:e,payload:t,visibleTicksCount:g.length,tickFormatter:v});return n.createElement(s.W,w({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,h.XC)(a.props,t,e)),f&&n.createElement("line",w({},S,u,{className:(0,l.A)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),p&&i.renderTickItem(p,b,"".concat(o()(v)?v(t.value,e):t.value).concat(m||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,i=e.width,a=e.height,c=e.ticksGenerator,u=e.className;if(e.hide)return null;var f=this.props,p=f.ticks,h=x(f,g),d=p;return(o()(c)&&(d=c(p&&p.length>0?this.props:h)),i<=0||a<=0||!d||!d.length)?null:n.createElement(s.W,{className:(0,l.A)("recharts-cartesian-axis",u),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(d,this.state.fontSize,this.state.letterSpacing),y.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var i,a=(0,l.A)(e.className,"recharts-cartesian-axis-tick-value");return n.isValidElement(t)?n.cloneElement(t,j(j({},e),{},{className:a})):o()(t)?t(j(j({},e),{},{className:a})):n.createElement(f.E,w({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&P(i.prototype,e),r&&P(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.Component);C(T,"displayName","CartesianAxis"),C(T,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},68425:(t,e,r)=>{r.d(e,{d:()=>C});var n=r(12115),i=r(71730),o=r.n(i),a=r(58672),c=r(49580),l=r(70543),u=r(24719),s=r(96878),f=r(64396),y=r(39830),p=["x1","y1","x2","y2","key"],h=["offset"];function d(t){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=d(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var O=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,i=t.x,o=t.y,a=t.width,c=t.height,l=t.ry;return n.createElement("rect",{x:i,y:o,ry:l,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function w(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(o()(t))r=t(e);else{var i=e.x1,a=e.y1,c=e.x2,u=e.y2,s=e.key,f=g(e,p),y=(0,l.J9)(f,!1),d=(y.offset,g(y,h));r=n.createElement("line",m({},d,{x1:i,y1:a,x2:c,y2:u,fill:"none",key:s}))}return r}function k(t){var e=t.x,r=t.width,i=t.horizontal,o=void 0===i||i,a=t.horizontalPoints;if(!o||!a||!a.length)return null;var c=a.map(function(n,i){return w(o,v(v({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function j(t){var e=t.y,r=t.height,i=t.vertical,o=void 0===i||i,a=t.verticalPoints;if(!o||!a||!a.length)return null;var c=a.map(function(n,i){return w(o,v(v({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function x(t){var e=t.horizontalFill,r=t.fillOpacity,i=t.x,o=t.y,a=t.width,c=t.height,l=t.horizontalPoints,u=t.horizontal;if(!(void 0===u||u)||!e||!e.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,l){var u=s[l+1]?s[l+1]-t:o+c-t;if(u<=0)return null;var f=l%e.length;return n.createElement("rect",{key:"react-".concat(l),y:t,x:i,height:u,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,r=t.verticalFill,i=t.fillOpacity,o=t.x,a=t.y,c=t.width,l=t.height,u=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var u=s[e+1]?s[e+1]-t:o+c-t;if(u<=0)return null;var f=e%r.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:a,width:u,height:l,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var S=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return(0,u.PW)((0,s.f)(v(v(v({},f.u.defaultProps),r),{},{ticks:(0,u.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},E=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return(0,u.PW)((0,s.f)(v(v(v({},f.u.defaultProps),r),{},{ticks:(0,u.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},A={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function C(t){var e,r,i,l,u,s,f=(0,y.yi)(),p=(0,y.rY)(),h=(0,y.hj)(),b=v(v({},t),{},{stroke:null!=(e=t.stroke)?e:A.stroke,fill:null!=(r=t.fill)?r:A.fill,horizontal:null!=(i=t.horizontal)?i:A.horizontal,horizontalFill:null!=(l=t.horizontalFill)?l:A.horizontalFill,vertical:null!=(u=t.vertical)?u:A.vertical,verticalFill:null!=(s=t.verticalFill)?s:A.verticalFill,x:(0,c.Et)(t.x)?t.x:h.left,y:(0,c.Et)(t.y)?t.y:h.top,width:(0,c.Et)(t.width)?t.width:h.width,height:(0,c.Et)(t.height)?t.height:h.height}),g=b.x,w=b.y,C=b.width,N=b.height,T=b.syncWithTicks,_=b.horizontalValues,z=b.verticalValues,D=(0,y.pj)(),B=(0,y.$G)();if(!(0,c.Et)(C)||C<=0||!(0,c.Et)(N)||N<=0||!(0,c.Et)(g)||g!==+g||!(0,c.Et)(w)||w!==+w)return null;var R=b.verticalCoordinatesGenerator||S,F=b.horizontalCoordinatesGenerator||E,I=b.horizontalPoints,G=b.verticalPoints;if((!I||!I.length)&&o()(F)){var L=_&&_.length,J=F({yAxis:B?v(v({},B),{},{ticks:L?_:B.ticks}):void 0,width:f,height:p,offset:h},!!L||T);(0,a.R)(Array.isArray(J),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(d(J),"]")),Array.isArray(J)&&(I=J)}if((!G||!G.length)&&o()(R)){var V=z&&z.length,W=R({xAxis:D?v(v({},D),{},{ticks:V?z:D.ticks}):void 0,width:f,height:p,offset:h},!!V||T);(0,a.R)(Array.isArray(W),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(d(W),"]")),Array.isArray(W)&&(G=W)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(O,{fill:b.fill,fillOpacity:b.fillOpacity,x:b.x,y:b.y,width:b.width,height:b.height,ry:b.ry}),n.createElement(k,m({},b,{offset:h,horizontalPoints:I,xAxis:D,yAxis:B})),n.createElement(j,m({},b,{offset:h,verticalPoints:G,xAxis:D,yAxis:B})),n.createElement(x,m({},b,{horizontalPoints:I})),n.createElement(P,m({},b,{verticalPoints:G})))}C.displayName="CartesianGrid"},73697:(t,e,r)=>{r.d(e,{h:()=>b});var n=r(12115),i=r(2821),o=r(39830),a=r(64396),c=r(24719);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(u=function(){return!!t})()}function s(t){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function y(t,e,r){return(e=p(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function p(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var d=function(t){var e=t.yAxisId,r=(0,o.yi)(),l=(0,o.rY)(),u=(0,o.Nk)(e);return null==u?null:n.createElement(a.u,h({},u,{className:(0,i.A)("recharts-".concat(u.axisType," ").concat(u.axisType),u.className),viewBox:{x:0,y:0,width:r,height:l},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))},b=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=s(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,u()?Reflect.construct(t,e||[],s(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f(r,t),e=[{key:"render",value:function(){return n.createElement(d,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,p(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);y(b,"displayName","YAxis"),y(b,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},96878:(t,e,r)=>{r.d(e,{f:()=>h});var n=r(71730),i=r.n(n),o=r(49580),a=r(36462),c=r(33692),l=r(36164);function u(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function s(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?y(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){var n,f,y,h,d,b=t.tick,v=t.ticks,m=t.viewBox,g=t.minTickGap,O=t.orientation,w=t.interval,k=t.tickFormatter,j=t.unit,x=t.angle;if(!v||!v.length||!b)return[];if((0,o.Et)(w)||c.m.isSsr)return u(v,("number"==typeof w&&(0,o.Et)(w)?w:0)+1);var P=[],S="top"===O||"bottom"===O?"width":"height",E=j&&"width"===S?(0,a.Pu)(j,{fontSize:e,letterSpacing:r}):{width:0,height:0},A=function(t,n){var o,c,u=i()(k)?k(t.value,n):t.value;return"width"===S?(o=(0,a.Pu)(u,{fontSize:e,letterSpacing:r}),c={width:o.width+E.width,height:o.height+E.height},(0,l.bx)(c,x)):(0,a.Pu)(u,{fontSize:e,letterSpacing:r})[S]},C=v.length>=2?(0,o.sA)(v[1].coordinate-v[0].coordinate):1,N=(n="width"===S,f=m.x,y=m.y,h=m.width,d=m.height,1===C?{start:n?f:y,end:n?f+h:y+d}:{start:n?f+h:y+d,end:n?f:y});return"equidistantPreserveStart"===w?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,l=e.end,f=0,y=1,p=c;y<=a.length;)if(o=function(){var e,o=null==n?void 0:n[f];if(void 0===o)return{v:u(n,y)};var a=f,h=function(){return void 0===e&&(e=r(o,a)),e},d=o.coordinate,b=0===f||s(t,d,h,p,l);b||(f=0,p=c,y+=1),b&&(p=d+t*(h()/2+i),f+=y)}())return o.v;return[]}(C,N,A,v,g):("preserveStart"===w||"preserveStartEnd"===w?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,l=e.start,u=e.end;if(o){var f=n[c-1],y=r(f,c-1),h=t*(f.coordinate+t*y/2-u);a[c-1]=f=p(p({},f),{},{tickCoord:h>0?f.coordinate-h*t:f.coordinate}),s(t,f.tickCoord,function(){return y},l,u)&&(u=f.tickCoord-t*(y/2+i),a[c-1]=p(p({},f),{},{isShow:!0}))}for(var d=o?c-1:c,b=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var f=t*(o.coordinate-t*c()/2-l);a[e]=o=p(p({},o),{},{tickCoord:f<0?o.coordinate-f*t:o.coordinate})}else a[e]=o=p(p({},o),{},{tickCoord:o.coordinate});s(t,o.tickCoord,c,l,u)&&(l=o.tickCoord+t*(c()/2+i),a[e]=p(p({},o),{},{isShow:!0}))},v=0;v<d;v++)b(v);return a}(C,N,A,v,g,"preserveStartEnd"===w):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,l=e.end,u=function(e){var n,u=o[e],f=function(){return void 0===n&&(n=r(u,e)),n};if(e===a-1){var y=t*(u.coordinate+t*f()/2-l);o[e]=u=p(p({},u),{},{tickCoord:y>0?u.coordinate-y*t:u.coordinate})}else o[e]=u=p(p({},u),{},{tickCoord:u.coordinate});s(t,u.tickCoord,f,c,l)&&(l=u.tickCoord-t*(f()/2+i),o[e]=p(p({},u),{},{isShow:!0}))},f=a-1;f>=0;f--)u(f);return o}(C,N,A,v,g)).filter(function(t){return t.isShow})}}}]);