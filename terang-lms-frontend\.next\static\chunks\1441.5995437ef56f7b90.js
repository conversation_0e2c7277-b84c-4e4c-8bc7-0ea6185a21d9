try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d7571006-bd3c-4b33-9825-457dcd5c7395",e._sentryDebugIdIdentifier="sentry-dbid-d7571006-bd3c-4b33-9825-457dcd5c7395")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1441],{1441:(e,t,n)=>{function r(e){for(var t={},n=e.split(" "),r=0;r<n.length;++r)t[n[r]]=!0;return t}n.r(t),n.d(t,{asn1:()=>s});let i={keywords:r("DEFINITIONS OBJECTS IF DERIVED INFORMATION ACTION REPLY ANY NAMED CHARACTERIZED BEHAVIOUR REGISTERED WITH AS IDENTIFIED CONSTRAINED BY PRESENT BEGIN IMPORTS FROM UNITS SYNTAX MIN-ACCESS MAX-ACCESS MINACCESS MAXACCESS REVISION STATUS DESCRIPTION SEQUENCE SET COMPONENTS OF CHOICE DistinguishedName ENUMERATED SIZE MODULE END INDEX AUGMENTS EXTENSIBILITY IMPLIED EXPORTS"),cmipVerbs:r("ACTIONS ADD GET NOTIFICATIONS REPLACE REMOVE"),compareTypes:r("OPTIONAL DEFAULT MANAGED MODULE-TYPE MODULE_IDENTITY MODULE-COMPLIANCE OBJECT-TYPE OBJECT-IDENTITY OBJECT-COMPLIANCE MODE CONFIRMED CONDITIONAL SUBORDINATE SUPERIOR CLASS TRUE FALSE NULL TEXTUAL-CONVENTION"),status:r("current deprecated mandatory obsolete"),tags:r("APPLICATION AUTOMATIC EXPLICIT IMPLICIT PRIVATE TAGS UNIVERSAL"),storage:r("BOOLEAN INTEGER OBJECT IDENTIFIER BIT OCTET STRING UTCTime InterfaceIndex IANAifType CMIP-Attribute REAL PACKAGE PACKAGES IpAddress PhysAddress NetworkAddress BITS BMPString TimeStamp TimeTicks TruthValue RowStatus DisplayString GeneralString GraphicString IA5String NumericString PrintableString SnmpAdminString TeletexString UTF8String VideotexString VisibleString StringStore ISO646String T61String UniversalString Unsigned32 Integer32 Gauge Gauge32 Counter Counter32 Counter64"),modifier:r("ATTRIBUTE ATTRIBUTES MANDATORY-GROUP MANDATORY-GROUPS GROUP GROUPS ELEMENTS EQUALITY ORDERING SUBSTRINGS DEFINED"),accessTypes:r("not-accessible accessible-for-notify read-only read-create read-write"),multiLineStrings:!0};function s(e){var t,n=e.keywords||i.keywords,r=e.cmipVerbs||i.cmipVerbs,s=e.compareTypes||i.compareTypes,a=e.status||i.status,E=e.tags||i.tags,o=e.storage||i.storage,I=e.modifier||i.modifier,T=e.accessTypes||i.accessTypes,u=e.multiLineStrings||i.multiLineStrings,l=!1!==e.indentStatements,S=/[\|\^]/;function d(e,t,n,r,i){this.indented=e,this.column=t,this.type=n,this.align=r,this.prev=i}function c(e,t,n){var r=e.indented;return e.context&&"statement"==e.context.type&&(r=e.context.indented),e.context=new d(r,t,n,null,e.context)}function p(e){var t=e.context.type;return(")"==t||"]"==t||"}"==t)&&(e.indented=e.context.indented),e.context=e.context.prev}return{name:"asn1",startState:function(){return{tokenize:null,context:new d(-2,0,"top",!1),indented:0,startOfLine:!0}},token:function(e,i){var d=i.context;if(e.sol()&&(null==d.align&&(d.align=!1),i.indented=e.indentation(),i.startOfLine=!0),e.eatSpace())return null;t=null;var N=(i.tokenize||function(e,i){var l,d=e.next();if('"'==d||"'"==d){return l=d,i.tokenize=function(e,t){for(var n,r=!1,i=!1;null!=(n=e.next());){if(n==l&&!r){var s=e.peek();s&&("b"==(s=s.toLowerCase())||"h"==s||"o"==s)&&e.next(),i=!0;break}r=!r&&"\\"==n}return(i||!(r||u))&&(t.tokenize=null),"string"},i.tokenize(e,i)}if(/[\[\]\(\){}:=,;]/.test(d))return t=d,"punctuation";if("-"==d&&e.eat("-"))return e.skipToEnd(),"comment";if(/\d/.test(d))return e.eatWhile(/[\w\.]/),"number";if(S.test(d))return e.eatWhile(S),"operator";e.eatWhile(/[\w\-]/);var c=e.current();return n.propertyIsEnumerable(c)?"keyword":r.propertyIsEnumerable(c)?"variableName":s.propertyIsEnumerable(c)?"atom":a.propertyIsEnumerable(c)?"comment":E.propertyIsEnumerable(c)?"typeName":o.propertyIsEnumerable(c)||I.propertyIsEnumerable(c)||T.propertyIsEnumerable(c)?"modifier":"variableName"})(e,i);if("comment"==N)return N;if(null==d.align&&(d.align=!0),(";"==t||":"==t||","==t)&&"statement"==d.type)p(i);else if("{"==t)c(i,e.column(),"}");else if("["==t)c(i,e.column(),"]");else if("("==t)c(i,e.column(),")");else if("}"==t){for(;"statement"==d.type;)d=p(i);for("}"==d.type&&(d=p(i));"statement"==d.type;)d=p(i)}else t==d.type?p(i):l&&(("}"==d.type||"top"==d.type)&&";"!=t||"statement"==d.type&&"newstatement"==t)&&c(i,e.column(),"statement");return i.startOfLine=!1,N},languageData:{indentOnInput:/^\s*[{}]$/,commentTokens:{line:"--"}}}}}}]);