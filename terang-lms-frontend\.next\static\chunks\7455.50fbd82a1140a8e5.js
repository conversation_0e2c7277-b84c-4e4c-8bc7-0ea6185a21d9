try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8c502c56-a4e3-42f7-9d9b-77f0864f577d",e._sentryDebugIdIdentifier="sentry-dbid-8c502c56-a4e3-42f7-9d9b-77f0864f577d")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7455],{57455:(e,t,a)=>{a.r(t),a.d(t,{ebnf:()=>c});var r={slash:0,parenthesis:1},n={comment:0,_string:1,characterClass:2};let c={name:"ebnf",startState:function(){return{stringType:null,commentType:null,braced:0,lhs:!0,localState:null,stack:[],inDefinition:!1}},token:function(e,t){if(e){switch(0===t.stack.length&&('"'==e.peek()||"'"==e.peek()?(t.stringType=e.peek(),e.next(),t.stack.unshift(n._string)):e.match("/*")?(t.stack.unshift(n.comment),t.commentType=r.slash):e.match("(*")&&(t.stack.unshift(n.comment),t.commentType=r.parenthesis)),t.stack[0]){case n._string:for(;t.stack[0]===n._string&&!e.eol();)e.peek()===t.stringType?(e.next(),t.stack.shift()):"\\"===e.peek()?(e.next(),e.next()):e.match(/^.[^\\\"\']*/);return t.lhs?"property":"string";case n.comment:for(;t.stack[0]===n.comment&&!e.eol();)t.commentType===r.slash&&e.match("*/")||t.commentType===r.parenthesis&&e.match("*)")?(t.stack.shift(),t.commentType=null):e.match(/^.[^\*]*/);return"comment";case n.characterClass:for(;t.stack[0]===n.characterClass&&!e.eol();)e.match(/^[^\]\\]+/)||e.match(".")||t.stack.shift();return"operator"}var a=e.peek();switch(a){case"[":return e.next(),t.stack.unshift(n.characterClass),"bracket";case":":case"|":case";":return e.next(),"operator";case"%":if(e.match("%%"))return"header";if(e.match(/[%][A-Za-z]+/))return"keyword";if(e.match(/[%][}]/))return"bracket";break;case"/":if(e.match(/[\/][A-Za-z]+/))return"keyword";case"\\":if(e.match(/[\][a-z]+/))return"string.special";case".":if(e.match("."))return"atom";case"*":case"-":case"+":case"^":if(e.match(a))return"atom";case"$":if(e.match("$$"))return"builtin";if(e.match(/[$][0-9]+/))return"variableName.special";case"<":if(e.match(/<<[a-zA-Z_]+>>/))return"builtin"}if(e.match("//"))return e.skipToEnd(),"comment";if(e.match("return"))return"operator";if(e.match(/^[a-zA-Z_][a-zA-Z0-9_]*/))return e.match(/(?=[\(.])/)?"variable":e.match(/(?=[\s\n]*[:=])/)?"def":"variableName.special";if(-1!=["[","]","(",")"].indexOf(e.peek()))return e.next(),"bracket";e.eatSpace()||e.next();return null}}}}}]);