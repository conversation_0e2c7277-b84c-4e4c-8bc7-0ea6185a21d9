try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="3b1511bb-0300-4da9-ae2b-38b39e75fcdc",e._sentryDebugIdIdentifier="sentry-dbid-3b1511bb-0300-4da9-ae2b-38b39e75fcdc")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[165],{70165:(e,t,r)=>{function n(e){return RegExp("^(("+e.join(")|(")+"))\\b")}r.r(t),r.d(t,{webIDL:()=>E});var a=["Clamp","Constructor","EnforceRange","Exposed","ImplicitThis","Global","PrimaryGlobal","LegacyArrayClass","LegacyUnenumerableNamedProperties","LenientThis","NamedConstructor","NewObject","NoInterfaceObject","OverrideBuiltins","PutForwards","Replaceable","SameObject","TreatNonObjectAsNull","TreatNullAs","EmptyString","Unforgeable","Unscopeable"],i=n(a),o=["unsigned","short","long","unrestricted","float","double","boolean","byte","octet","Promise","ArrayBuffer","DataView","Int8Array","Int16Array","Int32Array","Uint8Array","Uint16Array","Uint32Array","Uint8ClampedArray","Float32Array","Float64Array","ByteString","DOMString","USVString","sequence","object","RegExp","Error","DOMException","FrozenArray","any","void"],c=n(o),l=["attribute","callback","const","deleter","dictionary","enum","getter","implements","inherit","interface","iterable","legacycaller","maplike","partial","required","serializer","setlike","setter","static","stringifier","typedef","optional","readonly","or"],s=n(l),u=["true","false","Infinity","NaN","null"],f=n(u),m=n(["callback","dictionary","enum","interface"]),d=n(["typedef"]),b=/^[:<=>?]/,y=/^-?([1-9][0-9]*|0[Xx][0-9A-Fa-f]+|0[0-7]*)/,p=/^-?(([0-9]+\.[0-9]*|[0-9]*\.[0-9]+)([Ee][+-]?[0-9]+)?|[0-9]+[Ee][+-]?[0-9]+)/,h=/^_?[A-Za-z][0-9A-Z_a-z-]*/,g=/^_?[A-Za-z][0-9A-Z_a-z-]*(?=\s*;)/,A=/^"[^"]*"/,D=/^\/\*.*?\*\//,k=/^\/\*.*/,w=/^.*?\*\//;let E={name:"webidl",startState:function(){return{inComment:!1,lastToken:"",startDef:!1,endDef:!1}},token:function(e,t){var r=function(e,t){if(e.eatSpace())return null;if(t.inComment)return e.match(w)?t.inComment=!1:e.skipToEnd(),"comment";if(e.match("//"))return e.skipToEnd(),"comment";if(e.match(D))return"comment";if(e.match(k))return t.inComment=!0,"comment";if(e.match(/^-?[0-9\.]/,!1)&&(e.match(y)||e.match(p)))return"number";if(e.match(A))return"string";if(t.startDef&&e.match(h))return"def";if(t.endDef&&e.match(g))return t.endDef=!1,"def";if(e.match(s))return"keyword";if(e.match(c)){var r=t.lastToken,n=(e.match(/^\s*(.+?)\b/,!1)||[])[1];return":"===r||"implements"===r||"implements"===n||"="===n?"builtin":"type"}return e.match(i)?"builtin":e.match(f)?"atom":e.match(h)?"variable":e.match(b)?"operator":(e.next(),null)}(e,t);if(r){var n=e.current();t.lastToken=n,"keyword"===r?(t.startDef=m.test(n),t.endDef=t.endDef||d.test(n)):t.startDef=!1}return r},languageData:{autocomplete:a.concat(o).concat(l).concat(u)}}}}]);