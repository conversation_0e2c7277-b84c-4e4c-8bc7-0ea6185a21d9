try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="cf3a3fa1-c9e0-4e46-9da3-60214fbe77c7",e._sentryDebugIdIdentifier="sentry-dbid-cf3a3fa1-c9e0-4e46-9da3-60214fbe77c7")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6093],{66093:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e8,Bk:()=>e$});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,s=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function g(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let y=new Set(["top","bottom"]);function w(e){return y.has(p(e))?"y":"x"}function x(e){return e.replace(/start|end/g,e=>c[e])}let v=["left","right"],b=["right","left"],A=["top","bottom"],R=["bottom","top"];function S(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function T(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function L(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function E(e,t,n){let r,{reference:i,floating:o}=e,l=w(t),a=g(w(t)),f=m(a),s=p(t),u="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[f]/2-o[f]/2;switch(s){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=y*(n&&u?-1:1);break;case"end":r[a]+=y*(n&&u?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:u,y:c}=E(s,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:g}=a[n],{x:m,y:y,data:w,reset:x}=await g({x:u,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});u=null!=m?m:u,c=null!=y?y:c,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(s=!0===x.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:u,y:c}=E(s,d,f)),n=-1)}return{x:u,y:c,placement:d,strategy:i,middlewareData:p}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:s="clippingAncestors",rootBoundary:u="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),g=T(h),m=a[p?"floating"===c?"reference":"floating":c],y=L(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(m)))||n?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:s,rootBoundary:u,strategy:f})),w="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=L(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-b.top+g.top)/v.y,bottom:(b.bottom-y.bottom+g.bottom)/v.y,left:(y.left-b.left+g.left)/v.x,right:(b.right-y.right+g.right)/v.x}}function P(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return i.some(t=>e[t]>=0)}let D=new Set(["left","top"]);async function H(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===w(n),s=D.has(l)?-1:1,u=o&&f?-1:1,c=d(t,e),{mainAxis:g,crossAxis:m,alignmentAxis:y}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof y&&(m="end"===a?-1*y:y),f?{x:m*u,y:g*s}:{x:g*s,y:m*u}}function F(){return"undefined"!=typeof window}function N(e){return W(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function M(e){var t;return null==(t=(W(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function W(e){return!!F()&&(e instanceof Node||e instanceof j(e).Node)}function z(e){return!!F()&&(e instanceof Element||e instanceof j(e).Element)}function B(e){return!!F()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function _(e){return!!F()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}let I=new Set(["inline","contents"]);function V(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!I.has(i)}let X=new Set(["table","td","th"]),Y=[":popover-open",":modal"];function G(e){return Y.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],U=["paint","layout","strict","content"];function J(e){let t=K(),n=z(e)?ee(e):e;return $.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||q.some(e=>(n.willChange||"").includes(e))||U.some(e=>(n.contain||"").includes(e))}function K(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function Z(e){return Q.has(N(e))}function ee(e){return j(e).getComputedStyle(e)}function et(e){return z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||_(e)&&e.host||M(e);return _(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=en(t);return Z(n)?t.ownerDocument?t.ownerDocument.body:t.body:B(n)&&V(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=j(i);if(o){let e=ei(l);return t.concat(l,l.visualViewport||[],V(i)?i:[],e&&n?er(e):[])}return t.concat(i,er(i,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=B(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function el(e){return z(e)?e:e.contextElement}function ea(e){let t=el(e);if(!B(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=eo(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let ef=s(0);function es(e){let t=j(e);return K()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ef}function eu(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=el(e),a=s(1);t&&(r?z(r)&&(a=ea(r)):a=ea(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===j(l))&&i)?es(l):s(0),u=(o.left+f.x)/a.x,c=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=j(l),t=r&&z(r)?j(r):r,n=e,i=ei(n);for(;i&&r&&t!==n;){let e=ea(i),t=i.getBoundingClientRect(),r=ee(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,p*=e.y,u+=o,c+=l,i=ei(n=j(i))}}return L({width:d,height:p,x:u,y:c})}function ec(e,t){let n=et(e).scrollLeft;return t?t.left+n:eu(M(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=j(e),r=M(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=K();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=M(e),n=et(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),f=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(M(e));else if(z(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=B(e)?ea(e):s(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=es(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return L(r)}function eg(e){return"static"===ee(e).position}function em(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return M(e)===n&&(n=n.ownerDocument.body),n}function ey(e,t){var n;let r=j(e);if(G(e))return r;if(!B(e)){let t=en(e);for(;t&&!Z(t);){if(z(t)&&!eg(t))return t;t=en(t)}return r}let i=em(e,t);for(;i&&(n=i,X.has(N(n)))&&eg(i);)i=em(i,t);return i&&Z(i)&&eg(i)&&!J(i)?r:i||function(e){let t=en(e);for(;B(t)&&!Z(t);){if(J(t))return t;if(G(t))break;t=en(t)}return null}(e)||r}let ew=async function(e){let t=this.getOffsetParent||ey,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=B(t),i=M(t),o="fixed"===n,l=eu(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=s(0);if(r||!r&&!o)if(("body"!==N(t)||V(i))&&(a=et(t)),r){let e=eu(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=ec(i));o&&!r&&i&&(f.x=ec(i));let u=!i||r||o?s(0):ed(i,a);return{x:l.left+a.scrollLeft-f.x-u.x,y:l.top+a.scrollTop-f.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ex={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=M(r),a=!!t&&G(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},u=s(1),c=s(0),d=B(r);if((d||!d&&!o)&&(("body"!==N(r)||V(l))&&(f=et(r)),B(r))){let e=eu(r);u=ea(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||o?s(0):ed(l,f,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-f.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-f.scrollTop*u.y+c.y+p.y}},getDocumentElement:M,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?G(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>z(e)&&"body"!==N(e)),i=null,o="fixed"===ee(e).position,l=o?en(e):e;for(;z(l)&&!Z(l);){let t=ee(l),n=J(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&ep.has(i.position)||V(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!z(r)||Z(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],s=a.reduce((e,n)=>{let r=eh(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},eh(t,f,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ey,getElementRects:ew,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eo(e);return{width:t,height:n}},getScale:ea,isElement:z,isRTL:function(e){return"rtl"===ee(e).direction}};function ev(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:s,middlewareData:u}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let y=T(p),x={x:n,y:r},v=g(w(i)),b=m(v),A=await f.getDimensions(c),R="y"===v,S=R?"clientHeight":"clientWidth",L=a.reference[b]+a.reference[v]-x[v]-a.floating[b],E=x[v]-a.reference[v],C=await (null==f.getOffsetParent?void 0:f.getOffsetParent(c)),O=C?C[S]:0;O&&await (null==f.isElement?void 0:f.isElement(C))||(O=s.floating[S]||a.floating[b]);let P=O/2-A[b]/2-1,k=o(y[R?"top":"left"],P),D=o(y[R?"bottom":"right"],P),H=O-A[b]-D,F=O/2-A[b]/2+(L/2-E/2),N=l(k,o(F,H)),j=!u.arrow&&null!=h(i)&&F!==N&&a.reference[b]/2-(F<k?k:D)-A[b]/2<0,M=j?F<k?F-k:F-H:0;return{[v]:x[v]+M,data:{[v]:N,centerOffset:F-N-M,...j&&{alignmentOffset:M}},reset:j}}}),eA=(e,t,n)=>{let r=new Map,i={platform:ex,...n},o={...i.platform,_c:r};return C(e,t,{...i,platform:o})};var eR=n(47650),eS="undefined"!=typeof document?r.useLayoutEffect:function(){};function eT(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eT(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eT(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eL(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eE(e,t){let n=eL(e);return Math.round(t*n)/n}function eC(e){let t=r.useRef(e);return eS(()=>{t.current=e}),t}let eO=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),eP=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await H(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=d(e,t),c={x:n,y:r},h=await O(t,u),m=w(p(i)),y=g(m),x=c[y],v=c[m];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}if(f){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}let b=s.fn({...t,[y]:x,[m]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[m]:f}}}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:s=!0}=d(e,t),u={x:n,y:r},c=w(i),h=g(c),m=u[h],y=u[c],x=d(a,t),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+v.mainAxis,n=o.reference[h]+o.reference[e]-v.mainAxis;m<t?m=t:m>n&&(m=n)}if(s){var b,A;let e="y"===h?"width":"height",t=D.has(p(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:v.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(A=l.offset)?void 0:A[c])||0)-(t?v.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[c]:y}}}}(e),options:[e,t]}),eH=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:s,initialPlacement:u,platform:c,elements:y}=t,{mainAxis:T=!0,crossAxis:L=!0,fallbackPlacements:E,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:k=!0,...D}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let H=p(a),F=w(u),N=p(u)===u,j=await (null==c.isRTL?void 0:c.isRTL(y.floating)),M=E||(N||!k?[S(u)]:function(e){let t=S(e);return[x(e),t,x(t)]}(u)),W="none"!==P;!E&&W&&M.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:v;return t?v:b;case"left":case"right":return t?A:R;default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(x)))),o}(u,k,P,j));let z=[u,...M],B=await O(t,D),_=[],I=(null==(r=f.flip)?void 0:r.overflows)||[];if(T&&_.push(B[H]),L){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=g(w(e)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=S(l)),[l,S(l)]}(a,s,j);_.push(B[e[0]],B[e[1]])}if(I=[...I,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=z[e];if(t&&("alignment"!==L||F===w(t)||I.every(e=>w(e.placement)!==F||e.overflows[0]>0)))return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(o=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(C){case"bestFit":{let e=null==(l=I.filter(e=>{if(W){let t=w(e.placement);return t===F||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=u}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eF=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:f,rects:s,platform:u,elements:c}=t,{apply:g=()=>{},...m}=d(e,t),y=await O(t,m),x=p(f),v=h(f),b="y"===w(f),{width:A,height:R}=s.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let S=R-y.top-y.bottom,T=A-y.left-y.right,L=o(R-y[i],S),E=o(A-y[a],T),C=!t.middlewareData.shift,P=L,k=E;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=S),C&&!v){let e=l(y.left,0),t=l(y.right,0),n=l(y.top,0),r=l(y.bottom,0);b?k=A-2*(0!==e||0!==t?e+t:l(y.left,y.right)):P=R-2*(0!==n||0!==r?n+r:l(y.top,y.bottom))}await g({...t,availableWidth:k,availableHeight:P});let D=await u.getDimensions(c.floating);return A!==D.width||R!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=P(await O(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=P(await O(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),ej=(e,t)=>({...eO(e),options:[e,t]});var eM=n(97602),eW=n(95155),ez=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eW.jsx)(eM.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eW.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ez.displayName="Arrow";var eB=n(94446),e_=n(3468),eI=n(70222),eV=n(4129),eX=n(84288),eY="Popper",[eG,e$]=(0,e_.A)(eY),[eq,eU]=eG(eY),eJ=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eW.jsx)(eq,{scope:t,anchor:i,onAnchorChange:o,children:n})};eJ.displayName=eY;var eK="PopperAnchor",eQ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eU(eK,n),a=r.useRef(null),f=(0,eB.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eW.jsx)(eM.sG.div,{...o,ref:f})});eQ.displayName=eK;var eZ="PopperContent",[e0,e1]=eG(eZ),e2=r.forwardRef((e,t)=>{var n,i,a,s,u,c,d,p;let{__scopePopper:h,side:g="bottom",sideOffset:m=0,align:y="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:A=0,sticky:R="partial",hideWhenDetached:S=!1,updatePositionStrategy:T="optimized",onPlaced:L,...E}=e,C=eU(eZ,h),[O,P]=r.useState(null),k=(0,eB.s)(t,e=>P(e)),[D,H]=r.useState(null),F=(0,eX.X)(D),N=null!=(d=null==F?void 0:F.width)?d:0,j=null!=(p=null==F?void 0:F.height)?p:0,W="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},z=Array.isArray(b)?b:[b],B=z.length>0,_={padding:W,boundary:z.filter(e9),altBoundary:B},{refs:I,floatingStyles:V,placement:X,isPositioned:Y,middlewareData:G}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:s,open:u}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);eT(p,i)||h(i);let[g,m]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==R.current&&(R.current=e,m(e))},[]),v=r.useCallback(e=>{e!==S.current&&(S.current=e,w(e))},[]),b=l||g,A=a||y,R=r.useRef(null),S=r.useRef(null),T=r.useRef(c),L=null!=s,E=eC(s),C=eC(o),O=eC(u),P=r.useCallback(()=>{if(!R.current||!S.current)return;let e={placement:t,strategy:n,middleware:p};C.current&&(e.platform=C.current),eA(R.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};k.current&&!eT(T.current,t)&&(T.current=t,eR.flushSync(()=>{d(t)}))})},[p,t,n,C,O]);eS(()=>{!1===u&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[u]);let k=r.useRef(!1);eS(()=>(k.current=!0,()=>{k.current=!1}),[]),eS(()=>{if(b&&(R.current=b),A&&(S.current=A),b&&A){if(E.current)return E.current(b,A,P);P()}},[b,A,P,E,L]);let D=r.useMemo(()=>({reference:R,floating:S,setReference:x,setFloating:v}),[x,v]),H=r.useMemo(()=>({reference:b,floating:A}),[b,A]),F=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!H.floating)return e;let t=eE(H.floating,c.x),r=eE(H.floating,c.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...eL(H.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,H.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:P,refs:D,elements:H,floatingStyles:F}),[c,P,D,H,F])}({strategy:"fixed",placement:g+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:u="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=el(e),h=a||s?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let g=p&&c?function(e,t){let n,r=null,i=M(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:g,height:m}=d;if(u||t(),!g||!m)return;let y=f(h),w=f(i.clientWidth-(p+g)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+m))+"px "+-f(p)+"px",threshold:l(0,o(1,c))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!v)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ev(d,e.getBoundingClientRect())||s(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(p,n):null,m=-1,y=null;u&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?eu(e):null;return d&&function t(){let r=eu(e);w&&!ev(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==g||g(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===T})},elements:{reference:C.anchor},middleware:[eP({mainAxis:m+j,alignmentAxis:w}),v&&ek({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eD():void 0,..._}),v&&eH({..._}),eF({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&ej({element:D,padding:x}),e6({arrowWidth:N,arrowHeight:j}),S&&eN({strategy:"referenceHidden",..._})]}),[$,q]=e7(X),U=(0,eI.c)(L);(0,eV.N)(()=>{Y&&(null==U||U())},[Y,U]);let J=null==(n=G.arrow)?void 0:n.x,K=null==(i=G.arrow)?void 0:i.y,Q=(null==(a=G.arrow)?void 0:a.centerOffset)!==0,[Z,ee]=r.useState();return(0,eV.N)(()=>{O&&ee(window.getComputedStyle(O).zIndex)},[O]),(0,eW.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:Y?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Z,"--radix-popper-transform-origin":[null==(s=G.transformOrigin)?void 0:s.x,null==(u=G.transformOrigin)?void 0:u.y].join(" "),...(null==(c=G.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eW.jsx)(e0,{scope:h,placedSide:$,onArrowChange:H,arrowX:J,arrowY:K,shouldHideArrow:Q,children:(0,eW.jsx)(eM.sG.div,{"data-side":$,"data-align":q,...E,ref:k,style:{...E.style,animation:Y?void 0:"none"}})})})});e2.displayName=eZ;var e4="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e3=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=e1(e4,n),o=e5[i.placedSide];return(0,eW.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eW.jsx)(ez,{...r,ref:t,style:{...r.style,display:"block"}})})});function e9(e){return null!==e}e3.displayName=e4;var e6=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:s}=t,u=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,c=u?0:e.arrowWidth,d=u?0:e.arrowHeight,[p,h]=e7(a),g={start:"0%",center:"50%",end:"100%"}[h],m=(null!=(o=null==(r=s.arrow)?void 0:r.x)?o:0)+c/2,y=(null!=(l=null==(i=s.arrow)?void 0:i.y)?l:0)+d/2,w="",x="";return"bottom"===p?(w=u?g:"".concat(m,"px"),x="".concat(-d,"px")):"top"===p?(w=u?g:"".concat(m,"px"),x="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),x=u?g:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),x=u?g:"".concat(y,"px")),{data:{x:w,y:x}}}});function e7(e){let[t,n="center"]=e.split("-");return[t,n]}var e8=eJ,te=eQ,tt=e2,tn=e3},84288:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(12115),i=n(4129);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}}]);