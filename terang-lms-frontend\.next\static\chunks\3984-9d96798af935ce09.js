try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="823af188-3f06-4630-aefe-f220dfd5ac15",e._sentryDebugIdIdentifier="sentry-dbid-823af188-3f06-4630-aefe-f220dfd5ac15")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3984],{15868:(e,t,r)=>{r.d(t,{A:()=>m});var a=r(95155),s=r(66094),n=r(20764),i=r(6132),o=r(19408),d=r(23664),l=r(47886);r(12115);var c=r(62879);function u(e){let{children:t,scrollable:r=!0}=e;return(0,a.jsx)(a.Fragment,{children:r?(0,a.jsx)(c.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,a.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:t})}):(0,a.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:t})})}function m(e){let{userRole:t}=e,r=l.qs.getUser(),{title:c,description:m,actionText:x}="teacher"===t?{title:"Akun Teacher Belum Terdaftar di Institusi",description:"Akun Anda sebagai Teacher belum ditugaskan ke institusi manapun. Silakan hubungi Super Admin untuk mendapatkan akses ke institusi.",actionText:"Hubungi Super Admin"}:{title:"Akun Student Belum Terdaftar di Institusi",description:"Akun Anda sebagai Student belum ditugaskan ke institusi manapun. Silakan hubungi Teacher atau Super Admin untuk mendapatkan akses ke institusi.",actionText:"Hubungi Admin"};return(0,a.jsx)(u,{"data-sentry-element":"PageContainer","data-sentry-component":"NotAssignedToInstitution","data-sentry-source-file":"not-assigned-to-institution.tsx",children:(0,a.jsx)("div",{className:"flex w-full min-h-[calc(100vh-200px)] items-center justify-center",children:(0,a.jsxs)(s.Zp,{className:"w-full max-w-md mx-auto","data-sentry-element":"Card","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,a.jsxs)(s.aR,{className:"text-center","data-sentry-element":"CardHeader","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,a.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100",children:(0,a.jsx)(i.A,{className:"h-8 w-8 text-orange-600","data-sentry-element":"AlertCircle","data-sentry-source-file":"not-assigned-to-institution.tsx"})}),(0,a.jsx)(s.ZB,{className:"text-xl font-semibold","data-sentry-element":"CardTitle","data-sentry-source-file":"not-assigned-to-institution.tsx",children:c}),(0,a.jsx)(s.BT,{className:"text-sm text-muted-foreground","data-sentry-element":"CardDescription","data-sentry-source-file":"not-assigned-to-institution.tsx",children:m})]}),(0,a.jsxs)(s.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,a.jsx)("div",{className:"rounded-lg border bg-muted/50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-muted-foreground","data-sentry-element":"Building2","data-sentry-source-file":"not-assigned-to-institution.tsx"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Status Institusi"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Belum ditugaskan"})]})]})}),(0,a.jsx)("div",{className:"rounded-lg border bg-muted/50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-muted-foreground","data-sentry-element":"Mail","data-sentry-source-file":"not-assigned-to-institution.tsx"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Email Akun"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:null==r?void 0:r.email})]})]})}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)("p",{className:"text-xs text-muted-foreground text-center",children:"Setelah Super Admin menugaskan Anda ke institusi, silakan logout dan login kembali untuk mengakses dashboard."})}),(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsxs)(n.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,a.jsx)(d.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Mail","data-sentry-source-file":"not-assigned-to-institution.tsx"}),x]}),(0,a.jsx)(n.$,{variant:"ghost",onClick:()=>{l.qs.removeUser(),window.location.href="/auth/sign-in"},className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"not-assigned-to-institution.tsx",children:"Logout"})]})]})]})})})}},20764:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>o});var a=r(95155);r(12115);var s=r(32467),n=r(83101),i=r(64269);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",iai:"bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20","iai-outline":"border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:t})),...l,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},47886:(e,t,r)=>{r.d(t,{WG:()=>s,cl:()=>i,qs:()=>a});let a={setUser:e=>{localStorage.setItem("auth_user",JSON.stringify(e))},getUser:()=>{{let e=localStorage.getItem("auth_user");return e?JSON.parse(e):null}},removeUser:()=>{localStorage.removeItem("auth_user")},isAuthenticated:()=>null!==a.getUser(),hasRole:e=>{let t=a.getUser();return(null==t?void 0:t.role)===e},isSuperAdmin:()=>a.hasRole("super_admin"),isTeacher:()=>a.hasRole("teacher"),isStudent:()=>a.hasRole("student")},s=e=>{switch(e.role){case"super_admin":return"/dashboard/admin";case"teacher":return"/dashboard/teacher";case"student":return"/courses";default:return"/dashboard"}},n=()=>{let e=a.getUser();return e||(window.location.href="/auth/sign-in",null)},i=e=>{let t=n();return t?t.role!==e?(window.location.href=s(t),null):t:null}},62879:(e,t,r)=>{r.d(t,{$:()=>o,ScrollArea:()=>i});var a=r(95155);r(12115);var s=r(59034),n=r(64269);function i(e){let{className:t,children:r,...i}=e;return(0,a.jsxs)(s.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",t),...i,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,a.jsx)(s.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:r}),(0,a.jsx)(o,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,a.jsx)(s.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function o(e){let{className:t,orientation:r="vertical",...i}=e;return(0,a.jsx)(s.VM,{"data-slot":"scroll-area-scrollbar",orientation:r,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",t),...i,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,a.jsx)(s.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},64269:(e,t,r)=>{r.d(t,{cn:()=>n,z:()=>i});var a=r(2821),s=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function i(e){var t,r;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:s=0,sizeType:n="normal"}=a;if(0===e)return"0 Byte";let i=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,i)).toFixed(s)," ").concat("accurate"===n?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][i])?t:"Bytest":null!=(r=["Bytes","KB","MB","GB","TB"][i])?r:"Bytes")}},66094:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var a=r(95155);r(12115);var s=r(64269);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},88021:(e,t,r)=>{r.d(t,{E:()=>d});var a=r(95155);r(12115);var s=r(32467),n=r(83101),i=r(64269);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,asChild:n=!1,...d}=e,l=n?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:r}),t),...d,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}}]);