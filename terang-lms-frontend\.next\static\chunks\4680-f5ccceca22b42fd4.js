try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="939eae57-5a58-4773-88f4-6524271e544d",e._sentryDebugIdIdentifier="sentry-dbid-939eae57-5a58-4773-88f4-6524271e544d")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{5917:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71847).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},17787:(e,t,n)=>{let r;n.d(t,{Mp:()=>eL,Hd:()=>e$,vL:()=>o,cA:()=>eu,IG:()=>ec,Sj:()=>T,fF:()=>e_,PM:()=>eF,zM:()=>eU,MS:()=>R,FR:()=>S});var l,i,a,o,u,s,c,d,f,h,g=n(12115),p=n(47650),v=n(27587);let m={display:"none"};function y(e){let{id:t,value:n}=e;return g.createElement("div",{id:t,style:m},n)}function b(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return g.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,g.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},D={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function E(e){let{announcements:t=D,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,g.useState)(!1);(0,g.useEffect)(()=>{s(!0)},[]);var c=(0,g.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,g.useContext)(w);if((0,g.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let f=g.createElement(g.Fragment,null,g.createElement(y,{id:r,value:l.draggable}),g.createElement(b,{id:o,announcement:a}));return n?(0,p.createPortal)(f,n):f}function C(){}function R(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function S(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let k=Object.freeze({x:0,y:0});function M(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}let O=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(M)};function I(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:k}let N=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1);function j(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let A={ignoreTransform:!1};function T(e,t){void 0===t&&(t=A);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=j(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function L(e){return T(e,{ignoreTransform:!0})}function P(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function z(e){let[t]=P(e,1);return null!=t?t:null}function F(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function _(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function W(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function U(e){return{x:_(e),y:W(e)}}function Y(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function B(e){let t={x:0,y:0},n=Y(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let K={x:.2,y:.2};function G(e){return e.reduce((e,t)=>(0,v.WQ)(e,U(t)),k)}function V(e,t){if(void 0===t&&(t=T),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);z(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let X=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+_(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+W(t),0)}]];class H{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=P(t),r=G(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,X))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Z{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function $(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function q(e){e.preventDefault()}function J(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let Q={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},ee=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class et{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new Z((0,v.TW)(t)),this.windowListeners=new Z((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&V(n),t(k)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=Q,coordinateGetter:i=ee,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:k;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:f,minScroll:h}=B(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,m=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=f.x||l===o.Left&&e>=h.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-f.x:n.scrollLeft-h.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(m&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=f.y||l===o.Up&&e>=h.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-f.y:n.scrollTop-h.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function en(e){return!!(e&&"distance"in e)}function er(e){return!!(e&&"delay"in e)}et.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Q,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class el{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new Z(this.document),this.listeners=new Z(n),this.windowListeners=new Z((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:k,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,q),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,q),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(er(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(en(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,J,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:k,u=(0,v.Re)(r,o);if(!n&&a){if(en(a)){if(null!=a.tolerance&&$(u,a.tolerance))return this.handleCancel();if($(u,a.distance))return this.handleStart()}return er(a)&&$(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ei={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ea extends el{constructor(e){let{event:t}=e;super(e,ei,(0,v.TW)(t.target))}}ea.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eo={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class eu extends el{constructor(e){super(e,eo,(0,v.TW)(e.event.target))}}eu.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let es={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class ec extends el{constructor(e){super(e,es)}static setup(){return window.addEventListener(es.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(es.move.name,e)};function e(){}}}ec.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ed={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(f||(f={})).Optimized="optimized";let ef=new Map;function eh(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function eg(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ep(e){return new H(T(e),e)}function ev(e,t,n){void 0===t&&(t=ep);let[r,l]=(0,g.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=eg({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let em=[];function ey(e,t){void 0===t&&(t=[]);let n=(0,g.useRef)(null);return(0,g.useEffect)(()=>{n.current=null},t),(0,g.useEffect)(()=>{let t=e!==k;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):k}function eb(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let ew=[];function ex(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}let eD=[{sensor:ea,options:{}},{sensor:et,options:{}}],eE={current:{}},eC={draggable:{measure:L},droppable:{measure:L,strategy:d.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:T}};class eR extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eS={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eR,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:C},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eC,measureDroppableContainers:C,windowRect:null,measuringScheduled:!1},ek={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:C,draggableNodes:new Map,over:null,measureDroppableContainers:C},eM=(0,g.createContext)(ek),eO=(0,g.createContext)(eS);function eI(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eR}}}function eN(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eR(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eR(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eR(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function ej(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,g.useContext)(eM),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,g.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}function eA(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}let eT=(0,g.createContext)({...k,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(h||(h={}));let eL=(0,g.memo)(function(e){var t,n,r,a,o,u;let{id:f,accessibility:m,autoScroll:y=!0,children:b,sensors:x=eD,collisionDetection:D=O,measuring:C,modifiers:R,...S}=e,[M,j]=(0,g.useReducer)(eN,void 0,eI),[A,L]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[_,W]=(0,g.useState)(h.Uninitialized),V=_===h.Initialized,{draggable:{active:X,nodes:Z,translate:$},droppable:{containers:q}}=M,J=null!=X?Z.get(X):null,Q=(0,g.useRef)({initial:null,translated:null}),ee=(0,g.useMemo)(()=>{var e;return null!=X?{id:X,data:null!=(e=null==J?void 0:J.data)?e:eE,rect:Q}:null},[X,J]),et=(0,g.useRef)(null),[en,er]=(0,g.useState)(null),[el,ei]=(0,g.useState)(null),ea=(0,v.YN)(S,Object.values(S)),eo=(0,v.YG)("DndDescribedBy",f),eu=(0,g.useMemo)(()=>q.getEnabled(),[q]),es=(0,g.useMemo)(()=>({draggable:{...eC.draggable,...null==C?void 0:C.draggable},droppable:{...eC.droppable,...null==C?void 0:C.droppable},dragOverlay:{...eC.dragOverlay,...null==C?void 0:C.dragOverlay}}),[null==C?void 0:C.draggable,null==C?void 0:C.droppable,null==C?void 0:C.dragOverlay]),{droppableRects:ec,measureDroppableContainers:ep,measuringScheduled:eR}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,g.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,g.useRef)(e),f=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),h=(0,v.YN)(f),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),h.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[h]),m=(0,g.useRef)(null),y=(0,v.KG)(t=>{if(f&&!n)return ef;if(!t||t===ef||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new H(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,f,u]);return(0,g.useEffect)(()=>{c.current=e},[e]),(0,g.useEffect)(()=>{f||p()},[n,f]),(0,g.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{f||"number"!=typeof o||null!==m.current||(m.current=setTimeout(()=>{p(),m.current=null},o))},[o,f,p,...r]),{droppableRects:y,measureDroppableContainers:p,measuringScheduled:null!=i}}(eu,{dragging:V,dependencies:[$.x,$.y],config:es.droppable}),eS=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(Z,X),ek=(0,g.useMemo)(()=>el?(0,v.e_)(el):null,[el]),eL=function(){let e=(null==en?void 0:en.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,n=V&&!e&&!t;return"object"==typeof y?{...y,enabled:n}:{enabled:n}}(),eP=eh(eS,es.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,g.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=I(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=z(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=X?Z.get(X):null,config:eL.layoutShiftCompensation,initialRect:eP,measure:es.draggable.measure});let ez=ev(eS,es.draggable.measure,eP),eF=ev(eS?eS.parentElement:null),e_=(0,g.useRef)({activatorEvent:null,active:null,activeNode:eS,collisionRect:null,collisions:null,droppableRects:ec,draggableNodes:Z,draggingNode:null,draggingNodeRect:null,droppableContainers:q,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eW=q.getNodeFor(null==(t=e_.current.over)?void 0:t.id),eU=function(e){let{measure:t}=e,[n,r]=(0,g.useState)(null),l=eg({callback:(0,g.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,g.useCallback)(e=>{let n=ex(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,g.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:es.dragOverlay.measure}),eY=null!=(n=eU.nodeRef.current)?n:eS,eB=V?null!=(r=eU.rect)?r:ez:null,eK=!!(eU.nodeRef.current&&eU.rect),eG=function(e){let t=eh(e);return I(e,t)}(eK?null:ez),eV=eb(eY?(0,v.zk)(eY):null),eX=function(e){let t=(0,g.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==em&&e&&t.current&&e.parentNode===t.current.parentNode?n:P(e):em,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),n}(V?null!=eW?eW:eS:null),eH=function(e,t){void 0===t&&(t=T);let[n]=e,r=eb(n?(0,v.zk)(n):null),[l,i]=(0,g.useState)(ew);function a(){i(()=>e.length?e.map(e=>Y(e)?r:new H(t(e),e)):ew)}let o=eg({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eX),eZ=eA(R,{transform:{x:$.x-eG.x,y:$.y-eG.y,scaleX:1,scaleY:1},activatorEvent:el,active:ee,activeNodeRect:ez,containerNodeRect:eF,draggingNodeRect:eB,over:e_.current.over,overlayNodeRect:eU.rect,scrollableAncestors:eX,scrollableAncestorRects:eH,windowRect:eV}),e$=ek?(0,v.WQ)(ek,$):null,eq=function(e){let[t,n]=(0,g.useState)(null),r=(0,g.useRef)(e),l=(0,g.useCallback)(e=>{let t=F(e.target);t&&n(e=>e?(e.set(t,U(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=F(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,U(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=F(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),k):G(e):k,[e,t])}(eX),eJ=ey(eq),eQ=ey(eq,[ez]),e0=(0,v.WQ)(eZ,eJ),e1=eB?N(eB,eZ):null,e2=ee&&e1?D({active:ee,collisionRect:e1,droppableRects:ec,droppableContainers:eu,pointerCoordinates:e$}):null,e5=function(e,t){if(!e||0===e.length)return null;let[n]=e;return n.id}(e2,"id"),[e4,e6]=(0,g.useState)(null),e7=(o=eK?eZ:(0,v.WQ)(eZ,eQ),u=null!=(a=null==e4?void 0:e4.rect)?a:null,{...o,scaleX:u&&ez?u.width/ez.width:1,scaleY:u&&ez?u.height/ez.height:1}),e9=(0,g.useRef)(null),e8=(0,g.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==et.current)return;let i=Z.get(et.current);if(!i)return;let a=e.nativeEvent,o=new n({active:et.current,activeNode:i,event:a,options:r,context:e_,onAbort(e){if(!Z.get(e))return;let{onDragAbort:t}=ea.current,n={id:e};null==t||t(n),A({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!Z.get(e))return;let{onDragPending:l}=ea.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),A({type:"onDragPending",event:i})},onStart(e){let t=et.current;if(null==t)return;let n=Z.get(t);if(!n)return;let{onDragStart:r}=ea.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:Q}};(0,p.unstable_batchedUpdates)(()=>{null==r||r(i),W(h.Initializing),j({type:l.DragStart,initialCoordinates:e,active:t}),A({type:"onDragStart",event:i}),er(e9.current),ei(a)})},onMove(e){j({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=e_.current,o=null;if(t&&i){let{cancelDrop:u}=ea.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}et.current=null,(0,p.unstable_batchedUpdates)(()=>{j({type:e}),W(h.Uninitialized),e6(null),er(null),ei(null),e9.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=ea.current[t];null==e||e(o),A({type:t,event:o})}})}}e9.current=o},[Z]),e3=(0,g.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=Z.get(r);null!==et.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},et.current=r,e8(n,t))},[Z,e8]),te=(0,g.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e3(e.handler,t)}))]},[]),[x,e3]);(0,g.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{ez&&_===h.Initializing&&W(h.Initialized)},[ez,_]),(0,g.useEffect)(()=>{let{onDragMove:e}=ea.current,{active:t,activatorEvent:n,collisions:r,over:l}=e_.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e0.x,y:e0.y},over:l};(0,p.unstable_batchedUpdates)(()=>{null==e||e(i),A({type:"onDragMove",event:i})})},[e0.x,e0.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=e_.current;if(!e||null==et.current||!t||!l)return;let{onDragOver:i}=ea.current,a=r.get(e5),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,p.unstable_batchedUpdates)(()=>{e6(o),null==i||i(u),A({type:"onDragOver",event:u})})},[e5]),(0,v.Es)(()=>{e_.current={activatorEvent:el,active:ee,activeNode:eS,collisionRect:e1,collisions:e2,droppableRects:ec,draggableNodes:Z,draggingNode:eY,draggingNodeRect:eB,droppableContainers:q,over:e4,scrollableAncestors:eX,scrollAdjustedTranslate:e0},Q.current={initial:eB,translated:e1}},[ee,eS,e2,e1,Z,eY,eB,ec,q,e4,eX,e0]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:h,delta:p,threshold:m}=e,y=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return ed;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:p,disabled:!a}),[b,w]=(0,v.$$)(),x=(0,g.useRef)({x:0,y:0}),D=(0,g.useRef)({x:0,y:0}),E=(0,g.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),C=(0,g.useRef)(null),R=(0,g.useCallback)(()=>{let e=C.current;if(!e)return;let t=x.current.x*D.current.x,n=x.current.y*D.current.y;e.scrollBy(t,n)},[]),S=(0,g.useMemo)(()=>u===c.TreeOrder?[...f].reverse():f,[u,f]);(0,g.useEffect)(()=>{if(!a||!f.length||!E)return void w();for(let e of S){if((null==r?void 0:r(e))===!1)continue;let n=h[f.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=K);let{isTop:c,isBottom:d,isLeft:f,isRight:h}=B(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!h&&u>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!f&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,E,t,m);for(let e of["x","y"])y[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),C.current=e,b(R,o),x.current=a,D.current=l;return}}x.current={x:0,y:0},D.current={x:0,y:0},w()},[t,R,r,w,a,o,JSON.stringify(E),JSON.stringify(y),b,f,S,h,JSON.stringify(m)])}({...eL,delta:$,draggingRect:e1,pointerCoordinates:e$,scrollableAncestors:eX,scrollableAncestorRects:eH});let tt=(0,g.useMemo)(()=>({active:ee,activeNode:eS,activeNodeRect:ez,activatorEvent:el,collisions:e2,containerNodeRect:eF,dragOverlay:eU,draggableNodes:Z,droppableContainers:q,droppableRects:ec,over:e4,measureDroppableContainers:ep,scrollableAncestors:eX,scrollableAncestorRects:eH,measuringConfiguration:es,measuringScheduled:eR,windowRect:eV}),[ee,eS,ez,el,e2,eF,eU,Z,q,ec,e4,ep,eX,eH,es,eR,eV]),tn=(0,g.useMemo)(()=>({activatorEvent:el,activators:te,active:ee,activeNodeRect:ez,ariaDescribedById:{draggable:eo},dispatch:j,draggableNodes:Z,over:e4,measureDroppableContainers:ep}),[el,te,ee,ez,j,eo,Z,e4,ep]);return g.createElement(w.Provider,{value:L},g.createElement(eM.Provider,{value:tn},g.createElement(eO.Provider,{value:tt},g.createElement(eT.Provider,{value:e7},b)),g.createElement(ej,{disabled:(null==m?void 0:m.restoreFocus)===!1})),g.createElement(E,{...m,hiddenTextDescribedById:eo}))}),eP=(0,g.createContext)(null),ez="button";function eF(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,g.useContext)(eM),{role:h=ez,roleDescription:p="draggable",tabIndex:m=0}=null!=l?l:{},y=(null==u?void 0:u.id)===t,b=(0,g.useContext)(y?eT:eP),[w,x]=(0,v.lk)(),[D,E]=(0,v.lk)(),C=(0,g.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),R=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:D,data:R}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,g.useMemo)(()=>({role:h,tabIndex:m,"aria-disabled":r,"aria-pressed":!!y&&h===ez||void 0,"aria-roledescription":p,"aria-describedby":c.draggable}),[r,h,m,y,p,c.draggable]),isDragging:y,listeners:r?void 0:C,node:w,over:f,setNodeRef:x,setActivatorNodeRef:E,transform:b}}function e_(){return(0,g.useContext)(eO)}let eW={timeout:25};function eU(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,g.useContext)(eM),d=(0,g.useRef)({disabled:n}),f=(0,g.useRef)(!1),h=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:m,updateMeasurementsFor:y,timeout:b}={...eW,...i},w=(0,v.YN)(null!=y?y:r),x=eg({callback:(0,g.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),p.current=null},b)},[b]),disabled:m||!o}),D=(0,g.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x]),[E,C]=(0,v.lk)(D),R=(0,v.YN)(t);return(0,g.useEffect)(()=>{x&&E.current&&(x.disconnect(),f.current=!1,x.observe(E.current))},[E,x]),(0,g.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:E,rect:h,data:R}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,g.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:h,isOver:(null==s?void 0:s.id)===r,node:E,over:s,setNodeRef:C}}function eY(e){let{animation:t,children:n}=e,[r,l]=(0,g.useState)(null),[i,a]=(0,g.useState)(null),o=(0,v.ZC)(n);return n||r||!o||l(o),(0,v.Es)(()=>{if(!i)return;let e=null==r?void 0:r.key,n=null==r?void 0:r.props.id;if(null==e||null==n)return void l(null);Promise.resolve(t(n,i)).then(()=>{l(null)})},[t,r,i]),g.createElement(g.Fragment,null,n,r?(0,g.cloneElement)(r,{ref:a}):null)}let eB={x:0,y:0,scaleX:1,scaleY:1};function eK(e){let{children:t}=e;return g.createElement(eM.Provider,{value:ek},g.createElement(eT.Provider,{value:eB},t))}let eG={position:"fixed",touchAction:"none"},eV=e=>(0,v.kx)(e)?"transform 250ms ease":void 0,eX=(0,g.forwardRef)((e,t)=>{let{as:n,activatorEvent:r,adjustScale:l,children:i,className:a,rect:o,style:u,transform:s,transition:c=eV}=e;if(!o)return null;let d=l?s:{...s,scaleX:1,scaleY:1},f={...eG,width:o.width,height:o.height,top:o.top,left:o.left,transform:v.Ks.Transform.toString(d),transformOrigin:l&&r?function(e,t){let n=(0,v.e_)(e);if(!n)return"0 0";let r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}(r,o):void 0,transition:"function"==typeof c?c(r):c,...u};return g.createElement(n,{className:a,style:f,ref:t},i)}),eH={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:v.Ks.Transform.toString(t)},{transform:v.Ks.Transform.toString(n)}]},sideEffects:(r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}})},eZ=0,e$=g.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:l,transition:i,modifiers:a,wrapperElement:o="div",className:u,zIndex:s=999}=e,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggableNodes:p,droppableContainers:m,dragOverlay:y,over:b,measuringConfiguration:w,scrollableAncestors:x,scrollableAncestorRects:D,windowRect:E}=e_(),C=(0,g.useContext)(eT),R=function(e){return(0,g.useMemo)(()=>{if(null!=e)return++eZ},[e])}(null==d?void 0:d.id),S=eA(a,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggingNodeRect:y.rect,over:b,overlayNodeRect:y.rect,scrollableAncestors:x,scrollableAncestorRects:D,transform:C,windowRect:E}),k=eh(f),M=function(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:l}=e;return(0,v._q)((e,i)=>{if(null===t)return;let a=n.get(e);if(!a)return;let o=a.node.current;if(!o)return;let u=ex(i);if(!u)return;let{transform:s}=(0,v.zk)(i).getComputedStyle(i),c=j(s);if(!c)return;let d="function"==typeof t?t:function(e){let{duration:t,easing:n,sideEffects:r,keyframes:l}={...eH,...e};return e=>{let{active:i,dragOverlay:a,transform:o,...u}=e;if(!t)return;let s={x:a.rect.left-i.rect.left,y:a.rect.top-i.rect.top},c={scaleX:1!==o.scaleX?i.rect.width*o.scaleX/a.rect.width:1,scaleY:1!==o.scaleY?i.rect.height*o.scaleY/a.rect.height:1},d={x:o.x-s.x,y:o.y-s.y,...c},f=l({...u,active:i,dragOverlay:a,transform:{initial:o,final:d}}),[h]=f,g=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(g))return;let p=null==r?void 0:r({active:i,dragOverlay:a,...u}),v=a.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise(e=>{v.onfinish=()=>{null==p||p(),e()}})}}(t);return V(o,l.draggable.measure),d({active:{id:e,data:a.data,node:o,rect:l.draggable.measure(o)},draggableNodes:n,dragOverlay:{node:i,rect:l.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:l,transform:c})})}({config:r,draggableNodes:p,droppableContainers:m,measuringConfiguration:w}),O=k?y.setRef:void 0;return g.createElement(eK,null,g.createElement(eY,{animation:M},d&&R?g.createElement(eX,{key:R,id:d.id,ref:O,as:o,activatorEvent:c,adjustScale:t,className:u,transition:i,rect:k,style:{zIndex:s,...l},transform:S},n):null))})},25959:(e,t,n)=>{n.d(t,{v:()=>u});var r=n(12115);let l=e=>{let t,n=new Set,r=(e,r)=>{let l="function"==typeof e?e(t):e;if(!Object.is(l,t)){let e=t;t=(null!=r?r:"object"!=typeof l||null===l)?l:Object.assign({},t,l),n.forEach(n=>n(t,e))}},l=()=>t,i={setState:r,getState:l,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,l,i);return i},i=e=>e?l(e):l,a=e=>e,o=e=>{let t=i(e),n=e=>(function(e,t=a){let n=r.useSyncExternalStore(e.subscribe,r.useCallback(()=>t(e.getState()),[e,t]),r.useCallback(()=>t(e.getInitialState()),[e,t]));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},u=e=>e?o(e):o},27587:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>h,KG:()=>m,Ks:()=>k,Ll:()=>o,Re:()=>C,Sw:()=>i,TW:()=>f,WQ:()=>E,YG:()=>x,YN:()=>v,ZC:()=>b,_q:()=>g,ag:()=>O,e_:()=>S,jn:()=>l,kx:()=>R,l6:()=>a,lk:()=>y,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function m(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function y(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function b(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function D(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let E=D(1),C=D(-1);function R(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function S(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let k=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[k.Translate.toString(e),k.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),M="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function O(e){return e.matches(M)?e:e.querySelector(M)}},32467:(e,t,n)=>{n.d(t,{DX:()=>o,Dc:()=>s,TL:()=>a});var r=n(12115),l=n(94446),i=n(95155);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var a;let e,o,u=(a=n,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),s=function(e,t){let n={...t};for(let r in t){let l=e[r],i=t[r];/^on[A-Z]/.test(r)?l&&i?n[r]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...i}:"className"===r&&(n[r]=[l,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,l.t)(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...a}=e,o=r.Children.toArray(l),u=o.find(c);if(u){let e=u.props.children,l=o.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...a,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var o=a("Slot"),u=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=u,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}},34212:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},35646:(e,t,n)=>{n.d(t,{UC:()=>L,VY:()=>_,ZD:()=>z,ZL:()=>A,bL:()=>N,hE:()=>F,hJ:()=>T,l9:()=>j,rc:()=>P});var r=n(12115),l=n(3468),i=n(94446),a=n(89511),o=n(92556),u=n(32467),s=n(95155),c="AlertDialog",[d,f]=(0,l.A)(c,[a.Hs]),h=(0,a.Hs)(),g=e=>{let{__scopeAlertDialog:t,...n}=e,r=h(t);return(0,s.jsx)(a.bL,{...r,...n,modal:!0})};g.displayName=c;var p=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.l9,{...l,...r,ref:t})});p.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...n}=e,r=h(t);return(0,s.jsx)(a.ZL,{...r,...n})};v.displayName="AlertDialogPortal";var m=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.hJ,{...l,...r,ref:t})});m.displayName="AlertDialogOverlay";var y="AlertDialogContent",[b,w]=d(y),x=(0,u.Dc)("AlertDialogContent"),D=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:l,...u}=e,c=h(n),d=r.useRef(null),f=(0,i.s)(t,d),g=r.useRef(null);return(0,s.jsx)(a.G$,{contentName:y,titleName:E,docsSlug:"alert-dialog",children:(0,s.jsx)(b,{scope:n,cancelRef:g,children:(0,s.jsxs)(a.UC,{role:"alertdialog",...c,...u,ref:f,onOpenAutoFocus:(0,o.m)(u.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=g.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(x,{children:l}),(0,s.jsx)(I,{contentRef:d})]})})})});D.displayName=y;var E="AlertDialogTitle",C=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.hE,{...l,...r,ref:t})});C.displayName=E;var R="AlertDialogDescription",S=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.VY,{...l,...r,ref:t})});S.displayName=R;var k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.bm,{...l,...r,ref:t})});k.displayName="AlertDialogAction";var M="AlertDialogCancel",O=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:l}=w(M,n),o=h(n),u=(0,i.s)(t,l);return(0,s.jsx)(a.bm,{...o,...r,ref:u})});O.displayName=M;var I=e=>{let{contentRef:t}=e,n="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(R,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},N=g,j=p,A=v,T=m,L=D,P=k,z=O,F=C,_=S},49027:(e,t,n)=>{n.d(t,{be:()=>a,gB:()=>d,gl:()=>y});var r=n(12115),l=n(17787),i=n(27587);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s="Sortable",c=r.createContext({activeIndex:-1,containerId:s,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function d(e){let{children:t,id:n,items:a,strategy:o=u,disabled:d=!1}=e,{active:f,dragOverlay:h,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.fF)(),m=(0,i.YG)(s,n),y=null!==h.rect,b=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=f,x=f?b.indexOf(f.id):-1,D=p?b.indexOf(p.id):-1,E=(0,r.useRef)(b),C=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(b,E.current),R=-1!==D&&-1===x||C,S="boolean"==typeof d?{draggable:d,droppable:d}:d;(0,i.Es)(()=>{C&&w&&v(b)},[C,b,w,v]),(0,r.useEffect)(()=>{E.current=b},[b]);let k=(0,r.useMemo)(()=>({activeIndex:x,containerId:m,disabled:S,disableTransforms:R,items:b,overIndex:D,useDragOverlay:y,sortedRects:b.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(b.length)),strategy:o}),[x,m,S.draggable,S.droppable,R,b,D,g,y,o]);return r.createElement(c.Provider,{value:k},t)}let f=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},h=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},g={duration:200,easing:"ease"},p="transform",v=i.Ks.Transition.toString({property:p,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function y(e){var t,n,a,u;let{animateLayoutChanges:s=h,attributes:d,disabled:y,data:b,getNewIndex:w=f,id:x,strategy:D,resizeObserverConfig:E,transition:C=g}=e,{items:R,containerId:S,activeIndex:k,disabled:M,disableTransforms:O,sortedRects:I,overIndex:N,useDragOverlay:j,strategy:A}=(0,r.useContext)(c),T=(t=y,n=M,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),L=R.indexOf(x),P=(0,r.useMemo)(()=>({sortable:{containerId:S,index:L,items:R},...b}),[S,b,L,R]),z=(0,r.useMemo)(()=>R.slice(R.indexOf(x)),[R,x]),{rect:F,node:_,isOver:W,setNodeRef:U}=(0,l.zM)({id:x,data:P,disabled:T.droppable,resizeObserverConfig:{updateMeasurementsFor:z,...E}}),{active:Y,activatorEvent:B,activeNodeRect:K,attributes:G,setNodeRef:V,listeners:X,isDragging:H,over:Z,setActivatorNodeRef:$,transform:q}=(0,l.PM)({id:x,data:P,attributes:{...m,...d},disabled:T.draggable}),J=(0,i.jn)(U,V),Q=!!Y,ee=Q&&!O&&o(k)&&o(N),et=!j&&H,en=et&&ee?q:null,er=ee?null!=en?en:(null!=D?D:A)({rects:I,activeNodeRect:K,activeIndex:k,overIndex:N,index:L}):null,el=o(k)&&o(N)?w({id:x,items:R,activeIndex:k,overIndex:N}):L,ei=null==Y?void 0:Y.id,ea=(0,r.useRef)({activeId:ei,items:R,newIndex:el,containerId:S}),eo=R!==ea.current.items,eu=s({active:Y,containerId:S,isDragging:H,isSorting:Q,id:x,index:L,items:R,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:C,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:L,node:_,rect:F});return(0,r.useEffect)(()=>{Q&&ea.current.newIndex!==el&&(ea.current.newIndex=el),S!==ea.current.containerId&&(ea.current.containerId=S),R!==ea.current.items&&(ea.current.items=R)},[Q,el,S,R]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(ei&&!ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:Y,activeIndex:k,attributes:G,data:P,rect:F,index:L,newIndex:el,items:R,isOver:W,isSorting:Q,isDragging:H,listeners:X,node:_,overIndex:N,over:Z,setNodeRef:J,setActivatorNodeRef:$,setDroppableNodeRef:U,setDraggableNodeRef:V,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===L?v:(!et||(0,i.kx)(B))&&C&&(Q||eu)?i.Ks.Transition.toString({...C,property:p}):void 0}}l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left},49476:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71847).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},65229:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(71847).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},71847:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:o=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...a,width:l,height:l,stroke:n,strokeWidth:u?24*Number(o)/Number(l):o,className:i("lucide",s),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:u,...s}=n;return(0,r.createElement)(o,{ref:a,iconNode:t,className:i("lucide-".concat(l(e)),u),...s})});return n.displayName="".concat(e),n}},74879:(e,t,n)=>{let r;n.d(t,{A:()=>o});let l={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},i=new Uint8Array(16),a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let o=function(e,t,n){if(l.randomUUID&&!t&&!e)return l.randomUUID();let o=(e=e||{}).random??e.rng?.()??function(){if(!r){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(i)}();if(o.length<16)throw Error("Random bytes length must be >= 16");if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){if((n=n||0)<0||n+16>t.length)throw RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[n+e]=o[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(o)}},83101:(e,t,n)=>{n.d(t,{F:()=>a});var r=n(2821);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:o}=t,u=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==o?void 0:o[e];if(null===t)return null;let i=l(t)||l(r);return a[e][i]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...s}[t]):({...o,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},89511:(e,t,n)=>{n.d(t,{G$:()=>H,Hs:()=>x,UC:()=>en,VY:()=>el,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(12115),l=n(92556),i=n(94446),a=n(3468),o=n(68946),u=n(23558),s=n(44831),c=n(69666),d=n(75433),f=n(76842),h=n(97602),g=n(19526),p=n(14432),v=n(97745),m=n(32467),y=n(95155),b="Dialog",[w,x]=(0,a.A)(b),[D,E]=w(b),C=e=>{let{__scopeDialog:t,children:n,open:l,defaultOpen:i,onOpenChange:a,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,h]=(0,u.i)({prop:l,defaultProp:null!=i&&i,onChange:a,caller:b});return(0,y.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:f,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:s,children:n})};C.displayName=b;var R="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=E(R,n),o=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":V(a.open),...r,ref:o,onClick:(0,l.m)(e.onClick,a.onOpenToggle)})});S.displayName=R;var k="DialogPortal",[M,O]=w(k,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:l,container:i}=e,a=E(k,t);return(0,y.jsx)(M,{scope:t,forceMount:n,children:r.Children.map(l,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};I.displayName=k;var N="DialogOverlay",j=r.forwardRef((e,t)=>{let n=O(N,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=E(N,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(T,{...l,ref:t})}):null});j.displayName=N;var A=(0,m.TL)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(N,n);return(0,y.jsx)(p.A,{as:A,allowPinchZoom:!0,shards:[l.contentRef],children:(0,y.jsx)(h.sG.div,{"data-state":V(l.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",P=r.forwardRef((e,t)=>{let n=O(L,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=E(L,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(z,{...l,ref:t}):(0,y.jsx)(F,{...l,ref:t})})});P.displayName=L;var z=r.forwardRef((e,t)=>{let n=E(L,e.__scopeDialog),a=r.useRef(null),o=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(_,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=E(L,e.__scopeDialog),l=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(l.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),l.current=!1,i.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let o=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(o))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:l,onOpenAutoFocus:a,onCloseAutoFocus:o,...u}=e,d=E(L,n),f=r.useRef(null),h=(0,i.s)(t,f);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:a,onUnmountAutoFocus:o,children:(0,y.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...u,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)($,{titleId:d.titleId}),(0,y.jsx)(q,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(W,n);return(0,y.jsx)(h.sG.h2,{id:l.titleId,...r,ref:t})});U.displayName=W;var Y="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(Y,n);return(0,y.jsx)(h.sG.p,{id:l.descriptionId,...r,ref:t})});B.displayName=Y;var K="DialogClose",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(K,n);return(0,y.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:(0,l.m)(e.onClick,()=>i.onOpenChange(!1))})});function V(e){return e?"open":"closed"}G.displayName=K;var X="DialogTitleWarning",[H,Z]=(0,a.q)(X,{contentName:L,titleName:W,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,n=Z(X),l="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(l))},[l,t]),null},q=e=>{let{contentRef:t,descriptionId:n}=e,l=Z("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(l.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},J=C,Q=S,ee=I,et=j,en=P,er=U,el=B,ei=G},89863:(e,t,n)=>{n.d(t,{Zr:()=>l});let r=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>r(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>r(t)(e)}}},l=(e,t)=>(n,l,i)=>{let a,o={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var t;let r=e=>null===e?null:JSON.parse(e,void 0),l=null!=(t=n.getItem(e))?t:null;return l instanceof Promise?l.then(r):r(l)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,s=new Set,c=new Set,d=o.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...e)},l,i);let f=()=>{let e=o.partialize({...l()});return d.setItem(o.name,{state:e,version:o.version})},h=i.setState;i.setState=(e,t)=>{h(e,t),f()};let g=e((...e)=>{n(...e),f()},l,i);i.getInitialState=()=>g;let p=()=>{var e,t;if(!d)return;u=!1,s.forEach(e=>{var t;return e(null!=(t=l())?t:g)});let i=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=l())?e:g))||void 0;return r(d.getItem.bind(d))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[r,i]=e;if(n(a=o.merge(i,null!=(t=l())?t:g),!0),r)return f()}).then(()=>{null==i||i(a,void 0),a=l(),u=!0,c.forEach(e=>e(a))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{o={...o,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>p(),hasHydrated:()=>u,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||p(),a||g}},94446:(e,t,n)=>{n.d(t,{s:()=>a,t:()=>i});var r=n(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function a(...e){return r.useCallback(i(...e),e)}}}]);