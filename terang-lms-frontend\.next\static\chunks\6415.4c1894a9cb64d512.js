try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},E=(new t.Error).stack;E&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[E]="cf3a1d08-c0f4-4f9f-957a-1cc451a96f0d",t._sentryDebugIdIdentifier="sentry-dbid-cf3a1d08-c0f4-4f9f-957a-1cc451a96f0d")}catch(t){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6415],{6415:(t,E,e)=>{function i(t){var E=[];return t.split(" ").forEach(function(t){E.push({name:t})}),E}e.r(E),e.d(E,{forth:()=>O});var n=i("INVERT AND OR XOR 2* 2/ LSHIFT RSHIFT 0= = 0< < > U< MIN MAX 2DROP 2DUP 2OVER 2SWAP ?DUP DEPTH DROP DUP OVER ROT SWAP >R R> R@ + - 1+ 1- ABS NEGATE S>D * M* UM* FM/MOD SM/REM UM/MOD */ */MOD / /MOD MOD HERE , @ ! CELL+ CELLS C, C@ C! CHARS 2@ 2! ALIGN ALIGNED +! ALLOT CHAR [CHAR] [ ] BL FIND EXECUTE IMMEDIATE COUNT LITERAL STATE ; DOES> >BODY EVALUATE SOURCE >IN <# # #S #> HOLD SIGN BASE >NUMBER HEX DECIMAL FILL MOVE . CR EMIT SPACE SPACES TYPE U. .R U.R ACCEPT TRUE FALSE <> U> 0<> 0> NIP TUCK ROLL PICK 2>R 2R@ 2R> WITHIN UNUSED MARKER I J TO COMPILE, [COMPILE] SAVE-INPUT RESTORE-INPUT PAD ERASE 2LITERAL DNEGATE D- D+ D0< D0= D2* D2/ D< D= DMAX DMIN D>S DABS M+ M*/ D. D.R 2ROT DU< CATCH THROW FREE RESIZE ALLOCATE CS-PICK CS-ROLL GET-CURRENT SET-CURRENT FORTH-WORDLIST GET-ORDER SET-ORDER PREVIOUS SEARCH-WORDLIST WORDLIST FIND ALSO ONLY FORTH DEFINITIONS ORDER -TRAILING /STRING SEARCH COMPARE CMOVE CMOVE> BLANK SLITERAL"),r=i("IF ELSE THEN BEGIN WHILE REPEAT UNTIL RECURSE [IF] [ELSE] [THEN] ?DO DO LOOP +LOOP UNLOOP LEAVE EXIT AGAIN CASE OF ENDOF ENDCASE");function R(t,E){var e;for(e=t.length-1;e>=0;e--)if(t[e].name===E.toUpperCase())return t[e]}let O={name:"forth",startState:function(){return{state:"",base:10,coreWordList:n,immediateWordList:r,wordList:[]}},token:function(t,E){var e;if(t.eatSpace())return null;if(""===E.state){if(t.match(/^(\]|:NONAME)(\s|$)/i))return E.state=" compilation","builtin";if(e=t.match(/^(\:)\s+(\S+)(\s|$)+/))return E.wordList.push({name:e[2].toUpperCase()}),E.state=" compilation","def";if(e=t.match(/^(VARIABLE|2VARIABLE|CONSTANT|2CONSTANT|CREATE|POSTPONE|VALUE|WORD)\s+(\S+)(\s|$)+/i))return E.wordList.push({name:e[2].toUpperCase()}),"def";if(e=t.match(/^(\'|\[\'\])\s+(\S+)(\s|$)+/))return"builtin"}else{if(t.match(/^(\;|\[)(\s)/))return E.state="",t.backUp(1),"builtin";if(t.match(/^(\;|\[)($)/))return E.state="","builtin";if(t.match(/^(POSTPONE)\s+\S+(\s|$)+/))return"builtin"}if(e=t.match(/^(\S+)(\s+|$)/))return void 0!==R(E.wordList,e[1])?"variable":"\\"===e[1]?(t.skipToEnd(),"comment"):void 0!==R(E.coreWordList,e[1])?"builtin":void 0!==R(E.immediateWordList,e[1])?"keyword":"("===e[1]?(t.eatWhile(function(t){return")"!==t}),t.eat(")"),"comment"):".("===e[1]?(t.eatWhile(function(t){return")"!==t}),t.eat(")"),"string"):'S"'===e[1]||'."'===e[1]||'C"'===e[1]?(t.eatWhile(function(t){return'"'!==t}),t.eat('"'),"string"):e[1]-0xfffffffff?"number":"atom"}}}}]);