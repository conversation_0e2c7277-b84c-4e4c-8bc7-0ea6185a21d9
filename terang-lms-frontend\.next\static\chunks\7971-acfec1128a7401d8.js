try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="49c1fb4c-6f51-4543-9bae-f435502037b7",e._sentryDebugIdIdentifier="sentry-dbid-49c1fb4c-6f51-4543-9bae-f435502037b7")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7971],{47971:(e,n,r)=>{r.d(n,{H_:()=>e1,UC:()=>eJ,YJ:()=>eQ,q7:()=>e0,VF:()=>e4,JU:()=>e$,ZL:()=>eZ,bL:()=>ez,wv:()=>e2,l9:()=>eY});var t=r(12115),o=r(92556),a=r(94446),u=r(3468),l=r(23558),i=r(97602),s=r(29118),d=r(66218),c=r(44831),f=r(19526),p=r(69666),m=r(68946),v=r(66093),w=r(75433),g=r(76842),h=r(72431),y=r(32467),x=r(70222),b=r(97745),M=r(14432),R=r(95155),C=["Enter"," "],D=["ArrowUp","PageDown","End"],j=["ArrowDown","PageUp","Home",...D],_={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},I={ltr:["ArrowLeft"],rtl:["ArrowRight"]},N="Menu",[T,k,E]=(0,s.N)(N),[P,O]=(0,u.A)(N,[E,v.Bk,h.RG]),A=(0,v.Bk)(),S=(0,h.RG)(),[F,L]=P(N),[K,G]=P(N),U=e=>{let{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=A(n),[s,c]=t.useState(null),f=t.useRef(!1),p=(0,x.c)(u),m=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,R.jsx)(v.bL,{...i,children:(0,R.jsx)(F,{scope:n,open:r,onOpenChange:p,content:s,onContentChange:c,children:(0,R.jsx)(K,{scope:n,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};U.displayName=N;var B=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=A(r);return(0,R.jsx)(v.Mz,{...o,...t,ref:n})});B.displayName="MenuAnchor";var V="MenuPortal",[W,q]=P(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:n,forceMount:r,children:t,container:o}=e,a=L(V,n);return(0,R.jsx)(W,{scope:n,forceMount:r,children:(0,R.jsx)(g.C,{present:r||a.open,children:(0,R.jsx)(w.Z,{asChild:!0,container:o,children:t})})})};H.displayName=V;var X="MenuContent",[z,Y]=P(X),Z=t.forwardRef((e,n)=>{let r=q(X,e.__scopeMenu),{forceMount:t=r.forceMount,...o}=e,a=L(X,e.__scopeMenu),u=G(X,e.__scopeMenu);return(0,R.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(g.C,{present:t||a.open,children:(0,R.jsx)(T.Slot,{scope:e.__scopeMenu,children:u.modal?(0,R.jsx)(J,{...o,ref:n}):(0,R.jsx)(Q,{...o,ref:n})})})})}),J=t.forwardRef((e,n)=>{let r=L(X,e.__scopeMenu),u=t.useRef(null),l=(0,a.s)(n,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,b.Eq)(e)},[]),(0,R.jsx)(ee,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Q=t.forwardRef((e,n)=>{let r=L(X,e.__scopeMenu);return(0,R.jsx)(ee,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),$=(0,y.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,n)=>{let{__scopeMenu:r,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:m,onEscapeKeyDown:w,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:C,..._}=e,I=L(X,r),N=G(X,r),T=A(r),E=S(r),P=k(r),[O,F]=t.useState(null),K=t.useRef(null),U=(0,a.s)(n,K,I.onContentChange),B=t.useRef(0),V=t.useRef(""),W=t.useRef(0),q=t.useRef(null),H=t.useRef("right"),Y=t.useRef(0),Z=C?M.A:t.Fragment,J=e=>{var n,r;let t=V.current+e,o=P().filter(e=>!e.disabled),a=document.activeElement,u=null==(n=o.find(e=>e.ref.current===a))?void 0:n.textValue,l=function(e,n,r){var t;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,a=r?e.indexOf(r):-1,u=(t=Math.max(a,0),e.map((n,r)=>e[(t+r)%e.length]));1===o.length&&(u=u.filter(e=>e!==r));let l=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==r?l:void 0}(o.map(e=>e.textValue),t,u),i=null==(r=o.find(e=>e.textValue===l))?void 0:r.ref.current;!function e(n){V.current=n,window.clearTimeout(B.current),""!==n&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.Oh)();let Q=t.useCallback(e=>{var n,r;return H.current===(null==(n=q.current)?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:r,y:t}=e,o=!1;for(let e=0,a=n.length-1;e<n.length;a=e++){let u=n[e],l=n[a],i=u.x,s=u.y,d=l.x,c=l.y;s>t!=c>t&&r<(d-i)*(t-s)/(c-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null==(r=q.current)?void 0:r.area)},[]);return(0,R.jsx)(z,{scope:r,searchRef:V,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var n;Q(e)||(null==(n=K.current)||n.focus(),F(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:W,onPointerGraceIntentChange:t.useCallback(e=>{q.current=e},[]),children:(0,R.jsx)(Z,{...C?{as:$,allowPinchZoom:!0}:void 0,children:(0,R.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(i,e=>{var n;e.preventDefault(),null==(n=K.current)||n.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,R.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:w,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,R.jsx)(h.bL,{asChild:!0,...E,dir:N.dir,orientation:"vertical",loop:u,currentTabStopId:O,onCurrentTabStopIdChange:F,onEntryFocus:(0,o.m)(m,e=>{N.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":ej(I.open),"data-radix-menu-content":"",dir:N.dir,...T,..._,ref:U,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!r&&t&&J(e.key));let o=K.current;if(e.target!==o||!j.includes(e.key))return;e.preventDefault();let a=P().filter(e=>!e.disabled).map(e=>e.ref.current);D.includes(e.key)&&a.reverse(),function(e){let n=document.activeElement;for(let r of e)if(r===n||(r.focus(),document.activeElement!==n))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eN(e=>{let n=e.target,r=Y.current!==e.clientX;e.currentTarget.contains(n)&&r&&(H.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Z.displayName=X;var en=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,R.jsx)(i.sG.div,{role:"group",...t,ref:n})});en.displayName="MenuGroup";var er=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,R.jsx)(i.sG.div,{...t,ref:n})});er.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,n)=>{let{disabled:r=!1,onSelect:u,...l}=e,s=t.useRef(null),d=G(et,e.__scopeMenu),c=Y(et,e.__scopeMenu),f=(0,a.s)(n,s),p=t.useRef(!1);return(0,R.jsx)(eu,{...l,ref:f,disabled:r,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!r&&e){let n=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,i.hO)(e,n),n.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:n=>{var r;null==(r=e.onPointerDown)||r.call(e,n),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;p.current||null==(n=e.currentTarget)||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;r||n&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var eu=t.forwardRef((e,n)=>{let{__scopeMenu:r,disabled:u=!1,textValue:l,...s}=e,d=Y(et,r),c=S(r),f=t.useRef(null),p=(0,a.s)(n,f),[m,v]=t.useState(!1),[w,g]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var n;g((null!=(n=e.textContent)?n:"").trim())}},[s.children]),(0,R.jsx)(T.ItemSlot,{scope:r,disabled:u,textValue:null!=l?l:w,children:(0,R.jsx)(h.q7,{asChild:!0,...c,focusable:!u,children:(0,R.jsx)(i.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eN(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eN(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),el=t.forwardRef((e,n)=>{let{checked:r=!1,onCheckedChange:t,...a}=e;return(0,R.jsx)(ev,{scope:e.__scopeMenu,checked:r,children:(0,R.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(r)?"mixed":r,...a,ref:n,"data-state":eI(r),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!e_(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,ed]=P(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,n)=>{let{value:r,onValueChange:t,...o}=e,a=(0,x.c)(t);return(0,R.jsx)(es,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,R.jsx)(en,{...o,ref:n})})});ec.displayName=ei;var ef="MenuRadioItem",ep=t.forwardRef((e,n)=>{let{value:r,...t}=e,a=ed(ef,e.__scopeMenu),u=r===a.value;return(0,R.jsx)(ev,{scope:e.__scopeMenu,checked:u,children:(0,R.jsx)(ea,{role:"menuitemradio","aria-checked":u,...t,ref:n,"data-state":eI(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,r)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var em="MenuItemIndicator",[ev,ew]=P(em,{checked:!1}),eg=t.forwardRef((e,n)=>{let{__scopeMenu:r,forceMount:t,...o}=e,a=ew(em,r);return(0,R.jsx)(g.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,R.jsx)(i.sG.span,{...o,ref:n,"data-state":eI(a.checked)})})});eg.displayName=em;var eh=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e;return(0,R.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:n})});eh.displayName="MenuSeparator";var ey=t.forwardRef((e,n)=>{let{__scopeMenu:r,...t}=e,o=A(r);return(0,R.jsx)(v.i3,{...o,...t,ref:n})});ey.displayName="MenuArrow";var[ex,eb]=P("MenuSub"),eM="MenuSubTrigger",eR=t.forwardRef((e,n)=>{let r=L(eM,e.__scopeMenu),u=G(eM,e.__scopeMenu),l=eb(eM,e.__scopeMenu),i=Y(eM,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,R.jsx)(B,{asChild:!0,...f,children:(0,R.jsx)(eu,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":ej(r.open),...e,ref:(0,a.t)(n,l.onTriggerChange),onClick:n=>{var t;null==(t=e.onClick)||t.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eN(n=>{i.onItemEnter(n),!n.defaultPrevented&&(e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eN(e=>{var n,t;p();let o=null==(n=r.content)?void 0:n.getBoundingClientRect();if(o){let n=null==(t=r.content)?void 0:t.dataset.side,a="right"===n,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==n.key)&&_[u.dir].includes(n.key)){var o;r.onOpenChange(!0),null==(o=r.content)||o.focus(),n.preventDefault()}})})})});eR.displayName=eM;var eC="MenuSubContent",eD=t.forwardRef((e,n)=>{let r=q(X,e.__scopeMenu),{forceMount:u=r.forceMount,...l}=e,i=L(X,e.__scopeMenu),s=G(X,e.__scopeMenu),d=eb(eC,e.__scopeMenu),c=t.useRef(null),f=(0,a.s)(n,c);return(0,R.jsx)(T.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(g.C,{present:u||i.open,children:(0,R.jsx)(T.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;s.isUsingKeyboardRef.current&&(null==(n=c.current)||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),r=I[s.dir].includes(e.key);if(n&&r){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function ej(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eI(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return n=>"mouse"===n.pointerType?e(n):void 0}eD.displayName=eC;var eT="DropdownMenu",[ek,eE]=(0,u.A)(eT,[O]),eP=O(),[eO,eA]=ek(eT),eS=e=>{let{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:s=!0}=e,d=eP(n),c=t.useRef(null),[f,p]=(0,l.i)({prop:a,defaultProp:null!=u&&u,onChange:i,caller:eT});return(0,R.jsx)(eO,{scope:n,triggerId:(0,m.B)(),triggerRef:c,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,R.jsx)(U,{...d,open:f,onOpenChange:p,dir:o,modal:s,children:r})})};eS.displayName=eT;var eF="DropdownMenuTrigger",eL=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,disabled:t=!1,...u}=e,l=eA(eF,r),s=eP(r);return(0,R.jsx)(B,{asChild:!0,...s,children:(0,R.jsx)(i.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(n,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eL.displayName=eF;var eK=e=>{let{__scopeDropdownMenu:n,...r}=e,t=eP(n);return(0,R.jsx)(H,{...t,...r})};eK.displayName="DropdownMenuPortal";var eG="DropdownMenuContent",eU=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...a}=e,u=eA(eG,r),l=eP(r),i=t.useRef(!1);return(0,R.jsx)(Z,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;i.current||null==(n=u.triggerRef.current)||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,r=0===n.button&&!0===n.ctrlKey,t=2===n.button||r;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=eG;var eB=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(en,{...o,...t,ref:n})});eB.displayName="DropdownMenuGroup";var eV=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(er,{...o,...t,ref:n})});eV.displayName="DropdownMenuLabel";var eW=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(ea,{...o,...t,ref:n})});eW.displayName="DropdownMenuItem";var eq=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(el,{...o,...t,ref:n})});eq.displayName="DropdownMenuCheckboxItem",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(ec,{...o,...t,ref:n})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(ep,{...o,...t,ref:n})}).displayName="DropdownMenuRadioItem";var eH=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(eg,{...o,...t,ref:n})});eH.displayName="DropdownMenuItemIndicator";var eX=t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(eh,{...o,...t,ref:n})});eX.displayName="DropdownMenuSeparator",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(ey,{...o,...t,ref:n})}).displayName="DropdownMenuArrow",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(eR,{...o,...t,ref:n})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,n)=>{let{__scopeDropdownMenu:r,...t}=e,o=eP(r);return(0,R.jsx)(eD,{...o,...t,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ez=eS,eY=eL,eZ=eK,eJ=eU,eQ=eB,e$=eV,e0=eW,e1=eq,e4=eH,e2=eX},72431:(e,n,r)=>{r.d(n,{RG:()=>b,bL:()=>T,q7:()=>k});var t=r(12115),o=r(92556),a=r(29118),u=r(94446),l=r(3468),i=r(68946),s=r(97602),d=r(70222),c=r(23558),f=r(66218),p=r(95155),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[g,h,y]=(0,a.N)(w),[x,b]=(0,l.A)(w,[y]),[M,R]=x(w),C=t.forwardRef((e,n)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(D,{...e,ref:n})})}));C.displayName=w;var D=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:x,onEntryFocus:b,preventScrollOnEntryFocus:R=!1,...C}=e,D=t.useRef(null),j=(0,u.s)(n,D),_=(0,f.jH)(i),[I,T]=(0,c.i)({prop:g,defaultProp:null!=y?y:null,onChange:x,caller:w}),[k,E]=t.useState(!1),P=(0,d.c)(b),O=h(r),A=t.useRef(!1),[S,F]=t.useState(0);return t.useEffect(()=>{let e=D.current;if(e)return e.addEventListener(m,P),()=>e.removeEventListener(m,P)},[P]),(0,p.jsx)(M,{scope:r,orientation:a,dir:_,loop:l,currentTabStopId:I,onItemFocus:t.useCallback(e=>T(e),[T]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>F(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:k||0===S?-1:0,"data-orientation":a,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{A.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!A.current;if(e.target===e.currentTarget&&n&&!k){let n=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=O().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),R)}}A.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),j="RovingFocusGroupItem",_=t.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:u=!1,tabStopId:l,children:d,...c}=e,f=(0,i.B)(),m=l||f,v=R(j,r),w=v.currentTabStopId===m,y=h(r),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:M}=v;return t.useEffect(()=>{if(a)return x(),()=>b()},[a,x,b]),(0,p.jsx)(g.ItemSlot,{scope:r,id:m,focusable:a,active:u,children:(0,p.jsx)(s.sG.span,{tabIndex:w?0:-1,"data-orientation":v.orientation,...c,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let n=function(e,n,r){var t;let o=(t=e.key,"rtl"!==r?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,v.orientation,v.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)r.reverse();else if("prev"===n||"next"===n){"prev"===n&&r.reverse();let t=r.indexOf(e.currentTarget);r=v.loop?function(e,n){return e.map((r,t)=>e[(n+t)%e.length])}(r,t+1):r.slice(t+1)}setTimeout(()=>N(r))}}),children:"function"==typeof d?d({isCurrentTabStop:w,hasTabStop:null!=M}):d})})});_.displayName=j;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let t of e)if(t===r||(t.focus({preventScroll:n}),document.activeElement!==r))return}var T=C,k=_},76842:(e,n,r)=>{r.d(n,{C:()=>u});var t=r(12115),o=r(94446),a=r(4129),u=e=>{let{present:n,children:r}=e,u=function(e){var n,r;let[o,u]=t.useState(),i=t.useRef(null),s=t.useRef(e),d=t.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,n)=>{let t=r[e][n];return null!=t?t:e},n));return t.useEffect(()=>{let e=l(i.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=i.current,r=s.current;if(r!==e){let t=d.current,o=l(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):r&&t!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let n,r=null!=(e=o.ownerDocument.defaultView)?e:window,t=e=>{let t=l(i.current).includes(e.animationName);if(e.target===o&&t&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(i.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",t),o.addEventListener("animationend",t),()=>{r.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",t),o.removeEventListener("animationend",t)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:t.useCallback(e=>{i.current=e?getComputedStyle(e):null,u(e)},[])}}(n),i="function"==typeof r?r({present:u.isPresent}):t.Children.only(r),s=(0,o.s)(u.ref,function(e){var n,r;let t=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=t&&"isReactWarning"in t&&t.isReactWarning;return o?e.ref:(o=(t=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||u.isPresent?t.cloneElement(i,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"}}]);