try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="74062e2f-d483-40b0-bceb-f9da97799a63",e._sentryDebugIdIdentifier="sentry-dbid-74062e2f-d483-40b0-bceb-f9da97799a63")}catch(e){}(()=>{var e={};e.id=1376,e.ids=[1376],e.modules={116:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(91754);function a({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}s(93491),s(76328)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5827:(e,t,s)=>{"use strict";s.d(t,{k:()=>o});var r=s(91754),a=s(93491),n=s(66536),i=s(82233);let o=a.forwardRef(({className:e,value:t,...s},a)=>(0,r.jsx)(n.bL,{ref:a,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:(0,r.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));o.displayName=n.bL.displayName},8086:e=>{"use strict";e.exports=require("module")},9005:(e,t,s)=>{Promise.resolve().then(s.bind(s,96532))},9260:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var r=s(91754);s(93491);var a=s(82233);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,s)=>{Promise.resolve().then(s.bind(s,7346)),Promise.resolve().then(s.bind(s,21444)),Promise.resolve().then(s.bind(s,3033)),Promise.resolve().then(s.bind(s,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21381:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},21820:e=>{"use strict";e.exports=require("os")},26711:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29178:(e,t,s)=>{Promise.resolve().then(s.bind(s,85342))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},36991:(e,t,s)=>{"use strict";s.d(t,{B8:()=>k,UC:()=>E,bL:()=>P,l9:()=>D});var r=s(93491),a=s(18682),n=s(10158),i=s(92023),o=s(55462),d=s(90604),l=s(78283),c=s(76322),u=s(62962),p=s(91754),m="Tabs",[x,f]=(0,n.A)(m,[i.RG]),h=(0,i.RG)(),[g,v]=x(m),y=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:a,defaultValue:n,orientation:i="horizontal",dir:o,activationMode:x="automatic",...f}=e,h=(0,l.jH)(o),[v,y]=(0,c.i)({prop:r,onChange:a,defaultProp:n??"",caller:m});return(0,p.jsx)(g,{scope:s,baseId:(0,u.B)(),value:v,onValueChange:y,orientation:i,dir:h,activationMode:x,children:(0,p.jsx)(d.sG.div,{dir:h,"data-orientation":i,...f,ref:t})})});y.displayName=m;var b="TabsList",j=r.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...a}=e,n=v(b,s),o=h(s);return(0,p.jsx)(i.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:r,children:(0,p.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...a,ref:t})})});j.displayName=b;var N="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...o}=e,l=v(N,s),c=h(s),u=q(l.baseId,r),m=S(l.baseId,r),x=r===l.value;return(0,p.jsx)(i.q7,{asChild:!0,...c,focusable:!n,active:x,children:(0,p.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;x||n||!e||l.onValueChange(r)})})})});w.displayName=N;var A="TabsContent",C=r.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,forceMount:n,children:i,...l}=e,c=v(A,s),u=q(c.baseId,a),m=S(c.baseId,a),x=a===c.value,f=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(o.C,{present:n||x,children:({present:s})=>(0,p.jsx)(d.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:s&&i})})});function q(e,t){return`${e}-trigger-${t}`}function S(e,t){return`${e}-content-${t}`}C.displayName=A;var P=y,k=j,D=w,E=C},37067:e=>{"use strict";e.exports=require("node:http")},38006:(e,t,s)=>{Promise.resolve().then(s.bind(s,116))},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},51897:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54090:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>l,av:()=>c,j7:()=>d,tU:()=>o});var r=s(91754),a=s(93491),n=s(36991),i=s(82233);let o=n.bL,d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.B8,{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=n.B8.displayName;let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.l9,{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",e),...t}));l.displayName=n.l9.displayName;let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.UC,{ref:s,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=n.UC.displayName},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>y,generateImageMetadata:()=>g,generateMetadata:()=>h,generateViewport:()=>v,metadata:()=>p});var a=s(63033),n=s(18188),i=s(5434),o=s(45188),d=s(67999),l=s(4590),c=s(23064),u=s(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),s=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:s,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...a},f="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;r=new Proxy(m,{apply:(e,t,s)=>{let r,a,n;try{let e=f?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}});let h=void 0,g=void 0,v=void 0,y=r},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66536:(e,t,s)=>{"use strict";s.d(t,{C1:()=>j,bL:()=>b});var r=s(93491),a=s(10158),n=s(90604),i=s(91754),o="Progress",[d,l]=(0,a.A)(o),[c,u]=d(o),p=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:a,value:o=null,max:d,getValueLabel:l=f,...u}=e;(d||0===d)&&!v(d)&&console.error((s=`${d}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=v(d)?d:100;null===o||y(o,p)||console.error((r=`${o}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=y(o,p)?o:null,x=g(m)?l(m,p):void 0;return(0,i.jsx)(c,{scope:a,value:m,max:p,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":h(m,p),"data-value":m??void 0,"data-max":p,...u,ref:t})})});p.displayName=o;var m="ProgressIndicator",x=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,a=u(m,s);return(0,i.jsx)(n.sG.div,{"data-state":h(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function y(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=m;var b=p,j=x},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,s)=>{Promise.resolve().then(s.bind(s,5434)),Promise.resolve().then(s.bind(s,45188)),Promise.resolve().then(s.bind(s,67999)),Promise.resolve().then(s.bind(s,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80601:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(91754);s(93491);var a=s(16435),n=s(25758),i=s(82233);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...n}){let d=s?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},85342:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(91754),a=s(93491),n=s(81012),i=s(76328),o=s(9260),d=s(56682),l=s(59672),c=s(80601),u=s(5827),p=s(54090),m=s(26711),x=s(51897),f=s(86857),h=s(69622),g=s(21381),v=s(41867),y=s(16041),b=s.n(y);function j(){let[e,t]=(0,a.useState)(""),[s,y]=(0,a.useState)(""),[j,N]=(0,a.useState)([]),[w,A]=(0,a.useState)([]),[C,q]=(0,a.useState)(!0),[S,P]=(0,a.useState)(!1),k=async()=>{try{let e=i.qs.getUser();if(!e)return void n.oR.error("Please log in to view courses");let t=await fetch(`/api/enrollments?studentId=${e.id}`);if(t.ok){let e=await t.json();N(e)}let s=await fetch("/api/courses");if(s.ok){let e=await s.json();A(e)}}catch(e){console.error("Error fetching courses:",e),n.oR.error("Failed to load courses")}finally{q(!1)}},D=async()=>{if(!s.trim())return;let e=i.qs.getUser();if(!e)return void n.oR.error("Please log in to enroll in courses");P(!0);try{let t=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:e.id,courseCode:s.trim()})});if(t.ok){let e=await t.json();n.oR.success(`Successfully enrolled in ${e.courseName}!`),y(""),k()}else{let e=await t.json();n.oR.error(e.message||"Failed to enroll in course")}}catch(e){console.error("Enrollment error:",e),n.oR.error("An error occurred while enrolling")}finally{P(!1)}},E=j.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase())),R=w.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())||t.description.toLowerCase().includes(e.toLowerCase()));return(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentCoursesPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Courses"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Access your enrolled courses and discover new ones"})]})}),(0,r.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Quick Enrollment"}),(0,r.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter a course code to quickly enroll in a course"})]}),(0,r.jsx)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(l.p,{placeholder:"Enter course code (e.g., MATH101)",value:s,onChange:e=>y(e.target.value.toUpperCase()),className:"flex-1","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"}),(0,r.jsx)(d.$,{onClick:D,disabled:!s.trim()||S,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:S?"Enrolling...":"Enroll"})]})})]}),(0,r.jsxs)(p.tU,{defaultValue:"enrolled",className:"space-y-6","data-sentry-element":"Tabs","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(p.j7,{"data-sentry-element":"TabsList","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(p.Xi,{value:"enrolled","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"My Courses"}),(0,r.jsx)(p.Xi,{value:"available","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"Available Courses"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(m.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,r.jsx)(l.p,{placeholder:"Search courses...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,r.jsxs)(p.av,{value:"enrolled","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:[C?(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[1,2,3].map(e=>(0,r.jsxs)(o.Zp,{className:"flex flex-col",children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"bg-muted h-4 w-3/4 rounded animate-pulse"}),(0,r.jsx)("div",{className:"bg-muted h-3 w-1/2 rounded animate-pulse"})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"bg-muted h-3 w-full rounded animate-pulse"}),(0,r.jsx)("div",{className:"bg-muted h-3 w-2/3 rounded animate-pulse"})]})})]},e))}):(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:E.map(e=>(0,r.jsxs)(o.Zp,{className:"flex flex-col",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(o.ZB,{className:"text-lg",children:e.name}),(0,r.jsx)("code",{className:"bg-muted rounded px-2 py-1 text-sm",children:e.courseCode})]}),(0,r.jsx)(c.E,{variant:"verified"===e.type?"default":"secondary",children:e.type})]}),(0,r.jsx)(o.BT,{children:e.description})]}),(0,r.jsxs)(o.Wu,{className:"flex-1 space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[e.progress,"%"]})]}),(0,r.jsx)(u.k,{value:e.progress,className:"h-2"}),(0,r.jsxs)("p",{className:"text-muted-foreground text-xs",children:[e.completedModules," of ",e.totalModules," modules completed"]})]}),"in_progress"===e.status&&e.nextChapter&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Next:"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:e.nextChapter})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-muted-foreground flex items-center space-x-2 text-sm",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["Due: ",e.dueDate?new Date(e.dueDate).toLocaleDateString():"No due date"]})]}),(0,r.jsx)(c.E,{variant:"completed"===e.status?"default":"outline",children:"completed"===e.status?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(f.A,{className:"mr-1 h-3 w-3"}),"Completed"]}):"In Progress"})]})]}),(0,r.jsx)("div",{className:"p-6 pt-0",children:(0,r.jsx)(b(),{href:`/dashboard/student/courses/${e.id}`,children:(0,r.jsx)(d.$,{className:"w-full",children:"completed"===e.status?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"View Certificate"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Continue Learning"]})})})})]},e.id))}),!C&&0===E.length&&(0,r.jsx)(o.Zp,{children:(0,r.jsx)(o.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"py-8 text-center",children:[(0,r.jsx)(v.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No enrolled courses"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:e?"No courses match your search.":"Get started by enrolling in a course."})]})})})]}),(0,r.jsxs)(p.av,{value:"available","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:[C?(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[1,2,3].map(e=>(0,r.jsxs)(o.Zp,{className:"flex flex-col",children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"bg-muted h-4 w-3/4 rounded animate-pulse"}),(0,r.jsx)("div",{className:"bg-muted h-3 w-1/2 rounded animate-pulse"})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"bg-muted h-3 w-full rounded animate-pulse"}),(0,r.jsx)("div",{className:"bg-muted h-3 w-2/3 rounded animate-pulse"})]})})]},e))}):(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:R.map(e=>(0,r.jsxs)(o.Zp,{className:"flex flex-col",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)(o.ZB,{className:"text-lg",children:e.name}),(0,r.jsx)("code",{className:"bg-muted rounded px-2 py-1 text-sm",children:e.courseCode})]}),(0,r.jsx)(c.E,{variant:"verified"===e.type?"default":"secondary",children:e.type})]}),(0,r.jsx)(o.BT,{children:e.description})]}),(0,r.jsx)(o.Wu,{className:"flex-1 space-y-4",children:(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Instructor:"}),(0,r.jsx)("span",{children:e.instructor})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Duration:"}),(0,r.jsx)("span",{children:e.duration})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Difficulty:"}),(0,r.jsx)(c.E,{variant:"outline",className:"text-xs",children:e.difficulty})]})]})}),(0,r.jsx)("div",{className:"p-6 pt-0",children:(0,r.jsxs)(d.$,{className:"w-full",variant:"outline",children:[(0,r.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Request Enrollment"]})})]},e.id))}),!C&&0===R.length&&(0,r.jsx)(o.Zp,{children:(0,r.jsx)(o.Wu,{className:"pt-6",children:(0,r.jsxs)("div",{className:"py-8 text-center",children:[(0,r.jsx)(v.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No available courses"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:e?"No courses match your search.":"Check back later for new courses."})]})})})]})]})]})}},86592:e=>{"use strict";e.exports=require("node:inspector")},86857:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91589:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var r=s(95500),a=s(56947),n=s(26052),i=s(13636),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["dashboard",{children:["student",{children:["courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,96532)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,97890)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/student/courses/page",pathname:"/dashboard/student/courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},96532:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),i=s(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof o?new Proxy(o,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student/courses",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):o;let c=void 0,u=void 0,p=void 0,m=r},97890:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),i=s(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof o?new Proxy(o,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student",componentType:"Layout",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):o;let c=void 0,u=void 0,p=void 0,m=r},98254:(e,t,s)=>{Promise.resolve().then(s.bind(s,97890))}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634],()=>s(91589));module.exports=r})();
//# sourceMappingURL=page.js.map