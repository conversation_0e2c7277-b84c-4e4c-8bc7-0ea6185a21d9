try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="591721cc-3755-4f01-97cf-f5ee1030d885",e._sentryDebugIdIdentifier="sentry-dbid-591721cc-3755-4f01-97cf-f5ee1030d885")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1008],{91008:(e,t,r)=>{r.r(t),r.d(t,{pascal:()=>s});var n=function(e){for(var t={},r=e.split(" "),n=0;n<r.length;++n)t[r[n]]=!0;return t}("absolute and array asm begin case const constructor destructor div do downto else end file for function goto if implementation in inherited inline interface label mod nil not object of operator or packed procedure program record reintroduce repeat self set shl shr string then to type unit until uses var while with xor as class dispinterface except exports finalization finally initialization inline is library on out packed property raise resourcestring threadvar try absolute abstract alias assembler bitpacked break cdecl continue cppdecl cvar default deprecated dynamic enumerator experimental export external far far16 forward generic helper implements index interrupt iocheck local message name near nodefault noreturn nostackframe oldfpccall otherwise overload override pascal platform private protected public published read register reintroduce result safecall saveregisters softfloat specialize static stdcall stored strict unaligned unimplemented varargs virtual write"),a={null:!0},i=/[+\-*&%=<>!?|\/]/;function o(e,t){for(var r,n=!1;r=e.next();){if(")"==r&&n){t.tokenize=null;break}n="*"==r}return"comment"}function l(e,t){for(var r;r=e.next();)if("}"==r){t.tokenize=null;break}return"comment"}let s={name:"pascal",startState:function(){return{tokenize:null}},token:function(e,t){if(e.eatSpace())return null;var r=(t.tokenize||function(e,t){var r,s=e.next();if("#"==s&&t.startOfLine)return e.skipToEnd(),"meta";if('"'==s||"'"==s){return r=s,t.tokenize=function(e,t){for(var n,a=!1,i=!1;null!=(n=e.next());){if(n==r&&!a){i=!0;break}a=!a&&"\\"==n}return(i||!a)&&(t.tokenize=null),"string"},t.tokenize(e,t)}if("("==s&&e.eat("*"))return t.tokenize=o,o(e,t);if("{"==s)return t.tokenize=l,l(e,t);if(/[\[\]\(\),;\:\.]/.test(s))return null;if(/\d/.test(s))return e.eatWhile(/[\w\.]/),"number";if("/"==s&&e.eat("/"))return e.skipToEnd(),"comment";if(i.test(s))return e.eatWhile(i),"operator";e.eatWhile(/[\w\$_]/);var u=e.current().toLowerCase();return n.propertyIsEnumerable(u)?"keyword":a.propertyIsEnumerable(u)?"atom":"variable"})(e,t);return r},languageData:{indentOnInput:/^\s*[{}]$/,commentTokens:{block:{open:"(*",close:"*)"}}}}}}]);